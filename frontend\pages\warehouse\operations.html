<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作记录 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/warehouse/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            仓库管理 - 操作记录
                        </span>
                    </li>
                </ol>
            </nav>

            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-6">操作记录</h2>

                <!-- 操作选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeTab = 'quick'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'quick'}">
                            <i class="fas fa-bolt mr-1"></i>
                            快速操作
                        </button>
                        <button
                            @click="activeTab = 'records'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-green-600 transition-colors duration-200"
                            :class="{'border-b-2 border-green-500 text-green-600': activeTab === 'records'}">
                            <i class="fas fa-list mr-1"></i>
                            操作记录
                        </button>
                        <button
                            @click="activeTab = 'batch'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-purple-600 transition-colors duration-200"
                            :class="{'border-b-2 border-purple-500 text-purple-600': activeTab === 'batch'}">
                            <i class="fas fa-layer-group mr-1"></i>
                            批量操作
                        </button>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div v-if="activeTab === 'quick'" class="space-y-6">
                    <!-- 操作类型选择 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectQuickOperation('inbound')"
                             :class="{'ring-2 ring-blue-500': quickOperation === 'inbound'}">
                            <div class="text-center">
                                <i class="fas fa-arrow-down text-4xl text-blue-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-blue-600">入库操作</h3>
                                <p class="text-sm text-gray-600 mt-2">物料和成品入库</p>
                            </div>
                        </div>

                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectQuickOperation('outbound')"
                             :class="{'ring-2 ring-orange-500': quickOperation === 'outbound'}">
                            <div class="text-center">
                                <i class="fas fa-arrow-up text-4xl text-orange-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-orange-600">出库操作</h3>
                                <p class="text-sm text-gray-600 mt-2">物料发料和成品出库</p>
                            </div>
                        </div>

                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectQuickOperation('return')"
                             :class="{'ring-2 ring-purple-500': quickOperation === 'return'}">
                            <div class="text-center">
                                <i class="fas fa-undo text-4xl text-purple-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-purple-600">退料返仓</h3>
                                <p class="text-sm text-gray-600 mt-2">物料退料和成品返仓</p>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作表单 -->
                    <div v-if="quickOperation" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas" :class="{
                                'fa-arrow-down text-blue-500': quickOperation === 'inbound',
                                'fa-arrow-up text-orange-500': quickOperation === 'outbound',
                                'fa-undo text-purple-500': quickOperation === 'return'
                            }" class="mr-2"></i>
                            {{ getOperationTitle(quickOperation) }}
                        </h3>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 二维码扫描区域 -->
                            <div>
                                <h4 class="font-semibold mb-3">扫描二维码</h4>
                                <div class="qr-scan-area mb-4" :class="{'active': isScanning}">
                                    <div class="text-center">
                                        <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-600 mb-2">扫描二维码或手动输入</p>
                                        <input type="text" v-model="qrCodeInput" @keyup.enter="validateQRCode" 
                                               placeholder="请扫描或输入二维码"
                                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <button @click="validateQRCode" class="mt-2 action-btn primary">
                                            <i class="fas fa-search"></i>
                                            验证二维码
                                        </button>
                                    </div>
                                </div>

                                <!-- 或者手动选择 -->
                                <div class="border-t pt-4">
                                    <h4 class="font-semibold mb-3">或手动选择</h4>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">类型</label>
                                            <select v-model="manualSelection.type" @change="loadItemsForSelection" 
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">请选择类型</option>
                                                <option value="material">物料</option>
                                                <option value="finished_product">成品</option>
                                            </select>
                                        </div>
                                        <div v-if="manualSelection.type">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">选择项目</label>
                                            <select v-model="manualSelection.itemId" @change="selectManualItem" 
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">请选择项目</option>
                                                <option v-for="item in availableItems" :key="item.id" :value="item.id">
                                                    {{ item.code }} - {{ item.name }} (库存: {{ item.current_stock || 0 }})
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作表单 -->
                            <div v-if="selectedItem">
                                <h4 class="font-semibold mb-3">操作信息</h4>
                                <div class="space-y-4">
                                    <!-- 项目信息显示 -->
                                    <div class="bg-gray-50 p-3 rounded-md">
                                        <div class="text-sm space-y-1">
                                            <div><span class="font-medium">编号：</span>{{ selectedItem.code }}</div>
                                            <div><span class="font-medium">名称：</span>{{ selectedItem.name }}</div>
                                            <div><span class="font-medium">当前库存：</span>
                                                <span :class="getStockTextClass(selectedItem)">
                                                    {{ selectedItem.current_stock || 0 }} {{ selectedItem.unit }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作数量 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            {{ getQuantityLabel(quickOperation) }} <span class="text-red-500">*</span>
                                        </label>
                                        <div class="quantity-controls">
                                            <button @click="decreaseQuantity" class="quantity-btn">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" v-model.number="operationForm.quantity" min="1" 
                                                   :max="quickOperation === 'outbound' ? selectedItem.current_stock : undefined"
                                                   class="quantity-input px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <button @click="increaseQuantity" class="quantity-btn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <p v-if="quickOperation === 'outbound' && operationForm.quantity > selectedItem.current_stock" 
                                           class="text-xs text-red-500 mt-1">
                                            数量不能超过当前库存
                                        </p>
                                    </div>

                                    <!-- 部门（出库时） -->
                                    <div v-if="quickOperation === 'outbound'">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            {{ selectedItem.type === 'material' ? '领料部门' : '收货部门' }}
                                        </label>
                                        <input type="text" v-model="operationForm.department" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="请输入部门名称">
                                    </div>

                                    <!-- 原因（退料时） -->
                                    <div v-if="quickOperation === 'return'">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            {{ selectedItem.type === 'material' ? '退料原因' : '返仓原因' }}
                                        </label>
                                        <select v-model="operationForm.reason" 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">请选择原因</option>
                                            <option value="quality_issue">质量问题</option>
                                            <option value="excess_material">多余物料</option>
                                            <option value="wrong_specification">规格错误</option>
                                            <option value="production_change">生产变更</option>
                                            <option value="other">其他原因</option>
                                        </select>
                                    </div>

                                    <!-- 备注 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                        <textarea v-model="operationForm.notes" rows="3" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                  placeholder="请输入备注信息"></textarea>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="action-buttons">
                                        <button @click="submitQuickOperation" :disabled="isSubmitting || !canSubmit" 
                                                class="action-btn" 
                                                :class="{
                                                    'success': quickOperation === 'inbound',
                                                    'warning': quickOperation === 'outbound',
                                                    'primary': quickOperation === 'return'
                                                }"
                                                :style="quickOperation === 'return' ? 'background: #8B5CF6;' : ''">
                                            <i class="fas fa-check"></i>
                                            {{ isSubmitting ? '处理中...' : getSubmitButtonText(quickOperation) }}
                                        </button>
                                        <button @click="clearQuickOperation" class="action-btn secondary">
                                            <i class="fas fa-times"></i>
                                            清除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作记录 -->
                <div v-if="activeTab === 'records'" class="space-y-6">
                    <!-- 搜索和筛选 -->
                    <div class="filter-section">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">操作类型</label>
                                <select v-model="recordsFilter.operationType" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">全部操作</option>
                                    <option value="inbound">入库</option>
                                    <option value="outbound">出库</option>
                                    <option value="return">退料/返仓</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">项目类型</label>
                                <select v-model="recordsFilter.itemType" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">全部类型</option>
                                    <option value="material">物料</option>
                                    <option value="finished_product">成品</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">日期范围</label>
                                <div class="flex space-x-2">
                                    <input type="date" v-model="recordsFilter.startDate" 
                                           class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <input type="date" v-model="recordsFilter.endDate" 
                                           class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">搜索</label>
                                <input type="text" v-model="recordsFilter.search" placeholder="搜索项目名称、编号..."
                                       class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div class="action-buttons">
                                <button @click="searchRecords" class="action-btn primary">
                                    <i class="fas fa-search"></i>
                                    搜索
                                </button>
                                <button @click="clearRecordsFilter" class="action-btn secondary">
                                    <i class="fas fa-eraser"></i>
                                    清除
                                </button>
                                <button @click="exportRecords" class="action-btn success">
                                    <i class="fas fa-download"></i>
                                    导出
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 记录列表 -->
                    <div v-if="isLoadingRecords" class="warehouse-loading">
                        <div class="warehouse-spinner"></div>
                        <span class="ml-2 text-gray-600">加载中...</span>
                    </div>

                    <div v-else-if="filteredRecords.length === 0" class="text-center text-gray-500 py-8">
                        没有找到符合条件的操作记录
                    </div>

                    <div v-else class="overflow-x-auto">
                        <table class="warehouse-table">
                            <thead>
                                <tr>
                                    <th>事务ID</th>
                                    <th>操作类型</th>
                                    <th>项目类型</th>
                                    <th>项目信息</th>
                                    <th>数量</th>
                                    <th>操作人</th>
                                    <th>部门/原因</th>
                                    <th>操作时间</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="record in paginatedRecords" :key="record.id">
                                    <td class="font-mono text-xs">{{ record.transaction_id }}</td>
                                    <td>
                                        <span class="operation-badge" :class="record.operation_type">
                                            {{ getOperationTypeText(record.operation_type) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="operation-badge" :class="record.item_type === 'material' ? 'inbound' : 'outbound'">
                                            {{ record.item_type === 'material' ? '物料' : '成品' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-sm">
                                            <div class="font-medium">{{ record.item_name }}</div>
                                            <div class="text-gray-500">{{ record.item_code }}</div>
                                        </div>
                                    </td>
                                    <td class="font-semibold">{{ record.quantity }} {{ record.item_unit }}</td>
                                    <td>{{ record.operator_name }}</td>
                                    <td class="text-sm">{{ record.department || record.reason || '-' }}</td>
                                    <td class="text-sm text-gray-500">{{ formatDateTime(record.created_at) }}</td>
                                    <td class="text-sm">{{ record.notes || '-' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div v-if="recordsTotalPages > 1" class="flex justify-between items-center mt-6">
                        <div class="text-sm text-gray-500">
                            共 {{ filteredRecords.length }} 条记录，第 {{ recordsCurrentPage }} / {{ recordsTotalPages }} 页
                        </div>
                        <div class="flex space-x-2">
                            <button @click="goToRecordsPage(recordsCurrentPage - 1)" :disabled="recordsCurrentPage <= 1" 
                                    class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                上一页
                            </button>
                            <button @click="goToRecordsPage(recordsCurrentPage + 1)" :disabled="recordsCurrentPage >= recordsTotalPages"
                                    class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 批量操作 -->
                <div v-if="activeTab === 'batch'" class="space-y-6">
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-layer-group text-purple-500 mr-2"></i>
                            批量操作
                        </h3>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">批量操作功能</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>批量操作功能正在开发中，敬请期待。该功能将支持：</p>
                                        <ul class="list-disc list-inside mt-2 space-y-1">
                                            <li>批量导入物料和成品信息</li>
                                            <li>批量入库和出库操作</li>
                                            <li>批量二维码生成和打印</li>
                                            <li>批量库存调整</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center py-12">
                            <i class="fas fa-tools text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-500 mb-2">功能开发中</h3>
                            <p class="text-gray-400">批量操作功能即将上线，请使用快速操作进行单项操作</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/warehouse/operations.js"></script>
</body>
</html>
