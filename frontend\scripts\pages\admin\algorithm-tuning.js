/**
 * 算法调优页面
 * 智能排程算法性能优化和参数调整
 */

import { getCurrentUser } from '../../api/auth.js';
import Sidebar from '../../../components/common/Sidebar.js';

const { createApp, ref, onMounted, nextTick } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    components: {
        Sidebar
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const tuning = ref(false);
        const saving = ref(false);
        const resetting = ref(false);
        const benchmarking = ref(false);
        
        // 调优数据
        const tuningStats = ref({});
        const tuningParams = ref({
            deliveryPrediction: {
                baseAccuracy: 0.85,
                riskFactorWeight: 0.3,
                historicalDataWeight: 0.4,
                currentStatusWeight: 0.3,
                confidenceThreshold: 0.8
            },
            resourceOptimization: {
                efficiencyWeight: 0.4,
                costWeight: 0.3,
                riskWeight: 0.3,
                loadBalanceThreshold: 0.8
            },
            planGeneration: {
                maxPlansCount: 5,
                diversityThreshold: 0.2,
                qualityThreshold: 0.7,
                timeoutMs: 5000
            }
        });
        
        const performanceHistory = ref([]);
        const lastTuningResult = ref(null);
        const benchmarkResult = ref(null);
        
        // 图表引用
        const performanceChart = ref(null);
        let performanceChartInstance = null;

        // 初始化
        onMounted(async () => {
            try {
                // 获取当前用户信息
                const user = await getCurrentUser();
                if (user) {
                    currentUser.value = user;
                    isAuthenticated.value = true;
                    await loadTuningData();
                } else {
                    // 未登录，跳转到登录页
                    window.location.href = '/login';
                    return;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                window.location.href = '/login';
                return;
            } finally {
                hideLoading();
            }
        });

        // 加载调优数据
        async function loadTuningData() {
            try {
                // 加载调优统计
                await loadTuningStats();
                
                // 加载调优参数
                await loadTuningParams();
                
                // 加载性能历史
                await loadPerformanceHistory();
                
                // 等待DOM更新后创建图表
                await nextTick();
                createPerformanceChart();
                
            } catch (error) {
                console.error('加载调优数据失败:', error);
                window.showNotification('加载调优数据失败', 'error');
            }
        }

        // 加载调优统计
        async function loadTuningStats() {
            try {
                const response = await axios.get('/tuning/stats');
                
                if (response.data.success) {
                    tuningStats.value = response.data.data.stats;
                }
            } catch (error) {
                console.error('加载调优统计失败:', error);
            }
        }

        // 加载调优参数
        async function loadTuningParams() {
            try {
                const response = await axios.get('/tuning/params');
                
                if (response.data.success) {
                    tuningParams.value = response.data.data.params;
                }
            } catch (error) {
                console.error('加载调优参数失败:', error);
            }
        }

        // 加载性能历史
        async function loadPerformanceHistory() {
            try {
                const response = await axios.get('/tuning/performance/history');
                
                if (response.data.success) {
                    performanceHistory.value = response.data.data.history;
                }
            } catch (error) {
                console.error('加载性能历史失败:', error);
            }
        }

        // 执行调优
        async function executeTuning() {
            if (tuning.value) return;
            
            tuning.value = true;
            
            try {
                const response = await axios.post('/tuning/tune');
                
                if (response.data.success) {
                    window.showNotification('算法调优完成', 'success');
                    lastTuningResult.value = response.data.data.tuningResults;
                    
                    // 重新加载数据
                    await loadTuningData();
                } else {
                    window.showNotification(response.data.message || '调优失败', 'error');
                }
            } catch (error) {
                console.error('执行调优失败:', error);
                window.showNotification('执行调优失败', 'error');
            } finally {
                tuning.value = false;
            }
        }

        // 保存参数
        async function saveParams() {
            if (saving.value) return;
            
            saving.value = true;
            
            try {
                const response = await axios.put('/tuning/params', {
                    params: tuningParams.value
                });
                
                if (response.data.success) {
                    window.showNotification('参数保存成功', 'success');
                } else {
                    window.showNotification(response.data.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存参数失败:', error);
                window.showNotification('保存参数失败', 'error');
            } finally {
                saving.value = false;
            }
        }

        // 重置参数
        async function resetParams() {
            if (resetting.value) return;
            
            if (!confirm('确定要重置所有参数为默认值吗？')) {
                return;
            }
            
            resetting.value = true;
            
            try {
                const response = await axios.post('/tuning/params/reset');
                
                if (response.data.success) {
                    tuningParams.value = response.data.data.params;
                    window.showNotification('参数重置成功', 'success');
                } else {
                    window.showNotification(response.data.message || '重置失败', 'error');
                }
            } catch (error) {
                console.error('重置参数失败:', error);
                window.showNotification('重置参数失败', 'error');
            } finally {
                resetting.value = false;
            }
        }

        // 运行基准测试
        async function runBenchmark() {
            if (benchmarking.value) return;
            
            benchmarking.value = true;
            
            try {
                const response = await axios.post('/tuning/benchmark', {
                    testCount: 10,
                    testType: 'full'
                });
                
                if (response.data.success) {
                    benchmarkResult.value = response.data.data.results;
                    window.showNotification('基准测试完成', 'success');
                } else {
                    window.showNotification(response.data.message || '测试失败', 'error');
                }
            } catch (error) {
                console.error('基准测试失败:', error);
                window.showNotification('基准测试失败', 'error');
            } finally {
                benchmarking.value = false;
            }
        }

        // 创建性能图表
        function createPerformanceChart() {
            if (!performanceChart.value || performanceHistory.value.length === 0) return;

            const ctx = performanceChart.value.getContext('2d');
            
            // 销毁现有图表
            if (performanceChartInstance) {
                performanceChartInstance.destroy();
            }

            // 准备数据
            const labels = performanceHistory.value.map((_, index) => `调优 ${index + 1}`);
            const responseTimeData = performanceHistory.value.map(h => h.responseTime);
            const accuracyData = performanceHistory.value.map(h => h.accuracy * 100);
            const cacheHitRateData = performanceHistory.value.map(h => h.cacheHitRate * 100);

            performanceChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '响应时间 (ms)',
                            data: responseTimeData,
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            yAxisID: 'y'
                        },
                        {
                            label: '准确性 (%)',
                            data: accuracyData,
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            yAxisID: 'y1'
                        },
                        {
                            label: '缓存命中率 (%)',
                            data: cacheHitRateData,
                            borderColor: 'rgba(245, 158, 11, 1)',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '调优历史'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '响应时间 (ms)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '百分比 (%)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // 工具函数
        function getSuccessRate() {
            if (!tuningStats.value.totalTunings || tuningStats.value.totalTunings === 0) {
                return 0;
            }
            return Math.round((tuningStats.value.successfulTunings / tuningStats.value.totalTunings) * 100);
        }

        function formatLastTuning() {
            if (!tuningStats.value.lastTuningTime) {
                return '无';
            }
            const date = new Date(tuningStats.value.lastTuningTime);
            const now = new Date();
            const diffMs = now - date;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffHours / 24);
            
            if (diffDays > 0) {
                return `${diffDays}天前`;
            } else if (diffHours > 0) {
                return `${diffHours}小时前`;
            } else {
                return '刚刚';
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        }

        return {
            currentUser,
            isAuthenticated,
            tuning,
            saving,
            resetting,
            benchmarking,
            tuningStats,
            tuningParams,
            performanceHistory,
            lastTuningResult,
            benchmarkResult,
            performanceChart,
            executeTuning,
            saveParams,
            resetParams,
            runBenchmark,
            getSuccessRate,
            formatLastTuning,
            formatDate
        };
    }
}).mount('#app');
