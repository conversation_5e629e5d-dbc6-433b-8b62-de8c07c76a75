/* 文件管理中心页面专用样式 - File Management Upload CSS */

/* 页面标题优化 */
.page-title {
    color: #1f2937;
    font-weight: 700;
}

/* 统计信息卡片 */
.stats-card {
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 上传选项卡片优化 */
.upload-card {
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.upload-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--card-color);
}

/* 卡片图标容器 */
.card-icon {
    transition: transform 0.2s ease;
}

.upload-card:hover .card-icon {
    transform: scale(1.05);
}

/* 卡片颜色变量 */
.upload-card.certification {
    --card-color: #3b82f6;
}

.upload-card.sample {
    --card-color: #10b981;
}

.upload-card.production {
    --card-color: #8b5cf6;
}

/* 按钮优化 */
.upload-btn {
    transition: all 0.2s ease;
}

.upload-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 帮助信息区域 */
.help-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

/* 快速操作区域 */
.quick-actions {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem;
}

/* 文件格式提示 */
.format-hint {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #eff6ff;
    border: 1px solid #dbeafe;
    border-radius: 2rem;
    font-size: 0.875rem;
    color: #1e40af;
    transition: all 0.2s ease;
}

.format-hint:hover {
    background: #dbeafe;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .upload-card {
        margin-bottom: 1rem;
    }

    .upload-card:hover {
        transform: translateY(-1px);
    }
}

/* 进度指示器 */
.progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #3b82f6;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 9999;
}

.progress-indicator.active {
    animation: progress 2s ease-in-out;
}

@keyframes progress {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(-10%); }
    100% { transform: translateX(0); }
}
