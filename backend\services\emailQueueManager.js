/**
 * 邮件队列管理器
 * 提供异步邮件发送队列，确保邮件发送不影响主业务流程
 */

const logger = require('../utils/logger');
const emailService = require('./emailService');

class EmailQueueManager {
    constructor() {
        this.queue = [];
        this.processing = false;
        this.maxRetries = 5;
        this.retryDelay = 3000; // 3秒
        this.maxConcurrent = 3; // 最大并发发送数
        this.currentProcessing = 0;
        
        // 启动队列处理器
        this.startProcessor();
        
        logger.info('邮件队列管理器初始化完成', {
            maxRetries: this.maxRetries,
            retryDelay: this.retryDelay,
            maxConcurrent: this.maxConcurrent
        });
    }

    /**
     * 添加邮件到队列
     * @param {string} type - 邮件类型
     * @param {Object} data - 邮件数据
     * @param {Object} options - 选项
     */
    addToQueue(type, data, options = {}) {
        const emailTask = {
            id: this.generateTaskId(),
            type,
            data,
            options,
            retries: 0,
            createdAt: new Date(),
            status: 'pending'
        };

        this.queue.push(emailTask);
        
        logger.debug('邮件任务已添加到队列', {
            taskId: emailTask.id,
            type: emailTask.type,
            queueLength: this.queue.length
        });

        // 立即尝试处理队列
        this.processQueue();
    }

    /**
     * 启动队列处理器
     */
    startProcessor() {
        // 每5秒检查一次队列
        setInterval(() => {
            this.processQueue();
        }, 5000);
    }

    /**
     * 处理队列
     */
    async processQueue() {
        if (this.currentProcessing >= this.maxConcurrent) {
            return; // 已达到最大并发数
        }

        const pendingTasks = this.queue.filter(task => task.status === 'pending');
        if (pendingTasks.length === 0) {
            return; // 没有待处理任务
        }

        // 处理多个任务（不超过最大并发数）
        const tasksToProcess = pendingTasks.slice(0, this.maxConcurrent - this.currentProcessing);
        
        for (const task of tasksToProcess) {
            this.processTask(task);
        }
    }

    /**
     * 处理单个邮件任务
     * @param {Object} task - 邮件任务
     */
    async processTask(task) {
        if (task.status !== 'pending') {
            return;
        }

        this.currentProcessing++;
        task.status = 'processing';

        try {
            logger.debug('开始处理邮件任务', {
                taskId: task.id,
                type: task.type,
                retries: task.retries
            });

            await this.sendEmail(task);
            
            // 发送成功，从队列中移除
            this.removeFromQueue(task.id);
            
            logger.info('邮件发送成功', {
                taskId: task.id,
                type: task.type,
                retries: task.retries
            });

        } catch (error) {
            logger.error('邮件发送失败', {
                taskId: task.id,
                type: task.type,
                retries: task.retries,
                error: error.message
            });

            // 重试逻辑
            if (task.retries < this.maxRetries) {
                task.retries++;
                task.status = 'pending';
                task.nextRetryAt = new Date(Date.now() + this.retryDelay * Math.pow(2, task.retries)); // 指数退避
                
                logger.info('邮件任务将重试', {
                    taskId: task.id,
                    type: task.type,
                    retries: task.retries,
                    nextRetryAt: task.nextRetryAt
                });
            } else {
                // 超过最大重试次数，标记为失败
                task.status = 'failed';
                task.failedAt = new Date();
                
                logger.error('邮件任务失败，超过最大重试次数', {
                    taskId: task.id,
                    type: task.type,
                    retries: task.retries,
                    maxRetries: this.maxRetries
                });
            }
        } finally {
            this.currentProcessing--;
        }
    }

    /**
     * 发送邮件
     * @param {Object} task - 邮件任务
     */
    async sendEmail(task) {
        const { type, data } = task;

        switch (type) {
            case 'sample_form':
                return await emailService.sendSampleFormNotification(data);
            
            case 'file_management':
                return await emailService.sendFileManagementNotification(data.fileRecord, data.uploader, data.userIds);
            
            case 'quality_report':
                return await emailService.sendQualityReportNotification(data.report, data.uploader, data.userIds);
            
            case 'application_submitted':
                return await emailService.sendApplicationSubmittedNotification(data.application);
            
            case 'application_approved':
                return await emailService.sendApprovalStatusNotification(data.application, data.approver, data.comment);
            
            case 'application_rejected':
                return await emailService.sendApplicationRejectedNotification(data.application, data.approver, data.comment);
            
            case 'high_amount':
                return await emailService.sendHighAmountNotification(data.application);
            
            case 'all_confirmed':
                return await emailService.sendAllConfirmedNotification(data.uploaderInfo);
            
            default:
                throw new Error(`未知的邮件类型: ${type}`);
        }
    }

    /**
     * 从队列中移除任务
     * @param {string} taskId - 任务ID
     */
    removeFromQueue(taskId) {
        const index = this.queue.findIndex(task => task.id === taskId);
        if (index !== -1) {
            this.queue.splice(index, 1);
        }
    }

    /**
     * 生成任务ID
     * @returns {string} 任务ID
     */
    generateTaskId() {
        return `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取队列状态
     * @returns {Object} 队列状态
     */
    getQueueStatus() {
        const pending = this.queue.filter(task => task.status === 'pending').length;
        const processing = this.queue.filter(task => task.status === 'processing').length;
        const failed = this.queue.filter(task => task.status === 'failed').length;

        return {
            total: this.queue.length,
            pending,
            processing,
            failed,
            currentProcessing: this.currentProcessing
        };
    }

    /**
     * 清理失败的任务（超过24小时的）
     */
    cleanupFailedTasks() {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const beforeCount = this.queue.length;
        
        this.queue = this.queue.filter(task => {
            if (task.status === 'failed' && task.failedAt && task.failedAt < oneDayAgo) {
                return false; // 移除
            }
            return true; // 保留
        });

        const removedCount = beforeCount - this.queue.length;
        if (removedCount > 0) {
            logger.info('清理失败的邮件任务', {
                removedCount,
                remainingCount: this.queue.length
            });
        }
    }

    /**
     * 重试所有失败的任务
     */
    retryFailedTasks() {
        const failedTasks = this.queue.filter(task => task.status === 'failed');
        
        failedTasks.forEach(task => {
            task.status = 'pending';
            task.retries = 0;
            delete task.failedAt;
            delete task.nextRetryAt;
        });

        if (failedTasks.length > 0) {
            logger.info('重试失败的邮件任务', {
                retryCount: failedTasks.length
            });
            
            this.processQueue();
        }
    }
}

// 创建全局邮件队列管理器实例
const emailQueueManager = new EmailQueueManager();

// 定期清理失败任务（每小时执行一次）
setInterval(() => {
    emailQueueManager.cleanupFailedTasks();
}, 60 * 60 * 1000);

module.exports = emailQueueManager;
