/**
 * PostgreSQL设备健康度数据访问层
 * 替换SQLite的健康度评估服务，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class PostgresHealthRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL健康度数据访问层初始化完成');
        }
    }

    /**
     * 获取设备最新健康度记录
     */
    async findLatestHealth(equipmentId) {
        try {
            const health = await this.findOne(`
                SELECT * FROM equipment_health
                WHERE equipment_id = $1
                ORDER BY COALESCE(assessment_date, recorded_at, created_at) DESC
                LIMIT 1
            `, [equipmentId]);
            return health ? this.transformHealth(health) : null;
        } catch (error) {
            logger.error(`获取设备最新健康度失败 (${equipmentId}):`, error);
            throw error;
        }
    }

    /**
     * 获取设备健康度历史
     */
    async findHealthHistory(equipmentId, limit = 30) {
        try {
            // 首先尝试从 equipment_health_history 表获取历史数据
            let history = await this.findMany(`
                SELECT
                    id,
                    equipment_id,
                    total_score as health_score,
                    health_level,
                    age_score,
                    repair_frequency_score,
                    fault_severity_score,
                    maintenance_score,
                    assessment_date as recorded_at,
                    assessor,
                    calculation_details,
                    created_at
                FROM equipment_health_history
                WHERE equipment_id = $1
                ORDER BY assessment_date DESC
                LIMIT $2
            `, [equipmentId, limit]);

            // 如果历史表没有数据，从当前健康度表获取
            if (history.length === 0) {
                history = await this.findMany(`
                    SELECT
                        id,
                        equipment_id,
                        health_score,
                        age_score,
                        repair_frequency_score,
                        fault_severity_score,
                        maintenance_score,
                        assessment_date as recorded_at,
                        assessor,
                        temperature,
                        vibration,
                        pressure,
                        runtime_hours,
                        maintenance_status
                    FROM equipment_health
                    WHERE equipment_id = $1
                    ORDER BY recorded_at DESC
                    LIMIT $2
                `, [equipmentId, limit]);
            }

            const transformedHistory = history.map(record => this.transformHealthHistory(record));

            // 返回符合前端期望的数据结构
            return {
                equipmentId,
                history: transformedHistory,
                trends: null
            };
        } catch (error) {
            logger.error(`获取设备健康度历史失败 (${equipmentId}):`, error);
            throw error;
        }
    }

    /**
     * 获取所有设备健康度 - 基于设备真实数据计算
     */
    async findAllEquipmentHealth() {
        try {
            const equipmentData = await this.findMany(`
                SELECT
                    e.id,
                    e.code,
                    e.name,
                    e.area,
                    e.status,
                    e.manufacture_date,
                    e.created_at,
                    CASE
                        WHEN e.status = 'fault' THEN 30
                        WHEN e.status = 'maintenance' THEN 50
                        WHEN e.status = 'inactive' THEN 40
                        ELSE GREATEST(60, 100 - EXTRACT(YEAR FROM AGE(CURRENT_DATE, COALESCE(e.manufacture_date, CURRENT_DATE - INTERVAL '5 years'))) * 3)
                    END as calculated_health_score
                FROM equipment e
                ORDER BY e.code
            `);

            return equipmentData.map(equipment => ({
                id: `health_${equipment.id}`,
                equipment_id: equipment.id,
                equipment_name: equipment.name,
                equipment_code: equipment.code,
                area: equipment.area,
                health_score: parseFloat(equipment.calculated_health_score),
                temperature: this.calculateFixedTemperature(equipment), // 基于设备ID的固定温度
                vibration: this.calculateFixedVibration(equipment), // 基于设备ID的固定振动
                pressure: this.calculateFixedPressure(equipment), // 基于设备ID的固定压力
                runtime_hours: this.calculateRuntimeHours(equipment.manufacture_date),
                maintenance_status: equipment.status === 'maintenance' ? 'maintenance' : 'normal',
                last_maintenance: null,
                next_maintenance: null,
                alerts: this.calculateAlerts(equipment),
                recorded_at: equipment.created_at || new Date().toISOString()
            }));
        } catch (error) {
            logger.error('获取所有设备健康度失败:', error);
            throw error;
        }
    }

    /**
     * 计算设备运行小时数
     */
    calculateRuntimeHours(manufactureDate) {
        if (!manufactureDate) return 8760; // 默认一年运行时间

        const now = new Date();
        const manufacture = new Date(manufactureDate);
        const yearsDiff = (now - manufacture) / (1000 * 60 * 60 * 24 * 365);

        // 假设设备每年运行8760小时（24*365）
        return Math.round(yearsDiff * 8760);
    }

    /**
     * 基于设备ID计算固定温度
     */
    calculateFixedTemperature(equipment) {
        const idHash = this.generateHashFromId(equipment.id);
        let baseTemp = 30; // 基础温度

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            baseTemp += 20;
        } else if (equipment.status === 'maintenance') {
            baseTemp -= 5;
        }

        // 基于ID生成固定调整值
        const adjustment = (idHash % 21) - 10; // -10到+10的调整
        return Math.round((baseTemp + adjustment) * 10) / 10;
    }

    /**
     * 基于设备ID计算固定振动
     */
    calculateFixedVibration(equipment) {
        const idHash = this.generateHashFromId(equipment.id);
        let baseVibration = 2; // 基础振动

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            baseVibration += 3;
        } else if (equipment.status === 'maintenance') {
            baseVibration = 0.5;
        }

        // 基于ID生成固定调整值
        const adjustment = (idHash % 11) / 10; // 0-1的调整
        return Math.round((baseVibration + adjustment) * 10) / 10;
    }

    /**
     * 基于设备ID计算固定压力
     */
    calculateFixedPressure(equipment) {
        const idHash = this.generateHashFromId(equipment.id);
        let basePressure = 2.5; // 基础压力

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            basePressure += 1.5;
        } else if (equipment.status === 'maintenance') {
            basePressure -= 0.5;
        }

        // 基于ID生成固定调整值
        const adjustment = (idHash % 21) / 10 - 1; // -1到+1的调整
        return Math.round((basePressure + adjustment) * 10) / 10;
    }

    /**
     * 基于设备ID生成固定哈希值
     */
    generateHashFromId(id) {
        let hash = 0;
        for (let i = 0; i < id.length; i++) {
            const char = id.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }

    /**
     * 基于设备状态计算警告
     */
    calculateAlerts(equipment) {
        const alerts = [];

        if (equipment.status === 'fault') {
            alerts.push('设备故障');
        }
        if (equipment.status === 'maintenance') {
            alerts.push('维护中');
        }

        // 基于设备年龄的警告
        if (equipment.manufacture_date) {
            const age = new Date().getFullYear() - new Date(equipment.manufacture_date).getFullYear();
            if (age > 10) {
                alerts.push('设备老化');
            }
        }

        return alerts;
    }

    /**
     * 创建健康度记录
     */
    async createHealth(healthData) {
        try {
            const id = uuidv4();
            const now = new Date().toISOString();
            
            await this.query(`
                INSERT INTO equipment_health (
                    id, equipment_id, health_score, total_score, health_level, age_score, repair_frequency_score,
                    fault_severity_score, maintenance_score, assessment_date, assessor,
                    calculation_details, recommendations, next_maintenance_date, failure_probability,
                    temperature, vibration, pressure, runtime_hours, maintenance_status,
                    last_maintenance, next_maintenance, alerts, recorded_at, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26)
            `, [
                id,
                healthData.equipment_id,
                healthData.health_score || healthData.total_score,
                healthData.total_score || healthData.health_score,
                healthData.health_level || this.getHealthLevel(healthData.total_score || healthData.health_score || 0),
                healthData.age_score || null,
                healthData.repair_frequency_score || null,
                healthData.fault_severity_score || null,
                healthData.maintenance_score || null,
                healthData.assessment_date || now,
                healthData.assessor || 'system',
                healthData.calculation_details || null,
                healthData.recommendations || null,
                healthData.next_maintenance_date || null,
                healthData.failure_probability || null,
                healthData.temperature || null,
                healthData.vibration || null,
                healthData.pressure || null,
                healthData.runtime_hours || null,
                healthData.maintenance_status || 'normal',
                healthData.last_maintenance || null,
                healthData.next_maintenance || null,
                JSON.stringify(healthData.alerts || []),
                now,
                now,
                now
            ]);

            return { id, ...healthData, recorded_at: now };
        } catch (error) {
            logger.error('创建健康度记录失败:', error);
            throw error;
        }
    }

    /**
     * 更新健康度记录
     */
    async updateHealth(id, healthData) {
        try {
            await this.query(`
                UPDATE equipment_health
                SET health_score = $2, total_score = $3, health_level = $4,
                    age_score = $5, repair_frequency_score = $6, fault_severity_score = $7, maintenance_score = $8,
                    assessment_date = $9, assessor = $10, calculation_details = $11, recommendations = $12,
                    next_maintenance_date = $13, failure_probability = $14,
                    temperature = $15, vibration = $16, pressure = $17, runtime_hours = $18,
                    maintenance_status = $19, last_maintenance = $20, next_maintenance = $21, alerts = $22,
                    updated_at = $23
                WHERE id = $1
            `, [
                id,
                healthData.health_score || healthData.total_score,
                healthData.total_score || healthData.health_score,
                healthData.health_level || this.getHealthLevel(healthData.total_score || healthData.health_score || 0),
                healthData.age_score || null,
                healthData.repair_frequency_score || null,
                healthData.fault_severity_score || null,
                healthData.maintenance_score || null,
                healthData.assessment_date || new Date().toISOString(),
                healthData.assessor || 'system',
                healthData.calculation_details || null,
                healthData.recommendations || null,
                healthData.next_maintenance_date || null,
                healthData.failure_probability || null,
                healthData.temperature || null,
                healthData.vibration || null,
                healthData.pressure || null,
                healthData.runtime_hours || null,
                healthData.maintenance_status || 'normal',
                healthData.last_maintenance || null,
                healthData.next_maintenance || null,
                JSON.stringify(healthData.alerts || []),
                new Date().toISOString()
            ]);

            return await this.findOne('SELECT * FROM equipment_health WHERE id = $1', [id]);
        } catch (error) {
            logger.error(`更新健康度记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除健康度记录
     */
    async deleteHealth(id) {
        try {
            await this.query('DELETE FROM equipment_health WHERE id = $1', [id]);
            return true;
        } catch (error) {
            logger.error(`删除健康度记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 获取健康度统计 - 基于设备真实数据计算
     */
    async getHealthStatistics(filters = {}) {
        try {
            let whereClause = '';
            let params = [];
            let paramIndex = 1;

            if (filters.area) {
                whereClause += ` AND e.area = $${paramIndex}`;
                params.push(filters.area);
                paramIndex++;
            }

            // 基于设备真实数据计算健康度统计
            const query = `
                SELECT
                    e.id,
                    e.code,
                    e.name,
                    e.area,
                    e.status,
                    e.manufacture_date,
                    CASE
                        WHEN e.status = 'fault' THEN 30
                        WHEN e.status = 'maintenance' THEN 50
                        WHEN e.status = 'inactive' THEN 40
                        ELSE GREATEST(60, 100 - EXTRACT(YEAR FROM AGE(CURRENT_DATE, COALESCE(e.manufacture_date, CURRENT_DATE - INTERVAL '5 years'))) * 3)
                    END as calculated_health_score
                FROM equipment e
                WHERE 1=1 ${whereClause}
                ORDER BY e.code
            `;

            const equipmentData = await this.findMany(query, params);

            // 计算统计数据
            let totalEquipment = equipmentData.length;
            let totalScore = 0;
            let excellent = 0, good = 0, average = 0, poor = 0, dangerous = 0;
            let warningEquipment = [];

            equipmentData.forEach(equipment => {
                const score = parseFloat(equipment.calculated_health_score);
                totalScore += score;

                // 分类统计
                if (score >= 90) excellent++;
                else if (score >= 80) good++;
                else if (score >= 70) average++;
                else if (score >= 60) poor++;
                else dangerous++;

                // 收集需要关注的设备
                if (score < 70) {
                    // 确定健康度等级
                    let level = '';
                    if (score >= 90) level = '优秀';
                    else if (score >= 80) level = '良好';
                    else if (score >= 70) level = '一般';
                    else if (score >= 60) level = '较差';
                    else level = '危险';

                    warningEquipment.push({
                        id: equipment.id,
                        equipmentId: equipment.id,
                        equipmentName: equipment.name,
                        equipmentCode: equipment.code,
                        area: equipment.area,
                        totalScore: score,
                        level: level,
                        status: equipment.status,
                        manufactureDate: equipment.manufacture_date,
                        urgentRecommendations: score < 60 ? 3 : score < 70 ? 2 : 1, // 紧急建议数量
                        alerts: score < 60 ? ['低健康度警告'] : score < 70 ? ['需要关注'] : []
                    });
                }
            });

            return {
                overview: {
                    totalEquipment: totalEquipment,
                    averageHealth: totalEquipment > 0 ? Math.round(totalScore / totalEquipment) : 0,
                    lastUpdated: new Date().toISOString()
                },
                distribution: {
                    excellent: excellent,
                    good: good,
                    average: average,
                    poor: poor,
                    dangerous: dangerous
                },
                warningEquipment: warningEquipment.sort((a, b) => a.health_score - b.health_score)
            };
        } catch (error) {
            logger.error('获取健康度统计失败:', error);
            throw error;
        }
    }

    /**
     * 转换健康度数据格式
     */
    transformHealth(health) {
        if (!health) return null;

        return {
            id: health.id,
            equipment_id: health.equipment_id,
            total_score: parseFloat(health.total_score) || parseFloat(health.health_score) || 0,
            health_score: parseFloat(health.total_score) || parseFloat(health.health_score) || 0,
            health_level: health.health_level || this.getHealthLevel(parseFloat(health.total_score) || parseFloat(health.health_score) || 0),
            // 使用新添加的四维度字段
            age_score: parseFloat(health.age_score) || null,
            repair_frequency_score: parseFloat(health.repair_frequency_score) || null,
            fault_severity_score: parseFloat(health.fault_severity_score) || null,
            maintenance_score: parseFloat(health.maintenance_score) || null,
            assessment_date: health.assessment_date || health.recorded_at || health.created_at,
            assessor: health.assessor || 'system',
            calculation_details: health.calculation_details || null,
            recommendations: health.recommendations || null,
            next_maintenance_date: health.next_maintenance_date || null,
            failure_probability: parseFloat(health.failure_probability) || null,
            temperature: health.temperature ? parseFloat(health.temperature) : null,
            vibration: health.vibration ? parseFloat(health.vibration) : null,
            pressure: health.pressure ? parseFloat(health.pressure) : null,
            runtime_hours: health.runtime_hours ? parseFloat(health.runtime_hours) : null,
            maintenance_status: health.maintenance_status,
            last_maintenance: health.last_maintenance,
            next_maintenance: health.next_maintenance,
            alerts: Array.isArray(health.alerts) ? health.alerts : (health.alerts ? JSON.parse(health.alerts) : []),
            recorded_at: health.recorded_at
        };
    }

    /**
     * 转换带设备信息的健康度数据格式
     */
    transformHealthWithEquipment(record) {
        if (!record) return null;

        return {
            ...this.transformHealth(record),
            equipment_name: record.equipment_name,
            equipment_code: record.equipment_code,
            area: record.area
        };
    }

    /**
     * 转换健康度历史数据格式
     */
    transformHealthHistory(record) {
        if (!record) return null;

        return {
            id: record.id,
            equipmentId: record.equipment_id,
            total_score: parseFloat(record.health_score) || parseFloat(record.total_score) || 0,
            assessment_date: record.recorded_at || record.assessment_date,
            health_level: record.health_level || this.getHealthLevel(parseFloat(record.health_score) || parseFloat(record.total_score) || 0),
            details: {
                age: {
                    score: parseInt(record.age_score) || 0,
                    weight: 20
                },
                repairFrequency: {
                    score: parseInt(record.repair_frequency_score) || 0,
                    weight: 30
                },
                faultSeverity: {
                    score: parseInt(record.fault_severity_score) || 0,
                    weight: 30
                },
                maintenance: {
                    score: parseInt(record.maintenance_score) || 0,
                    weight: 20
                }
            },
            assessor: record.assessor || 'system',
            calculation_details: record.calculation_details || null,
            // 如果是从 equipment_health 表来的数据，包含传感器数据
            temperature: parseFloat(record.temperature) || null,
            vibration: parseFloat(record.vibration) || null,
            pressure: parseFloat(record.pressure) || null,
            runtime_hours: parseFloat(record.runtime_hours) || null,
            maintenance_status: record.maintenance_status || null
        };
    }

    /**
     * 根据分数获取健康度等级
     */
    getHealthLevel(score) {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '一般';
        if (score >= 60) return '较差';
        return '危险';
    }
}

module.exports = PostgresHealthRepository;
