/**
 * PostgreSQL连接池管理器
 * 替换SQLite连接池，提供PostgreSQL连接管理功能
 */

const { Pool } = require('pg');
const logger = require('./logger');

class PostgreSQLConnectionPool {
    constructor(options = {}) {
        this.config = {
            user: options.user || process.env.POSTGRES_USER || 'postgres',
            host: options.host || process.env.POSTGRES_HOST || 'localhost',
            database: options.database || process.env.POSTGRES_DATABASE || 'makrite_system',
            password: options.password || process.env.POSTGRES_PASSWORD || 'postgres',
            port: options.port || parseInt(process.env.POSTGRES_PORT) || 5432,
            max: options.max || parseInt(process.env.POSTGRES_POOL_MAX) || 20, // 最大连接数
            min: options.min || parseInt(process.env.POSTGRES_POOL_MIN) || 2,  // 最小连接数
            idleTimeoutMillis: options.idleTimeoutMillis || parseInt(process.env.POSTGRES_POOL_IDLE_TIMEOUT) || 30000, // 空闲超时
            connectionTimeoutMillis: options.connectionTimeoutMillis || parseInt(process.env.POSTGRES_POOL_CONNECTION_TIMEOUT) || 2000, // 连接超时
            acquireTimeoutMillis: options.acquireTimeoutMillis || 30000, // 获取连接超时
        };
        
        this.pool = null;
        this.stats = {
            created: 0,
            acquired: 0,
            released: 0,
            timeouts: 0,
            errors: 0
        };

        this.initialize();
    }

    /**
     * 初始化连接池
     */
    initialize() {
        try {
            this.pool = new Pool(this.config);
            
            // 监听连接池事件
            this.pool.on('connect', (client) => {
                this.stats.created++;
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.debug('PostgreSQL连接创建', { 
                        totalCount: this.pool.totalCount,
                        idleCount: this.pool.idleCount 
                    });
                }
            });

            this.pool.on('acquire', (client) => {
                this.stats.acquired++;
            });

            this.pool.on('release', (client) => {
                this.stats.released++;
            });

            this.pool.on('error', (err, client) => {
                this.stats.errors++;
                logger.error('PostgreSQL连接池错误:', err);
            });

            logger.info('PostgreSQL连接池初始化成功');
        } catch (error) {
            logger.error('PostgreSQL连接池初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取连接
     */
    async getConnection() {
        try {
            const client = await this.pool.connect();
            return client;
        } catch (error) {
            this.stats.errors++;
            logger.error('获取PostgreSQL连接失败:', error);
            throw error;
        }
    }

    /**
     * 执行查询
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     */
    async query(text, params = []) {
        const client = await this.getConnection();
        try {
            const start = Date.now();
            const result = await client.query(text, params);
            const duration = Date.now() - start;
            
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.debug('执行查询:', { 
                    text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                    duration, 
                    rows: result.rowCount 
                });
            }
            
            return result;
        } catch (error) {
            logger.error('查询执行失败:', { text, params, error: error.message });
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 执行事务
     * @param {Function} callback 事务回调函数
     */
    async transaction(callback) {
        const client = await this.getConnection();
        
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            logger.error('事务执行失败:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 批量执行查询
     * @param {Array} queries 查询数组，每个元素包含 {text, params}
     */
    async batchQuery(queries) {
        return await this.transaction(async (client) => {
            const results = [];
            for (const query of queries) {
                const result = await client.query(query.text, query.params || []);
                results.push(result);
            }
            return results;
        });
    }

    /**
     * 获取连接池状态
     */
    getStatus() {
        if (!this.pool) {
            return null;
        }
        
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount,
            stats: this.stats,
            config: {
                max: this.config.max,
                min: this.config.min,
                database: this.config.database,
                host: this.config.host,
                port: this.config.port
            }
        };
    }

    /**
     * 获取连接池统计信息
     */
    getStats() {
        return {
            ...this.stats,
            pool: {
                totalCount: this.pool?.totalCount || 0,
                idleCount: this.pool?.idleCount || 0,
                waitingCount: this.pool?.waitingCount || 0
            }
        };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            created: 0,
            acquired: 0,
            released: 0,
            timeouts: 0,
            errors: 0
        };
        logger.info('PostgreSQL连接池统计信息已重置');
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            logger.info('PostgreSQL连接池已关闭');
        }
    }

    /**
     * 测试连接
     */
    async testConnection() {
        try {
            const result = await this.query('SELECT NOW() as current_time, version() as version');
            logger.info('PostgreSQL连接测试成功:', result.rows[0]);
            return true;
        } catch (error) {
            logger.error('PostgreSQL连接测试失败:', error);
            return false;
        }
    }
}

// 创建全局连接池实例
const postgresConnectionPool = new PostgreSQLConnectionPool();

module.exports = {
    PostgreSQLConnectionPool,
    postgresConnectionPool
};
