<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样品单 - 管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/file-management/sample-form.css">
</head>

<body class="bg-gray-100 min-h-screen">
    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto"></div>
            <p class="mt-4 text-gray-600">正在加载样品单页面...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="flex min-h-screen" style="display: none;">
        <!-- 侧边栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 md:hidden">
                <div class="flex items-center justify-between px-4 py-3">
                    <button @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-600 hover:text-gray-900 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">样品单</h1>
                    <div class="w-6"></div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-4 md:p-6 lg:p-8">
                <div class="max-w-6xl mx-auto">
                    <!-- 页面标题 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                                    <a href="/file-upload" class="hover:text-blue-600">文件上传</a>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span class="text-gray-900">样品单</span>
                                </nav>
                                <h1 class="text-2xl font-bold text-gray-800">样品单</h1>
                                <p class="text-gray-600 mt-2">填写样品单相关信息，便于样品管理和追踪</p>
                            </div>
                            <a href="/file-upload"
                                class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                返回上传中心
                            </a>
                        </div>
                    </div>

                    <!-- 样品单表单 -->
                    <sample-form></sample-form>
                </div>
            </main>
        </div>
    </div>

    <!-- 遮罩层（移动端侧边栏） -->
    <div v-if="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden">
    </div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/file-management/sample.js"></script>
</body>

</html>