/**
 * PostgreSQL质量管理数据访问层
 * 替换SQLite的qualityRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresQualityRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL质量管理数据访问层初始化完成');
        }
    }

    /**
     * 获取所有质量报告
     */
    async findAllReports() {
        try {
            const reports = await this.findMany(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department,
                    (SELECT COUNT(*) FROM quality_report_files qrf WHERE qrf.report_id = qr.id) as file_count
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                ORDER BY qr.created_at DESC
            `);
            return reports.map(report => this.transformReport(report));
        } catch (error) {
            logger.error('获取所有质量报告失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找质量报告
     */
    async findReportById(id) {
        try {
            const report = await this.findOne(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                WHERE qr.id = $1
            `, [id]);
            return report ? this.transformReport(report) : null;
        } catch (error) {
            logger.error(`根据ID查找质量报告失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据报告编号查找质量报告
     */
    async findReportByNumber(reportNumber) {
        try {
            const report = await this.findOne(`
                SELECT 
                    qr.*,
                    u.username as uploader_name,
                    u.department as uploader_department
                FROM quality_reports qr
                LEFT JOIN users u ON qr.uploaded_by = u.id
                WHERE qr.report_number = $1
            `, [reportNumber]);
            return report ? this.transformReport(report) : null;
        } catch (error) {
            logger.error(`根据报告编号查找质量报告失败 (${reportNumber}):`, error);
            throw error;
        }
    }

    /**
     * 创建质量报告
     */
    async createReport(reportData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO quality_reports (
                    id, report_number, title, description, test_type, test_date,
                    sample_info, test_method, test_standard, test_result, conclusion,
                    uploaded_by, uploaded_at, status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                RETURNING *
            `, [
                reportData.id,
                reportData.report_number,
                reportData.title,
                reportData.description,
                reportData.test_type,
                reportData.test_date,
                reportData.sample_info,
                reportData.test_method,
                reportData.test_standard,
                reportData.test_result,
                reportData.conclusion,
                reportData.uploaded_by,
                reportData.uploaded_at || now,
                reportData.status || 'draft',
                now,
                now
            ]);

            return this.transformReport(result.rows[0]);
        } catch (error) {
            logger.error('创建质量报告失败:', error);
            throw error;
        }
    }

    /**
     * 更新质量报告
     */
    async updateReport(id, reportData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE quality_reports 
                SET title = $1, description = $2, test_type = $3, test_date = $4,
                    sample_info = $5, test_method = $6, test_standard = $7, 
                    test_result = $8, conclusion = $9, status = $10, updated_at = $11
                WHERE id = $12
                RETURNING *
            `, [
                reportData.title,
                reportData.description,
                reportData.test_type,
                reportData.test_date,
                reportData.sample_info,
                reportData.test_method,
                reportData.test_standard,
                reportData.test_result,
                reportData.conclusion,
                reportData.status,
                now,
                id
            ]);

            return result.rows.length > 0 ? this.transformReport(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新质量报告失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除质量报告
     */
    async deleteReport(id) {
        try {
            const result = await this.query('DELETE FROM quality_reports WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除质量报告失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据日期前缀统计报告数量
     */
    async countReportsByDatePrefix(datePrefix) {
        try {
            const result = await this.findOne(`
                SELECT COUNT(*) as count
                FROM quality_reports
                WHERE report_number LIKE $1
            `, [`${datePrefix}%`]);
            return result ? parseInt(result.count) : 0;
        } catch (error) {
            logger.error(`统计报告数量失败 (${datePrefix}):`, error);
            throw error;
        }
    }

    /**
     * 获取报告的文件列表
     */
    async findFilesByReportId(reportId) {
        try {
            const files = await this.findMany(`
                SELECT * FROM quality_report_files 
                WHERE report_id = $1 
                ORDER BY uploaded_at ASC
            `, [reportId]);
            return files;
        } catch (error) {
            logger.error(`获取报告文件失败 (${reportId}):`, error);
            throw error;
        }
    }

    /**
     * 根据ID获取文件
     */
    async findFileById(fileId) {
        try {
            const file = await this.findOne(`
                SELECT qrf.*, qr.title as report_title, qr.report_number
                FROM quality_report_files qrf
                LEFT JOIN quality_reports qr ON qrf.report_id = qr.id
                WHERE qrf.id = $1
            `, [fileId]);
            return file;
        } catch (error) {
            logger.error(`根据ID获取文件失败 (${fileId}):`, error);
            throw error;
        }
    }

    /**
     * 创建文件记录
     */
    async createFile(fileData) {
        try {
            const result = await this.query(`
                INSERT INTO quality_report_files (
                    id, report_id, original_filename, stored_filename,
                    file_path, file_size, file_type, mime_type, uploaded_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            `, [
                fileData.id,
                fileData.report_id,
                fileData.original_filename,
                fileData.stored_filename,
                fileData.file_path,
                fileData.file_size,
                fileData.file_type,
                fileData.mime_type,
                fileData.uploaded_at
            ]);

            return result.rows[0];
        } catch (error) {
            logger.error('创建文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 删除报告的所有文件
     */
    async deleteFilesByReportId(reportId) {
        try {
            const result = await this.query('DELETE FROM quality_report_files WHERE report_id = $1', [reportId]);
            return result.rowCount;
        } catch (error) {
            logger.error(`删除报告文件失败 (${reportId}):`, error);
            throw error;
        }
    }

    /**
     * 生成报告编号
     */
    async generateReportNumber() {
        try {
            const today = new Date();
            const datePrefix = today.toISOString().slice(0, 10).replace(/-/g, '');
            
            const count = await this.countReportsByDatePrefix(datePrefix);
            const sequence = (count + 1).toString().padStart(4, '0');
            
            return `QR${datePrefix}${sequence}`;
        } catch (error) {
            logger.error('生成报告编号失败:', error);
            throw error;
        }
    }

    /**
     * 转换报告数据格式
     */
    transformReport(report) {
        if (!report) return null;
        
        return {
            ...report,
            file_count: parseInt(report.file_count) || 0
        };
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
}

module.exports = PostgresQualityRepository;
