/**
 * 全局事件管理器
 * 用于组件间通信和事件传递
 */

class EventManager {
    constructor() {
        this.events = {};
    }

    /**
     * 注册事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(eventName, callback) {
        if (!this.events[eventName]) {
            return;
        }
        
        const index = this.events[eventName].indexOf(callback);
        if (index > -1) {
            this.events[eventName].splice(index, 1);
        }
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {*} data - 事件数据
     */
    emit(eventName, data) {
        if (!this.events[eventName]) {
            return;
        }
        
        this.events[eventName].forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`事件处理器执行失败 [${eventName}]:`, error);
            }
        });
    }

    /**
     * 注册一次性事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    once(eventName, callback) {
        const onceCallback = (data) => {
            callback(data);
            this.off(eventName, onceCallback);
        };
        this.on(eventName, onceCallback);
    }

    /**
     * 清除所有事件监听器
     */
    clear() {
        this.events = {};
    }

    /**
     * 清除指定事件的所有监听器
     * @param {string} eventName - 事件名称
     */
    clearEvent(eventName) {
        delete this.events[eventName];
    }

    /**
     * 获取事件监听器数量
     * @param {string} eventName - 事件名称
     * @returns {number} 监听器数量
     */
    getListenerCount(eventName) {
        return this.events[eventName] ? this.events[eventName].length : 0;
    }

    /**
     * 获取所有事件名称
     * @returns {string[]} 事件名称数组
     */
    getEventNames() {
        return Object.keys(this.events);
    }
}

// 创建全局事件管理器实例
const eventManager = new EventManager();

// 定义常用事件名称
export const EVENTS = {
    // 文件管理相关事件
    FILE_UPLOADED: 'file:uploaded',
    FILE_UPDATED: 'file:updated',
    FILE_DELETED: 'file:deleted',
    FILE_LIST_REFRESH: 'file:list:refresh',
    
    // 样品单相关事件
    SAMPLE_FORM_SUBMITTED: 'sample:form:submitted',
    SAMPLE_FORM_UPDATED: 'sample:form:updated',
    SAMPLE_FORM_DELETED: 'sample:form:deleted',
    
    // 申请相关事件
    APPLICATION_SUBMITTED: 'application:submitted',
    APPLICATION_APPROVED: 'application:approved',
    APPLICATION_REJECTED: 'application:rejected',
    
    // 质量管理相关事件
    QUALITY_REPORT_UPLOADED: 'quality:report:uploaded',
    QUALITY_REPORT_UPDATED: 'quality:report:updated',
    
    // 用户相关事件
    USER_LOGIN: 'user:login',
    USER_LOGOUT: 'user:logout',
    USER_PROFILE_UPDATED: 'user:profile:updated',
    
    // 系统相关事件
    SYSTEM_NOTIFICATION: 'system:notification',
    SYSTEM_ERROR: 'system:error',
    SYSTEM_WARNING: 'system:warning'
};

export default eventManager;
