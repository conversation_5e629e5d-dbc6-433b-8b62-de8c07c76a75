/**
 * 登录页面逻辑
 */

import { login, checkAuth } from '../api/auth.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

createApp({
    setup() {
        // 状态变量
        const username = ref('');
        const password = ref('');
        const showPassword = ref(false);
        const isLoading = ref(false);
        const errorMessage = ref('');

        // 初始化
        onMounted(() => {
            checkLoggedIn();
            // 确保加载指示器被隐藏
            hideLoading();
        });

        // 切换密码可见性
        function togglePasswordVisibility() {
            showPassword.value = !showPassword.value;
        }

        // 登录处理
        async function handleLogin() {
            if (!username.value || !password.value) {
                errorMessage.value = '请输入用户名和密码';
                return;
            }

            try {
                isLoading.value = true;
                errorMessage.value = '';

                await login(username.value, password.value);

                // 登录成功，检查是否有重定向地址
                const redirectPath = sessionStorage.getItem('redirectAfterLogin');
                if (redirectPath) {
                    sessionStorage.removeItem('redirectAfterLogin');
                    window.location.href = redirectPath;
                } else {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('登录失败:', error);
                errorMessage.value = error.response?.data?.message || '登录失败，请稍后再试';
            } finally {
                isLoading.value = false;
            }
        }

        // 检查是否已登录，如果已登录则跳转到主页
        async function checkLoggedIn() {
            const isLoggedIn = await checkAuth();
            if (isLoggedIn) {
                window.location.href = '/';
            }
        }

        return {
            username,
            password,
            showPassword,
            isLoading,
            errorMessage,
            togglePasswordVisibility,
            login: handleLogin
        };
    }
}).mount('#loginApp');
