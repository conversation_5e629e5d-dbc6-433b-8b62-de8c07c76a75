/**
 * PostgreSQL样品单数据访问层
 * 处理样品单相关的数据库操作
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresSampleFormRepository extends BasePostgresRepository {
    constructor() {
        super();
    }

    /**
     * 创建样品单
     */
    async createSampleForm(sampleData) {
        try {
            // 使用当前本地时间，确保时区正确
            const now = new Date();

            // 确保additional_notes字段存在
            await this.ensureAdditionalNotesField();
            
            const result = await this.query(`
                INSERT INTO sample_forms (
                    id, sample_number, generated_number, date, purpose,
                    company_name, recipient, application, address,
                    product_model, product_description, outer_layer, middle_layer, inner_layer,
                    headband, nose_bridge, ear_loop, valve, valve_top_cover, valve_bottom_cover, valve_sheet,
                    quantity, print_color, print_size, headband_color, valve_color, valve_print,
                    print_material_color, print_material_size, adjustment_buckle, internal_test,
                    packaging_method, packaging_materials, color_box, tape, tape_material,
                    blister_pack, other, other_description, test_notes, test_quantity,
                    test_items, test_levels, test_sample_counts, other_test_items,
                    additional_notes, recipients, cc_recipients, status, created_by, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                    $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
                    $21, $22, $23, $24, $25, $26, $27, $28, $29, $30,
                    $31, $32, $33, $34, $35, $36, $37, $38, $39, $40,
                    $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51, $52
                )
                RETURNING *
            `, [
                sampleData.id,
                sampleData.sampleNumber,
                sampleData.generatedNumber,
                sampleData.date,
                sampleData.purpose,
                sampleData.companyName,
                sampleData.recipient,
                sampleData.application,
                sampleData.address,
                sampleData.productModel,
                sampleData.productDescription,
                sampleData.outerLayer,
                sampleData.middleLayer,
                sampleData.innerLayer,
                sampleData.headband,
                sampleData.noseBridge,
                sampleData.earLoop,
                sampleData.valve,
                sampleData.valveTopCover,
                sampleData.valveBottomCover,
                sampleData.valveSheet,
                sampleData.quantity,
                sampleData.printColor,
                sampleData.printSize,
                sampleData.headbandColor,
                sampleData.valveColor,
                sampleData.valvePrint,
                sampleData.printMaterialColor,
                sampleData.printMaterialSize,
                sampleData.adjustmentBuckle,
                sampleData.internalTest,
                sampleData.packagingMethod,
                JSON.stringify(sampleData.packagingMaterials || []),
                sampleData.colorBox,
                sampleData.tape,
                sampleData.tapeMaterial,
                sampleData.blisterPack,
                sampleData.other,
                sampleData.otherDescription,
                sampleData.testNotes,
                sampleData.testQuantity,
                JSON.stringify(sampleData.testItems || []),
                JSON.stringify(sampleData.testLevels || {}),
                JSON.stringify(sampleData.testSampleCounts || {}),
                JSON.stringify(sampleData.otherTestItems || {}),
                sampleData.additionalNotes || '',
                JSON.stringify(sampleData.recipients || []),
                JSON.stringify(sampleData.ccRecipients || []),
                sampleData.status || 'submitted',
                sampleData.createdBy,
                now,
                now
            ]);

            return this.transformSampleForm(result.rows[0]);
        } catch (error) {
            logger.error('创建样品单失败:', error);
            throw error;
        }
    }

    /**
     * 创建样品单附件
     */
    async createSampleFormAttachment(attachmentData) {
        try {
            const result = await this.query(`
                INSERT INTO sample_form_attachments (
                    id, sample_form_id, file_type, original_filename, stored_filename,
                    file_path, file_size, mime_type, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            `, [
                attachmentData.id,
                attachmentData.sampleFormId,
                attachmentData.fileType,
                attachmentData.originalFilename,
                attachmentData.storedFilename,
                attachmentData.filePath,
                attachmentData.fileSize,
                attachmentData.mimeType,
                new Date().toISOString()
            ]);

            return result.rows[0];
        } catch (error) {
            logger.error('创建样品单附件失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有样品单
     */
    async getAllSampleForms() {
        try {
            const result = await this.query(`
                SELECT sf.*, u.username as created_by_name
                FROM sample_forms sf
                LEFT JOIN users u ON sf.created_by = u.id
                WHERE sf.status != 'deleted'
                ORDER BY sf.created_at DESC
            `);

            logger.info(`获取样品单列表: 找到 ${result.rows.length} 条有效记录`);

            const sampleForms = result.rows.map(row => this.transformSampleForm(row));
            
            // 为每个样品单获取附件
            for (const sampleForm of sampleForms) {
                sampleForm.attachments = await this.getAttachmentsBySampleFormId(sampleForm.id);
            }

            return sampleForms;
        } catch (error) {
            logger.error('获取样品单列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取样品单
     */
    async getSampleFormById(id) {
        try {
            const result = await this.query(`
                SELECT sf.*, u.username as created_by_name
                FROM sample_forms sf
                LEFT JOIN users u ON sf.created_by = u.id
                WHERE sf.id = $1 AND sf.status != 'deleted'
            `, [id]);

            if (result.rows.length === 0) {
                return null;
            }

            const sampleForm = this.transformSampleForm(result.rows[0]);
            sampleForm.attachments = await this.getAttachmentsBySampleFormId(id);

            return sampleForm;
        } catch (error) {
            logger.error('获取样品单详情失败:', error);
            throw error;
        }
    }

    /**
     * 获取样品单附件
     */
    async getAttachmentsBySampleFormId(sampleFormId) {
        try {
            const result = await this.query(`
                SELECT * FROM sample_form_attachments
                WHERE sample_form_id = $1
                ORDER BY created_at ASC
            `, [sampleFormId]);

            return result.rows;
        } catch (error) {
            logger.error('获取样品单附件失败:', error);
            throw error;
        }
    }

    /**
     * 生成样品单编号
     */
    async generateSampleNumber() {
        try {
            const today = new Date();
            const dateStr = today.getFullYear().toString() +
                           (today.getMonth() + 1).toString().padStart(2, '0') +
                           today.getDate().toString().padStart(2, '0');

            // 获取当天已使用的最大序号
            const result = await this.query(`
                SELECT sample_number FROM sample_forms
                WHERE sample_number LIKE $1
                ORDER BY sample_number DESC
                LIMIT 1
            `, [`SD-${dateStr}-%`]);

            let nextSequence = 1;
            if (result.rows.length > 0) {
                const lastNumber = result.rows[0].sample_number;
                const lastSequence = parseInt(lastNumber.split('-')[2]);
                nextSequence = lastSequence + 1;
            }

            return `SD-${dateStr}-${nextSequence.toString().padStart(3, '0')}`;
        } catch (error) {
            logger.error('生成样品单编号失败:', error);
            throw error;
        }
    }

    /**
     * 删除样品单
     */
    async deleteSampleForm(id) {
        try {
            // 开始事务
            await this.query('BEGIN');

            try {
                // 首先获取附件信息，用于删除物理文件
                const attachments = await this.query(`
                    SELECT * FROM sample_form_attachments
                    WHERE sample_form_id = $1
                `, [id]);

                // 删除样品单附件记录
                const attachmentResult = await this.query(`
                    DELETE FROM sample_form_attachments
                    WHERE sample_form_id = $1
                    RETURNING *
                `, [id]);

                logger.info(`删除样品单附件记录: ID=${id}, 删除附件数=${attachmentResult.rowCount}`);

                // 然后标记样品单为已删除状态
                const result = await this.query(`
                    UPDATE sample_forms
                    SET status = 'deleted', updated_at = $1
                    WHERE id = $2 AND status != 'deleted'
                    RETURNING *
                `, [new Date().toISOString(), id]);

                logger.info(`样品单删除操作: ID=${id}, 影响行数=${result.rowCount}`);

                // 提交事务
                await this.query('COMMIT');

                return result.rowCount > 0;
            } catch (error) {
                // 回滚事务
                await this.query('ROLLBACK');
                throw error;
            }
        } catch (error) {
            logger.error('删除样品单失败:', error);
            throw error;
        }
    }

    /**
     * 确保additional_notes字段存在
     */
    async ensureAdditionalNotesField() {
        try {
            // 检查字段是否存在
            const checkResult = await this.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'sample_forms'
                AND column_name = 'additional_notes'
            `);

            if (checkResult.rows.length === 0) {
                // 字段不存在，添加它
                await this.query(`
                    ALTER TABLE sample_forms
                    ADD COLUMN additional_notes TEXT
                `);
                logger.info('已添加sample_forms表的additional_notes字段');
            }
        } catch (error) {
            logger.error('检查/添加additional_notes字段失败:', error);
            // 不抛出错误，继续执行
        }
    }

    /**
     * 转换样品单数据
     */
    transformSampleForm(row) {
        if (!row) return null;

        return {
            id: row.id,
            sampleNumber: row.sample_number,
            generatedNumber: row.generated_number,
            date: row.date,
            purpose: row.purpose,
            companyName: row.company_name,
            recipient: row.recipient,
            application: row.application,
            address: row.address,
            productModel: row.product_model,
            productDescription: row.product_description,
            outerLayer: row.outer_layer,
            middleLayer: row.middle_layer,
            innerLayer: row.inner_layer,
            headband: row.headband,
            noseBridge: row.nose_bridge,
            earLoop: row.ear_loop,
            valve: row.valve,
            valveTopCover: row.valve_top_cover,
            valveBottomCover: row.valve_bottom_cover,
            valveSheet: row.valve_sheet,
            quantity: row.quantity,
            printColor: row.print_color,
            printSize: row.print_size,
            headbandColor: row.headband_color,
            valveColor: row.valve_color,
            valvePrint: row.valve_print,
            printMaterialColor: row.print_material_color,
            printMaterialSize: row.print_material_size,
            adjustmentBuckle: row.adjustment_buckle,
            internalTest: row.internal_test,
            packagingMethod: row.packaging_method,
            packagingMaterials: this.parseJSON(row.packaging_materials, []),
            colorBox: row.color_box,
            tape: row.tape,
            tapeMaterial: row.tape_material,
            blisterPack: row.blister_pack,
            other: row.other,
            otherDescription: row.other_description,
            testNotes: row.test_notes,
            testQuantity: row.test_quantity,
            testItems: this.parseJSON(row.test_items, []),
            testLevels: this.parseJSON(row.test_levels, {}),
            testSampleCounts: this.parseJSON(row.test_sample_counts, {}),
            otherTestItems: this.parseJSON(row.other_test_items, {}),
            additionalNotes: row.additional_notes,
            recipients: this.parseJSON(row.recipients, []),
            ccRecipients: this.parseJSON(row.cc_recipients, []),
            status: row.status,
            createdBy: row.created_by,
            createdByName: row.created_by_name,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            // 为了与文件列表兼容，添加这些字段
            title: `样品单 - ${row.company_name} - ${row.product_model}`,
            file_number: row.sample_number,
            customer_name: row.company_name,
            product_model: row.product_model,
            uploaded_at: row.created_at,
            uploaded_by_name: row.created_by_name,
            version: 1,
            is_first_version: true,
            type: 'sample_form'
        };
    }
}

module.exports = PostgresSampleFormRepository;
