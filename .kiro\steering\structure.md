# 项目结构与组织

## 根目录结构

```
/
├── frontend/                   # 前端代码 (Vue.js 3)
├── backend/                    # 后端代码 (Node.js + Express)
├── docs/                       # 项目文档
├── scripts/                    # 构建和工具脚本
├── uploads/                    # 文件上传存储
├── .env                        # 环境变量（根级别）
├── package.json               # 根package.json用于脚本
└── README.md                  # 项目文档
```

## 前端结构 (`frontend/`)

### 页面 (`frontend/pages/`)
- **HTML文件**: 每个路由/页面一个文件
- **命名**: 与路由匹配的描述性名称
- **结构**: 按功能模块组织
- **脚本**: JavaScript文件放在`<body>`末尾（不在`<head>`中）

### 组件 (`frontend/components/`)
- **Vue.js组件**: 可重用的UI组件
- **组织**: 按功能区域（common/, application/, scheduling/等）
- **命名**: 组件文件使用PascalCase

### 脚本 (`frontend/scripts/`)
- **API模块** (`api/`): HTTP请求处理器
- **通用工具** (`common/`): 共享工具函数
- **页面脚本** (`pages/`): 页面特定的业务逻辑
- **配置** (`config.js`): 前端配置

### 资源 (`frontend/assets/`)
- **CSS**: 自定义样式和Tailwind CSS
- **图片**: 静态图片和logo
- **字体**: Web字体和字体文件

## 后端结构 (`backend/`)

### 核心文件
- **server.js**: 应用程序入口点
- **package.json**: 后端依赖

### 控制器 (`backend/controllers/`)
- **用途**: 处理HTTP请求和响应
- **命名**: `[功能]Controller.js`
- **模式**: 数据库操作使用Async/await

### 服务 (`backend/services/`)
- **用途**: 业务逻辑和数据处理
- **命名**: `[功能]Service.js`
- **模式**: 尽可能使用纯函数

### 路由 (`backend/routes/`)
- **用途**: 定义API端点
- **命名**: `[功能]Routes.js`
- **组织**: RESTful API模式

### 模型 (`backend/models/`)
- **用途**: 数据模型和验证
- **命名**: `[功能]Model.js`
- **模式**: 数据库模式定义

### 数据库 (`backend/database/`)
- **用途**: 数据库连接和迁移
- **文件**: 连接池，适配器，仓库
- **模式**: 数据访问的仓库模式

### 中间件 (`backend/middlewares/`)
- **用途**: 请求/响应处理
- **类型**: 认证，日志，验证，安全
- **模式**: Express中间件函数

### 工具 (`backend/utils/`)
- **用途**: 工具函数和助手
- **类型**: 文件系统，日志，验证，性能监控
- **模式**: 纯函数和单例类

## 关键架构模式

### 前端模式

#### 组件组织
```javascript
// 组件结构
export default {
    props: { /* 组件属性 */ },
    setup(props) {
        // Composition API逻辑
        return { /* 响应式数据和方法 */ };
    },
    template: `/* HTML模板 */`
};
```

#### API集成
```javascript
// API模块模式
import { getApiUrl } from '../config.js';

export async function fetchData() {
    const response = await axios.get(`${await getApiUrl()}/endpoint`);
    return response.data;
}
```

#### 页面初始化
```javascript
// 页面脚本模式
import { initializePage, checkPermissions } from '../common/utils.js';

async function init() {
    const user = await initializePage();
    if (!checkPermissions(user, 'required_permission')) return;
    // 页面特定逻辑
}

init();
```

### 后端模式

#### 控制器模式
```javascript
// 控制器结构
async function controllerFunction(req, res) {
    try {
        const result = await service.processData(req.body);
        res.json({ success: true, data: result });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
}
```

#### 服务模式
```javascript
// 服务结构
class FeatureService {
    async processData(data) {
        // 业务逻辑
        return await repository.save(processedData);
    }
}
```

#### 仓库模式
```javascript
// 仓库结构
class FeatureRepository {
    async findById(id) {
        // 数据库查询
        return await db.query('SELECT * FROM table WHERE id = ?', [id]);
    }
}
```

## 文件命名约定

### 前端
- **页面**: `kebab-case.html` (例如: `new-application.html`)
- **组件**: `PascalCase.js` (例如: `ApplicationForm.js`)
- **脚本**: `camelCase.js` (例如: `applicationService.js`)
- **样式**: `kebab-case.css` (例如: `sidebar-scrollbar.css`)

### 后端
- **控制器**: `camelCaseController.js` (例如: `authController.js`)
- **服务**: `camelCaseService.js` (例如: `userService.js`)
- **路由**: `camelCaseRoutes.js` (例如: `applicationRoutes.js`)
- **模型**: `camelCaseModel.js` (例如: `userModel.js`)

## 模块组织

### 基于功能的组织
每个主要功能都有自己的目录结构：

```
feature/
├── pages/           # HTML页面
├── scripts/         # JavaScript逻辑
├── components/      # Vue组件
├── controllers/     # 后端控制器
├── services/        # 业务逻辑
├── routes/          # API路由
└── models/          # 数据模型
```

### 通用模块
共享功能在通用目录中组织：

```
common/
├── utils/           # 工具函数
├── middlewares/     # Express中间件
├── components/      # 可重用UI组件
└── styles/          # 共享CSS
```

## 配置管理

### 环境变量
- **位置**: 根目录`.env`文件
- **后端访问**: `process.env.VARIABLE_NAME`
- **前端访问**: 通过API配置端点

### 配置文件
- **后端**: `backend/config/index.js`
- **前端**: `frontend/scripts/config.js`
- **数据库**: 连接字符串和池设置

## 权限系统集成

### 前端权限检查
```javascript
// 渲染前检查用户权限
if (hasPermission(user, 'feature_access')) {
    // 渲染功能
}
```

### 后端权限中间件
```javascript
// 使用权限中间件保护路由
router.get('/endpoint', requirePermission('feature_access'), controller);
```

## 开发工作流

1. **功能开发**: 创建功能分支
2. **前端优先**: 构建UI组件和页面
3. **后端集成**: 实现API端点
4. **测试**: 测试前端和后端
5. **文档**: 更新相关文档
6. **代码审查**: 合并前审查

## 最佳实践

- **关注点分离**: 将业务逻辑保持在服务中
- **错误处理**: 一致的错误响应
- **验证**: 前端和后端都进行输入验证
- **安全**: 所有受保护路由的权限检查
- **性能**: 使用缓存和优化技术
- **可维护性**: 清晰的命名和文档