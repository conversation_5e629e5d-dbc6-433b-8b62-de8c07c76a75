/* 主页样式文件 - Dashboard CSS */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
    display: none;
}

/* 侧边栏按钮过渡效果 */
.sidebar-btn {
    transition: background-color 0.2s;
}

/* 加载中覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #F3F4F6;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 加载中旋转动画 */
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e5e7eb;
    border-top: 5px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 主页英雄区域 - 现代化设计 */
.dashboard-hero-modern {
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 英雄区域背景图案 */
.hero-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.dashboard-hero-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 保留原有英雄区域样式以兼容 */
.dashboard-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.dashboard-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* 统计数据网格布局 - 优化响应式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 640px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* 统计卡片样式 - 现代化设计 */
.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(229, 231, 235, 0.6);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--card-accent, #2563EB), var(--card-accent-light, #3B82F6));
    transform: scaleX(0);
    transition: transform 0.4s ease;
    border-radius: 20px 20px 0 0;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: var(--card-accent, #2563EB);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

/* 统计卡片颜色主题 - 遵循系统主色调 */
.stat-card.blue {
    --card-accent: #2563EB;
    --card-accent-light: #3B82F6;
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
}
.stat-card.green {
    --card-accent: #059669;
    --card-accent-light: #10b981;
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
}
.stat-card.purple {
    --card-accent: #7c3aed;
    --card-accent-light: #8b5cf6;
    background: linear-gradient(135deg, #ffffff 0%, #faf5ff 100%);
}
.stat-card.orange {
    --card-accent: #d97706;
    --card-accent-light: #f59e0b;
    background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}
.stat-card.indigo {
    --card-accent: #4f46e5;
    --card-accent-light: #6366f1;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
}
.stat-card.red {
    --card-accent: #dc2626;
    --card-accent-light: #ef4444;
    background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

/* 统计卡片头部 */
.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

/* 统计图标 - 现代化设计 */
.stat-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-icon::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 16px;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

.stat-card:hover .stat-icon::after {
    opacity: 1;
}

/* 统计数值 - 增强视觉效果 */
.stat-value {
    font-size: 2.5rem;
    font-weight: 900;
    color: #111827;
    margin-bottom: 0.5rem;
    line-height: 1;
    background: linear-gradient(135deg, #111827 0%, #374151 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-value {
    transform: scale(1.05);
}

/* 统计标签 - 优化字体 */
.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 统计底部信息 */
.stat-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
}

/* 统计变化指示器 */
.stat-change {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.stat-change.positive {
    color: #065f46;
    background-color: #d1fae5;
}

.stat-change.negative {
    color: #991b1b;
    background-color: #fee2e2;
}

/* 快速操作区域 - 现代化设计 */
.quick-actions {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(229, 231, 235, 0.6);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.quick-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563EB, #3B82F6, #60A5FA);
    border-radius: 20px 20px 0 0;
}

/* 操作网格布局 - 优化响应式 */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
}

@media (max-width: 640px) {
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* 操作卡片 - 现代化设计 */
.action-card {
    background: white;
    border: 1px solid rgba(229, 231, 235, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left 0.6s ease;
}

.action-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    border-color: #2563EB;
    text-decoration: none;
    color: inherit;
}

.action-card:hover::before {
    left: 100%;
}

/* 操作图标 - 现代化设计 */
.action-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 内容网格布局 - 优化响应式 */
.content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 1280px) {
    .content-grid {
        grid-template-columns: 1fr 1fr 1fr;
    }
}

/* 内容卡片 - 现代化设计 */
.content-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(229, 231, 235, 0.6);
    height: 420px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563EB, #3B82F6);
    border-radius: 20px 20px 0 0;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.content-card.auto-height {
    height: fit-content;
    max-height: 420px;
}

/* 区域头部 */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    flex-shrink: 0;
}

/* 卡片内容 */
.card-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 可滚动内容 */
.scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 滚动条样式 */
.scrollable-content::-webkit-scrollbar {
    width: 4px;
}

.scrollable-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 区域标题 */
.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-left: 0.5rem;
}

/* 区域图标 */
.section-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #6b7280;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
}

.empty-state svg {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    color: #d1d5db;
}

/* 列表项间距 */
.item-list > * + * {
    margin-top: 0.75rem;
}

/* 列表项 */
.list-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.list-item:hover {
    background-color: #f9fafb;
}

/* 项目图标 */
.item-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* 项目内容 */
.item-content {
    flex: 1;
    min-width: 0;
}

/* 项目标题 */
.item-title {
    font-weight: 500;
    color: #111827;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* 项目副标题 */
.item-subtitle {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 项目徽章 */
.item-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    flex-shrink: 0;
}

/* 优先级点 */
.priority-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* 通知项 */
.notification-item {
    padding: 0.75rem;
    border-left: 3px solid transparent;
    border-radius: 0 8px 8px 0;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 通知优先级样式 */
.notification-item.high {
    border-left-color: #ef4444;
    background-color: rgba(254, 242, 242, 0.5);
}

.notification-item.medium {
    border-left-color: #f59e0b;
    background-color: rgba(255, 251, 235, 0.5);
}

.notification-item.low {
    border-left-color: #10b981;
    background-color: rgba(240, 253, 244, 0.5);
}

/* 新增样式 - 现代化增强 */

/* 全局背景优化 */
body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* 移动端菜单按钮优化 */
.mobile-menu-btn {
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    transform: scale(1.05);
}

/* 用户头像和信息样式 */
.user-avatar {
    background: linear-gradient(135deg, #2563EB, #3B82F6);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

/* 加载状态优化 */
.loading-spinner {
    border-top-color: #2563EB;
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .stats-grid {
        padding: 0 0.5rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .content-card {
        padding: 1.5rem;
        height: auto;
        min-height: 300px;
    }

    .quick-actions {
        padding: 1.5rem;
    }
}

/* 动画增强 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }
.stat-card:nth-child(6) { animation-delay: 0.6s; }

/* 滚动条全局优化 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #2563EB, #3B82F6);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563EB);
}

/* 焦点状态优化 */
button:focus,
a:focus {
    outline: 2px solid #2563EB;
    outline-offset: 2px;
}

/* 文字选择优化 */
::selection {
    background: rgba(37, 99, 235, 0.2);
    color: #1e40af;
}
