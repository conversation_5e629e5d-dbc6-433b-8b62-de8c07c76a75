#!/usr/bin/env node
/**
 * 调试产品数据，查看specifications字段的实际内容
 */

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'makrite25!',
    database: process.env.POSTGRES_DATABASE || 'makrite_system',
});

async function debugProductData() {
    try {
        console.log('🔍 调试产品数据...');
        console.log('='.repeat(80));
        
        // 查看products表的数据
        const result = await pool.query('SELECT * FROM products LIMIT 5');
        
        console.log(`📊 找到 ${result.rows.length} 条产品数据:\n`);
        
        result.rows.forEach((product, index) => {
            console.log(`${index + 1}. 产品ID: ${product.id}`);
            console.log(`   名称: ${product.name}`);
            console.log(`   代码: ${product.code}`);
            console.log(`   规格字段类型: ${typeof product.specifications}`);
            console.log(`   规格字段值: ${JSON.stringify(product.specifications)}`);
            console.log(`   规格字段原始值: ${product.specifications}`);
            console.log();
        });
        
        // 测试ProductService.getProductOptions
        console.log('🧪 测试ProductService.getProductOptions...');
        const ProductService = require('./backend/services/productService');
        const productService = new ProductService();
        
        try {
            const result = await productService.getProductOptions();
            console.log('✅ ProductService.getProductOptions 成功:', result);
        } catch (error) {
            console.log('❌ ProductService.getProductOptions 失败:', error.message);
            console.log('错误堆栈:', error.stack);
        }
        
    } catch (error) {
        console.error(`❌ 错误: ${error.message}`);
        console.error('错误堆栈:', error.stack);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    debugProductData();
}
