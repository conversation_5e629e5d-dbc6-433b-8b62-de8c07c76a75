<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统日志 - 系统管理 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/logs-management.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 ml-0 md:ml-72 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
            <!-- 现代化头部区域 -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-6 py-4">
                    <!-- 面包屑导航 -->
                    <nav class="flex mb-4" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li class="inline-flex items-center">
                                <span class="inline-flex items-center text-sm font-medium text-gray-600">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    系统管理
                                </span>
                                <svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span class="text-sm font-medium text-blue-600">系统日志</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- 页面标题和统计信息 -->
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-900">系统日志管理</h1>
                                    <p class="text-gray-600">实时监控和分析系统运行日志</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                            <!-- 统计信息卡片 -->
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl shadow-lg">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <div class="text-sm">
                                        <div class="font-medium">{{ logState.statistics.totalFiles }} 个文件</div>
                                        <div class="opacity-90">{{ formatFileSize(logState.statistics.totalSize) }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 当前文件指示器 -->
                            <div v-if="hasSelectedFile" class="bg-green-100 text-green-800 px-4 py-3 rounded-xl border border-green-200">
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm font-medium">{{ logState.selectedFile.name }}</span>
                                </div>
                            </div>

                            <!-- 快捷键提示 -->
                            <div class="bg-gray-100 text-gray-700 px-4 py-3 rounded-xl border border-gray-200">
                                <div class="flex items-center space-x-2 text-xs">
                                    <kbd class="px-2 py-1 bg-white border border-gray-300 rounded text-xs font-mono">Ctrl+F</kbd>
                                    <span>搜索</span>
                                    <kbd class="px-2 py-1 bg-white border border-gray-300 rounded text-xs font-mono">Esc</kbd>
                                    <span>清除</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 现代化日志管理界面 -->
            <div class="p-6">
                <div class="logs-container-modern">
                    <!-- 上方控制面板 -->
                    <div class="control-panel-modern mb-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- 日志文件列表 -->
                            <div class="lg:col-span-1">
                                <log-file-list
                                    @file-selected="handleFileSelected"
                                    @files-updated="handleFilesUpdated">
                                </log-file-list>
                            </div>

                            <!-- 搜索组件 -->
                            <div class="lg:col-span-1">
                                <log-search
                                    :selected-file="logState.selectedFile"
                                    @search-results="handleSearchResults"
                                    @search-term-change="handleSearchTermChange">
                                </log-search>
                            </div>

                            <!-- 过滤器组件 -->
                            <div class="lg:col-span-1">
                                <log-filters
                                    :selected-file="logState.selectedFile"
                                    @level-filter-change="handleLevelFilterChange"
                                    @time-filter-change="handleTimeFilterChange"
                                    @filters-reset="handleFiltersReset">
                                </log-filters>
                            </div>
                        </div>
                    </div>

                    <!-- 下方日志查看器 -->
                    <div class="viewer-panel-modern">
                        <log-viewer
                            :selected-file="logState.selectedFile"
                            :search-term="logState.searchTerm"
                            :level-filter="logState.levelFilter">
                        </log-viewer>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/system-management/logs.js"></script>
</body>
</html>
