/**
 * PostgreSQL仓库管理数据访问层
 * 替换SQLite，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('../../../database/basePostgresRepository');
const logger = require('../../../utils/logger');

class PostgresWarehouseRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL仓库管理数据访问层初始化完成');
        }
    }

    // ==================== 物料管理 ====================

    /**
     * 获取所有物料
     */
    async getAllMaterials(filters = {}) {
        try {
            let query = 'SELECT * FROM warehouse_materials WHERE 1=1';
            const params = [];
            let paramIndex = 1;

            if (filters.material_type) {
                query += ` AND material_type = $${paramIndex}`;
                params.push(filters.material_type);
                paramIndex++;
            }

            if (filters.status) {
                query += ` AND status = $${paramIndex}`;
                params.push(filters.status);
                paramIndex++;
            }

            if (filters.search) {
                query += ` AND (material_code ILIKE $${paramIndex} OR material_name ILIKE $${paramIndex})`;
                params.push(`%${filters.search}%`);
                paramIndex++;
            }

            query += ' ORDER BY material_code';

            if (filters.limit) {
                query += ` LIMIT $${paramIndex}`;
                params.push(parseInt(filters.limit));
            }

            const materials = await this.findMany(query, params);
            return materials.map(material => this.transformMaterial(material));
        } catch (error) {
            logger.error('获取物料列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取物料
     */
    async getMaterialById(id) {
        try {
            const material = await this.findOne('SELECT * FROM warehouse_materials WHERE id = $1', [id]);
            return material ? this.transformMaterial(material) : null;
        } catch (error) {
            logger.error(`根据ID获取物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 创建物料
     */
    async createMaterial(materialData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO warehouse_materials (
                    id, material_code, material_name, material_type, unit,
                    current_stock, min_stock, max_stock, unit_price, supplier,
                    location, status, description, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                RETURNING *
            `, [
                materialData.id,
                materialData.material_code,
                materialData.material_name,
                materialData.material_type,
                materialData.unit || 'pcs',
                materialData.current_stock || 0,
                materialData.min_stock || 0,
                materialData.max_stock,
                materialData.unit_price,
                materialData.supplier,
                materialData.location,
                materialData.status || 'active',
                materialData.description,
                now,
                now
            ]);

            return this.transformMaterial(result.rows[0]);
        } catch (error) {
            logger.error('创建物料失败:', error);
            throw error;
        }
    }

    /**
     * 更新物料
     */
    async updateMaterial(id, materialData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE warehouse_materials SET 
                    material_code = $2, material_name = $3, material_type = $4,
                    unit = $5, min_stock = $6, max_stock = $7, unit_price = $8,
                    supplier = $9, location = $10, status = $11, description = $12,
                    updated_at = $13
                WHERE id = $1
                RETURNING *
            `, [
                id,
                materialData.material_code,
                materialData.material_name,
                materialData.material_type,
                materialData.unit,
                materialData.min_stock,
                materialData.max_stock,
                materialData.unit_price,
                materialData.supplier,
                materialData.location,
                materialData.status,
                materialData.description,
                now
            ]);

            return result.rows.length > 0 ? this.transformMaterial(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除物料
     */
    async deleteMaterial(id) {
        try {
            const rowCount = await this.delete('DELETE FROM warehouse_materials WHERE id = $1', [id]);
            return rowCount > 0;
        } catch (error) {
            logger.error(`删除物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 更新物料库存
     */
    async updateMaterialStock(id, quantity, operation = 'add') {
        try {
            const operator = operation === 'add' ? '+' : '-';
            const result = await this.query(`
                UPDATE warehouse_materials 
                SET current_stock = current_stock ${operator} $2, updated_at = $3
                WHERE id = $1
                RETURNING *
            `, [id, Math.abs(quantity), new Date().toISOString()]);

            return result.rows.length > 0 ? this.transformMaterial(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新物料库存失败 (${id}):`, error);
            throw error;
        }
    }

    // ==================== 成品管理 ====================

    /**
     * 获取所有成品
     */
    async getAllProducts(filters = {}) {
        try {
            let query = 'SELECT * FROM warehouse_products WHERE 1=1';
            const params = [];
            let paramIndex = 1;

            if (filters.product_type) {
                query += ` AND product_type = $${paramIndex}`;
                params.push(filters.product_type);
                paramIndex++;
            }

            if (filters.status) {
                query += ` AND status = $${paramIndex}`;
                params.push(filters.status);
                paramIndex++;
            }

            if (filters.search) {
                query += ` AND (product_code ILIKE $${paramIndex} OR product_name ILIKE $${paramIndex})`;
                params.push(`%${filters.search}%`);
                paramIndex++;
            }

            query += ' ORDER BY product_code';

            if (filters.limit) {
                query += ` LIMIT $${paramIndex}`;
                params.push(parseInt(filters.limit));
            }

            const products = await this.findMany(query, params);
            return products.map(product => this.transformProduct(product));
        } catch (error) {
            logger.error('获取成品列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取成品
     */
    async getProductById(id) {
        try {
            const product = await this.findOne('SELECT * FROM warehouse_products WHERE id = $1', [id]);
            return product ? this.transformProduct(product) : null;
        } catch (error) {
            logger.error(`根据ID获取成品失败 (${id}):`, error);
            throw error;
        }
    }

    // ==================== 库存事务管理 ====================

    /**
     * 创建库存事务
     */
    async createTransaction(transactionData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO warehouse_transactions (
                    id, transaction_number, transaction_type, item_type, item_id,
                    quantity, unit_price, total_amount, batch_number, qrcode,
                    operator_id, notes, transaction_date, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                RETURNING *
            `, [
                transactionData.id,
                transactionData.transaction_number,
                transactionData.transaction_type,
                transactionData.item_type,
                transactionData.item_id,
                transactionData.quantity,
                transactionData.unit_price,
                transactionData.total_amount,
                transactionData.batch_number,
                transactionData.qrcode,
                transactionData.operator_id,
                transactionData.notes,
                now,
                now
            ]);

            return this.transformTransaction(result.rows[0]);
        } catch (error) {
            logger.error('创建库存事务失败:', error);
            throw error;
        }
    }

    /**
     * 获取库存事务列表
     */
    async getTransactions(filters = {}) {
        try {
            let query = 'SELECT * FROM warehouse_transactions WHERE 1=1';
            const params = [];
            let paramIndex = 1;

            if (filters.transaction_type) {
                query += ` AND transaction_type = $${paramIndex}`;
                params.push(filters.transaction_type);
                paramIndex++;
            }

            if (filters.item_type) {
                query += ` AND item_type = $${paramIndex}`;
                params.push(filters.item_type);
                paramIndex++;
            }

            if (filters.item_id) {
                query += ` AND item_id = $${paramIndex}`;
                params.push(filters.item_id);
                paramIndex++;
            }

            query += ' ORDER BY transaction_date DESC';

            if (filters.limit) {
                query += ` LIMIT $${paramIndex}`;
                params.push(parseInt(filters.limit));
            }

            const transactions = await this.findMany(query, params);
            return transactions.map(transaction => this.transformTransaction(transaction));
        } catch (error) {
            logger.error('获取库存事务列表失败:', error);
            throw error;
        }
    }

    // ==================== 数据转换方法 ====================

    /**
     * 转换物料数据
     */
    transformMaterial(material) {
        if (!material) return null;
        
        return {
            id: material.id,
            materialCode: material.material_code,
            materialName: material.material_name,
            materialType: material.material_type,
            unit: material.unit,
            currentStock: parseFloat(material.current_stock) || 0,
            minStock: parseFloat(material.min_stock) || 0,
            maxStock: parseFloat(material.max_stock) || 0,
            unitPrice: parseFloat(material.unit_price) || 0,
            supplier: material.supplier,
            location: material.location,
            status: material.status,
            description: material.description,
            createdAt: material.created_at,
            updatedAt: material.updated_at
        };
    }

    /**
     * 转换成品数据
     */
    transformProduct(product) {
        if (!product) return null;
        
        return {
            id: product.id,
            productCode: product.product_code,
            productName: product.product_name,
            productType: product.product_type,
            unit: product.unit,
            currentStock: parseFloat(product.current_stock) || 0,
            minStock: parseFloat(product.min_stock) || 0,
            maxStock: parseFloat(product.max_stock) || 0,
            unitPrice: parseFloat(product.unit_price) || 0,
            location: product.location,
            status: product.status,
            description: product.description,
            createdAt: product.created_at,
            updatedAt: product.updated_at
        };
    }

    /**
     * 转换事务数据
     */
    transformTransaction(transaction) {
        if (!transaction) return null;
        
        return {
            id: transaction.id,
            transactionNumber: transaction.transaction_number,
            transactionType: transaction.transaction_type,
            itemType: transaction.item_type,
            itemId: transaction.item_id,
            quantity: parseFloat(transaction.quantity) || 0,
            unitPrice: parseFloat(transaction.unit_price) || 0,
            totalAmount: parseFloat(transaction.total_amount) || 0,
            batchNumber: transaction.batch_number,
            qrcode: transaction.qrcode,
            operatorId: transaction.operator_id,
            notes: transaction.notes,
            transactionDate: transaction.transaction_date,
            createdAt: transaction.created_at
        };
    }
}

module.exports = PostgresWarehouseRepository;
