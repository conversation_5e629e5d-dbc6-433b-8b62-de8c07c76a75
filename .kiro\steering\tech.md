# 技术栈与构建系统

## 前端技术

- **框架**: Vue.js 3 (Composition API)
- **样式**: Tailwind CSS (JavaScript实现)
- **图表**: Chart.js (UMD版本，支持饼图、柱状图)
- **二维码**: QRCode.js + html5-qrcode (仓库管理)
- **HTTP客户端**: Axios
- **模块系统**: ES6 Modules
- **构建**: 原生ES6，无需构建步骤

## 后端技术

- **运行时**: Node.js 14+
- **框架**: Express.js
- **数据库**: PostgreSQL 15
- **认证**: JWT (JSON Web Tokens)
- **文件上传**: Multer
- **密码加密**: bcrypt
- **邮件服务**: Nodemailer
- **日志记录**: Winston 带日志轮转
- **Excel处理**: ExcelJS
- **PDF生成**: pdf-lib
- **图像处理**: Sharp

## AI算法引擎

- **智能排产**: 多策略优化算法
- **产能计算**: 动态产能分析算法
- **交期预测**: 机器学习预测模型
- **资源优化**: 智能资源匹配算法
- **风险评估**: 多维度风险分析

## 数据库与存储

- **主数据库**: PostgreSQL 15
- **连接池**: PostgreSQL连接池优化
- **文件存储**: 本地文件系统
- **备份**: PostgreSQL备份机制
- **事务支持**: 完整的ACID事务支持
- **缓存**: 内存缓存优化

## 常用命令

### 安装
```bash
# 安装所有依赖（推荐）
npm run install:all

# 或者分别安装
npm install
cd backend && npm install
```

### 开发
```bash
# 启动开发服务器（静默模式，推荐）
npm start

# 启动详细日志模式（用于调试）
set VERBOSE_LOGS=true && npm start

# Windows批处理文件
start-quiet.bat      # 静默模式
start-verbose.bat    # 详细模式
```

### 数据库设置
```bash
# PostgreSQL安装（Windows）
winget install PostgreSQL.PostgreSQL.16

# 创建数据库
psql -U postgres -c "CREATE DATABASE makrite_system;"

# 系统启动时会自动初始化表结构
npm start
```

### 测试与调试
```bash
# 测试邮件服务
cd backend
node scripts/testEmailService.js

# 检查系统健康状态
curl http://localhost:3000/api/system/health

# 性能监控
curl http://localhost:3000/api/system/performance
```

### 生产环境部署
```bash
# 安装PM2进程管理器
npm install -g pm2

# 使用PM2启动
pm2 start backend/server.js --name "enterprise-app"

# 设置开机自启
pm2 startup
pm2 save
```

## 环境配置

### 必需的环境变量 (.env)
```env
# PostgreSQL数据库
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-password
POSTGRES_DATABASE=makrite_system

# 邮件服务（可选）
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
```

## 性能优化

- **前端**: 组件懒加载，图片优化，缓存策略
- **后端**: 数据库索引，查询优化，内存缓存，文件压缩
- **数据库**: 定期日志清理，索引维护，查询优化
- **连接池**: PostgreSQL连接池最小/最大配置

## 安全特性

- JWT Token认证配合sessionStorage
- bcrypt密码加密
- 文件上传验证和大小限制
- SQL注入防护
- XSS攻击防护
- RBAC权限控制系统
- 电子签名安全管理

## 开发工具

- **推荐编辑器**: VS Code + Vue Language Features, WebStorm
- **调试工具**: Chrome DevTools, Vue DevTools浏览器扩展
- **代码规范**: ESLint配置, Prettier格式化
- **版本控制**: Git配合pre-commit钩子