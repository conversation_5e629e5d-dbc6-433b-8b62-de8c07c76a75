/**
 * 文件详情模态框组件
 * 显示文件的完整详细信息
 */

import { downloadFile, fileManagementUtils } from '../../scripts/api/file-management.js';
import SampleFormTemplate from './SampleFormTemplate.js';

const { ref, computed, watch } = Vue;

export default {
    name: 'FileDetailModal',
    components: {
        SampleFormTemplate
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        file: {
            type: Object,
            default: null
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        // 响应式数据
        const modalVisible = ref(false);

        // 计算属性
        const hasAttachments = computed(() => {
            return props.file && props.file.attachments && props.file.attachments.length > 0;
        });

        const totalFileSize = computed(() => {
            if (!hasAttachments.value) return 0;
            return props.file.attachments.reduce((total, attachment) => total + attachment.file_size, 0);
        });

        // 监听显示状态
        watch(() => props.show, (newVal) => {
            modalVisible.value = newVal;
            if (newVal) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });

        // 方法
        function closeModal() {
            modalVisible.value = false;
            document.body.style.overflow = '';
            emit('close');
        }

        function handleDownloadFile(attachment) {
            if (props.file && attachment) {
                downloadFile(props.file.id, attachment.id);
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function getStatusText(file) {
            if (!file) return '未知';

            if (file.type === 'sample_form') {
                // 样品单状态
                switch (file.status) {
                    case 'submitted': return '已提交';
                    case 'processing': return '处理中';
                    case 'completed': return '已完成';
                    case 'cancelled': return '已取消';
                    default: return '已提交';
                }
            } else {
                // 文件记录状态
                switch (file.status) {
                    case 'active': return '正常';
                    case 'inactive': return '停用';
                    case 'pending': return '待审核';
                    case 'approved': return '已审核';
                    case 'rejected': return '已拒绝';
                    default: return '正常';
                }
            }
        }

        function getStatusClass(file) {
            if (!file) return 'bg-gray-100 text-gray-800';

            if (file.type === 'sample_form') {
                // 样品单状态样式
                switch (file.status) {
                    case 'submitted': return 'bg-blue-100 text-blue-800';
                    case 'processing': return 'bg-yellow-100 text-yellow-800';
                    case 'completed': return 'bg-green-100 text-green-800';
                    case 'cancelled': return 'bg-red-100 text-red-800';
                    default: return 'bg-blue-100 text-blue-800';
                }
            } else {
                // 文件记录状态样式
                switch (file.status) {
                    case 'active': return 'bg-green-100 text-green-800';
                    case 'inactive': return 'bg-red-100 text-red-800';
                    case 'pending': return 'bg-yellow-100 text-yellow-800';
                    case 'approved': return 'bg-green-100 text-green-800';
                    case 'rejected': return 'bg-red-100 text-red-800';
                    default: return 'bg-green-100 text-green-800';
                }
            }
        }

        // 获取附件类型显示名称
        function getAttachmentTypeLabel(attachment) {
            if (!attachment.file_type) return '';

            const typeLabels = {
                'maskPrintFiles': '口罩印字图档',
                'valvePrintFiles': '气阀印字图档',
                'colorBoxFiles': '彩盒图档',
                'blisterPackFiles': '吸塑包装图档'
            };

            return typeLabels[attachment.file_type] || '';
        }

        // 样品单模板显示状态
        const showSampleTemplate = Vue.ref(false);

        // 下载PDF - 使用专用模板
        async function downloadPDF() {
            if (!props.file || props.file.type !== 'sample_form') {
                alert('只有样品单支持PDF下载功能');
                return;
            }

            // 显示样品单模板
            showSampleTemplate.value = true;
        }

        // 关闭样品单模板
        function closeSampleTemplate() {
            showSampleTemplate.value = false;
        }

        // 打印
        function printSampleForm() {
            if (!props.file || props.file.type !== 'sample_form') return;

            // 创建打印窗口
            const printWindow = window.open('', '_blank');
            const element = document.querySelector('.modal-content-for-pdf');

            if (!element) {
                alert('找不到要打印的内容');
                return;
            }

            // 构建打印页面HTML
            const printHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>样品单 - ${props.file.companyName} - ${props.file.sampleNumber}</title>
                    <style>
                        body {
                            font-family: "Microsoft YaHei", Arial, sans-serif;
                            margin: 20px;
                            line-height: 1.6;
                            color: #333;
                        }
                        .print-header {
                            text-align: center;
                            margin-bottom: 30px;
                            border-bottom: 2px solid #2563eb;
                            padding-bottom: 20px;
                        }
                        .print-header h1 {
                            color: #2563eb;
                            margin: 0 0 10px 0;
                            font-size: 24px;
                        }
                        .print-content {
                            background: white;
                            padding: 20px;
                        }
                        .grid {
                            display: grid;
                            gap: 15px;
                        }
                        .grid-cols-2 {
                            grid-template-columns: 1fr 1fr;
                        }
                        .grid-cols-3 {
                            grid-template-columns: 1fr 1fr 1fr;
                        }
                        .info-item {
                            margin-bottom: 15px;
                        }
                        .info-label {
                            font-weight: bold;
                            color: #374151;
                            margin-bottom: 5px;
                        }
                        .info-value {
                            color: #111827;
                        }
                        .attachment-list {
                            margin-top: 20px;
                            border: 1px solid #e5e7eb;
                            border-radius: 8px;
                            padding: 15px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none !important; }
                            .print-header { page-break-after: avoid; }
                        }
                    </style>
                </head>
                <body>
                    <div class="print-header">
                        <h1>样品寄送通知单</h1>
                        <p><strong>编号:</strong> ${props.file.sampleNumber}</p>
                        <p><strong>生成时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
                    </div>
                    <div class="print-content">
                        ${element.innerHTML.replace(/class="no-print[^"]*"/g, 'style="display:none"')}
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printHTML);
            printWindow.document.close();

            // 等待内容加载完成后打印
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        }

        return {
            modalVisible,
            hasAttachments,
            totalFileSize,
            closeModal,
            handleDownloadFile,
            formatDate,
            getStatusText,
            getStatusClass,
            getAttachmentTypeLabel,
            downloadPDF,
            printSampleForm,
            showSampleTemplate,
            closeSampleTemplate,
            fileManagementUtils
        };
    },
    template: `
        <!-- 模态框遮罩 -->
        <div v-if="modalVisible" 
             class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
             @click.self="closeModal">
            
            <!-- 模态框内容 -->
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-content-for-pdf">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between w-full">
                        <div class="flex items-center space-x-3">
                            <h2 class="text-xl font-semibold text-gray-900">
                                {{ file && file.type === 'sample_form' ? '样品单详情' : '文件详情' }}
                            </h2>
                            <span v-if="file" class="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                                {{ file.file_number || file.sampleNumber }}
                            </span>
                        </div>

                        <!-- 样品单专用操作按钮 -->
                        <div v-if="file && file.type === 'sample_form'" class="flex items-center space-x-2 no-print">
                            <button @click="downloadPDF"
                                    class="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1.5">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>下载PDF</span>
                            </button>
                            <button @click="printSampleForm"
                                    class="px-3 py-1.5 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-1.5">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                <span>打印</span>
                            </button>
                        </div>
                    </div>
                    <button @click="closeModal"
                            class="text-gray-400 hover:text-gray-600 transition-colors no-print">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 模态框主体 -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]" v-if="file">
                    <!-- 基本信息 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ file.type === 'sample_form' ? '样品单标题' : '文件标题' }}
                                    </label>
                                    <p class="text-sm text-gray-900">{{ file.title }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ file.type === 'sample_form' ? '样品单编号' : '文件编号' }}
                                    </label>
                                    <p class="text-sm text-gray-900">{{ file.file_number || file.sampleNumber }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ file.type === 'sample_form' ? '公司名称' : '客户名称' }}
                                    </label>
                                    <p class="text-sm text-gray-900">{{ file.customer_name || file.companyName }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">产品型号</label>
                                    <p class="text-sm text-gray-900">{{ file.product_model || file.productModel }}</p>
                                </div>
                                <div v-if="file.type !== 'sample_form'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                                    <div class="flex items-center space-x-2">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                            V{{ file.version }}
                                        </span>
                                        <span v-if="!file.is_first_version" class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                            变更版本
                                        </span>
                                    </div>
                                </div>
                                <div v-if="file.type === 'sample_form'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">收件人</label>
                                    <p class="text-sm text-gray-900">{{ file.recipient }}</p>
                                </div>
                                <div v-if="file.type === 'sample_form'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">数量</label>
                                    <p class="text-sm text-gray-900">{{ file.quantity }} 个</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                    <span :class="getStatusClass(file)" class="px-2 py-1 text-xs rounded-full">
                                        {{ getStatusText(file) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ file.type === 'sample_form' ? '提交人员' : '上传人员' }}
                                    </label>
                                    <p class="text-sm text-gray-900">{{ file.uploaded_by_name || file.createdByName || file.createdBy }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ file.type === 'sample_form' ? '提交时间' : '上传时间' }}
                                    </label>
                                    <p class="text-sm text-gray-900">{{ formatDate(file.uploaded_at || file.createdAt) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件描述/样品单目的 -->
                    <div v-if="file.description || file.purpose" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            {{ file.type === 'sample_form' ? '邮寄目的' : '文件描述' }}
                        </h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ file.description || file.purpose }}</p>
                        </div>
                    </div>

                    <!-- 变更说明 -->
                    <div v-if="file.change_description && !file.is_first_version" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">变更说明</h3>
                        <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ file.change_description }}</p>
                        </div>
                    </div>

                    <!-- 附件列表 -->
                    <div v-if="hasAttachments" class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            附件列表 
                            <span class="text-sm font-normal text-gray-500">
                                ({{ file.attachments.length }} 个文件，总大小 {{ fileManagementUtils.formatFileSize(totalFileSize) }})
                            </span>
                        </h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="space-y-3">
                                <div v-for="attachment in file.attachments" :key="attachment.id"
                                     class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center space-x-3 flex-1">
                                        <span class="text-2xl">{{ fileManagementUtils.getFileTypeIcon(attachment.original_filename) }}</span>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center space-x-2 mb-1">
                                                <p class="text-sm font-medium text-gray-900 truncate">{{ attachment.original_filename }}</p>
                                                <span v-if="getAttachmentTypeLabel(attachment)" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full whitespace-nowrap">
                                                    {{ getAttachmentTypeLabel(attachment) }}
                                                </span>
                                            </div>
                                            <p class="text-xs text-gray-500">
                                                {{ fileManagementUtils.formatFileSize(attachment.file_size) }} •
                                                {{ attachment.mime_type }}
                                            </p>
                                        </div>
                                    </div>
                                    <button @click="handleDownloadFile(attachment)"
                                            class="ml-3 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                        下载
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 无附件提示 -->
                    <div v-else class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">附件列表</h3>
                        <div class="bg-gray-50 rounded-lg p-8 text-center">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-gray-500">暂无附件</p>
                        </div>
                    </div>
                </div>

                <!-- 模态框底部 -->
                <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
                    <button @click="closeModal" 
                            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>

        <!-- 样品单模板组件 -->
        <SampleFormTemplate
            v-if="file && file.type === 'sample_form'"
            :sample-form="file"
            :visible="showSampleTemplate"
            @close="closeSampleTemplate">
        </SampleFormTemplate>
    `
};
