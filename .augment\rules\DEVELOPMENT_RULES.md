---
type: "always_apply"
---

# 开发规范文档 (Development Rules)

## 🎨 系统设计规范

### 主色调标准
**系统主色调**: `#2563EB` (蓝色)

**使用规范**:
- **主要按钮**: 使用主色调作为背景色
- **链接颜色**: 使用主色调或其变体
- **品牌标识**: 所有图标和Logo使用主色调
- **强调元素**: 重要信息和状态使用主色调
- **导航元素**: 活跃状态使用主色调

**色彩变体**:
- **主色调**: `#2563EB` (bg-blue-600, text-blue-600)
- **浅色变体**: `#3B82F6` (bg-blue-500) - 用于悬停状态
- **深色变体**: `#1D4ED8` (bg-blue-700) - 用于按下状态
- **超浅色**: `#DBEAFE` (bg-blue-100) - 用于背景和边框
- **文字色**: `#1E40AF` (text-blue-800) - 用于深色文字

**Tailwind CSS类名对照**:
```css
/* 主色调 */
bg-blue-600, text-blue-600, border-blue-600

/* 悬停状态 */
hover:bg-blue-500, hover:text-blue-500

/* 按下状态 */
active:bg-blue-700, focus:ring-blue-500

/* 背景和边框 */
bg-blue-100, border-blue-200

/* 深色文字 */
text-blue-800, text-blue-900
```

**禁止事项**:
- 不得随意使用其他主色调
- 不得使用与主色调冲突的颜色作为主要元素
- 保持整个系统的色彩一致性

## 📋 概述

本文档规定了项目开发过程中必须遵循的规范，确保代码质量和文档的及时更新。

## 🔧 代码完成后的必要步骤

### 1. 文档更新要求

每次完成代码开发后，**必须**按以下顺序更新相关文档：

#### 📖 README.md 更新
- **何时更新**: 每次添加新功能、修改现有功能、或修复重要bug后
- **更新内容**:
  - 项目描述和功能概述
  - 安装和运行说明
  - API文档或使用示例
  - 依赖项变更
  - 配置说明
  - 已知问题和限制

#### 🔧 func.md 更新
- **何时更新**: 每次代码提交后
- **更新内容**:
  - 新增系统功能详细说明
  - 功能模块的技术实现
  - 功能特性和使用方法
  - 功能间的依赖关系
  - 功能配置和参数说明
  - 功能的性能指标和限制

### 2. 更新流程

```mermaid
graph TD
    A[完成代码开发] --> B[运行测试]
    B --> C[测试通过?]
    C -->|否| D[修复问题]
    D --> B
    C -->|是| E[更新 README.md]
    E --> F[更新 func.md]
    F --> G[提交代码和文档]
    G --> H[完成]
```

### 3. 文档质量标准

#### README.md 标准
- ✅ 清晰的项目标题和描述
- ✅ 完整的安装步骤
- ✅ 详细的使用说明
- ✅ API文档（如适用）
- ✅ 贡献指南
- ✅ 许可证信息

#### func.md 标准
- ✅ 系统功能模块说明
- ✅ 功能实现技术细节
- ✅ 功能使用方法和示例
- ✅ 功能配置参数说明
- ✅ 功能性能指标
- ✅ 功能限制和注意事项

## 🚫 违规处理

### 轻微违规
- 忘记更新文档 → 立即补充更新
- 文档内容不完整 → 完善相关内容

### 严重违规
- 连续多次不更新文档 → 代码回滚，重新提交
- 文档与代码严重不符 → 全面审查和修正

## 📝 文档模板

### README.md 基础模板
```markdown
# 项目名称

## 描述
简要描述项目的目的和功能

## 安装
\`\`\`bash
npm install
\`\`\`

## 使用方法
\`\`\`bash
npm start
\`\`\`

## API文档
详细的API使用说明

## 贡献
如何为项目做贡献

## 许可证
项目许可证信息
```

### func.md 基础模板
```markdown
# 系统功能文档

## 最新更新 (日期)
### 🔧 新增功能
- 功能名称1：功能描述和用途
- 功能名称2：功能描述和用途

### 💡 技术实现
- 实现方案1：技术栈和架构说明
- 实现方案2：技术栈和架构说明

### � 功能配置
- 配置参数说明
- 使用方法和示例

### ⚠️ 注意事项
- 功能限制
- 使用注意事项
```

## 🔍 检查清单

在每次代码提交前，请确认：

- [ ] 代码功能正常运行
- [ ] 所有测试通过
- [ ] README.md 已更新
- [ ] func.md 已更新
- [ ] 文档内容准确无误
- [ ] 代码和文档版本一致


**记住**: 好的文档是好代码的一部分！📚✨

## 🔐 新建页面权限建立规则

### 核心规则

**每次添加新页面或功能模块时，必须遵循以下权限管理规则：**

#### 1. 权限定义规范
- **必须在两个组件中同步添加权限**:
  - `frontend/components/user/PermissionManager.js`
  - `frontend/components/user/PermissionTemplateManager.js`

- **权限ID命名规则**: `{功能模块}_{操作}` (小写+下划线)
  - 示例: `product_view`, `schedule_create`, `file_upload`

- **权限分组**: 相同功能模块的权限归入同一权限组
  - 权限组ID格式: `{功能模块}_management`

#### 2. 标准权限类型
每个功能模块通常包含：
- `{module}_view` - 查看权限
- `{module}_create` - 创建权限
- `{module}_edit` - 编辑权限
- `{module}_delete` - 删除权限
- `{module}_manage` - 管理权限

#### 3. 实施要求

**后端API保护**:
```javascript
router.get('/api/resource', authenticateJWT, checkPermission('module_permission'), controller.method);
```

**前端页面保护**:
```javascript
onMounted(() => {
  if (!checkPermission('module_permission')) {
    window.location.href = '/unauthorized';
  }
});
```

**UI元素控制**:
```html
<button v-if="hasPermission('module_create')">创建</button>
```

#### 4. 检查清单

**权限定义阶段**
- [ ] 在PermissionManager.js中添加权限定义
- [ ] 在PermissionTemplateManager.js中添加相同权限定义
- [ ] 权限ID遵循命名规范
- [ ] 权限分组合理

**实施阶段**
- [ ] API路由添加权限中间件
- [ ] 前端页面检查权限
- [ ] UI元素根据权限显示/隐藏

**测试阶段**
- [ ] 有权限用户能正常访问
- [ ] 无权限用户被正确拒绝
- [ ] 权限模板应用正确
