<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传客户二认文件 - 管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto"></div>
            <p class="mt-4 text-gray-600">正在根据您的权限跳转到相应页面...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="flex min-h-screen" style="display: none;">
        <!-- 侧边栏 -->
        <sidebar
            :user="currentUser"
            :sidebar-open="sidebarOpen"
        ></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 md:hidden">
                <div class="flex items-center justify-between px-4 py-3">
                    <button
                        @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-600 hover:text-gray-900 focus:outline-none"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">客户二认文件上传</h1>
                    <div class="w-6"></div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-4 md:p-6 lg:p-8">
                <div class="max-w-5xl mx-auto">
                    <!-- 页面标题和导航 -->
                    <div class="mb-6">
                        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
                            <a href="/file-upload" class="hover:text-blue-600">文件上传</a>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span class="text-gray-900">客户二认文件</span>
                        </nav>
                        <h1 class="text-2xl font-bold text-gray-800">上传客户二认文件</h1>
                        <p class="text-gray-600 mt-2">请填写客户信息和产品信息，上传相关的二认文件。系统将自动进行版本管理。</p>
                    </div>

                    <!-- 文件上传表单 -->
                    <file-upload-form></file-upload-form>
                </div>
            </main>
        </div>
    </div>

    <!-- 遮罩层（移动端侧边栏） -->
    <div
        v-if="sidebarOpen"
        @click="sidebarOpen = false"
        class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
    ></div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/file-management/certification.js"></script>
</body>
</html>
