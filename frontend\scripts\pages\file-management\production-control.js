/**
 * 生产控制表页面
 * 处理生产控制表的填写功能（占位页面）
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_upload'],
    onUserLoaded: async (user) => {
        console.log('生产控制表页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
