/**
 * 质量管理控制器
 * 处理检测报告相关的HTTP请求
 */

const qualityService = require('../services/qualityService');
const emailService = require('../services/emailService');
const emailQueueManager = require('../services/emailQueueManager');
const userService = require('../services/userService');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs');

/**
 * 创建检测报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function createReport(req, res) {
    try {
        const {
            title,
            description,
            testType,
            testDate,
            sampleInfo,
            testMethod,
            testStandard,
            testResult,
            conclusion,
            status,
            notifyUsers // 新增：要通知的用户ID列表
        } = req.body;

        // 验证必填字段
        if (!title || !testType || !testDate) {
            return res.status(400).json({
                success: false,
                message: '报告标题、检测类型和检测日期为必填项'
            });
        }

        // 验证日期格式
        if (isNaN(Date.parse(testDate))) {
            return res.status(400).json({
                success: false,
                message: '检测日期格式不正确'
            });
        }

        const reportData = {
            title,
            description,
            testType,
            testDate,
            sampleInfo,
            testMethod,
            testStandard,
            testResult,
            conclusion,
            status
        };

        // 获取上传的文件
        const files = req.files || [];

        const report = await qualityService.createReport(reportData, files, req.user.id);

        // 记录业务事件
        logger.logBusinessEvent('质量管理', '创建检测报告', {
            reportId: report.id,
            reportNumber: report.report_number,
            title: report.title,
            testType: report.test_type,
            fileCount: files.length,
            notifyUsersCount: notifyUsers ? notifyUsers.length : 0
        }, req.user);

        // 异步发送邮件通知（不影响响应速度）
        if (notifyUsers && notifyUsers.length > 0) {
            // 解析notifyUsers（可能是字符串格式的JSON数组）
            let userIds = [];
            try {
                if (typeof notifyUsers === 'string') {
                    userIds = JSON.parse(notifyUsers);
                } else if (Array.isArray(notifyUsers)) {
                    userIds = notifyUsers;
                }

                // 将邮件发送任务添加到队列（完全异步，不阻塞响应）
                emailQueueManager.addToQueue('quality_report', {
                    report: report,
                    uploader: req.user,
                    userIds: userIds
                });

                logger.info('质量管理邮件通知已添加到发送队列', {
                    reportId: report.id,
                    reportNumber: report.report_number,
                    recipientCount: userIds.length
                });
            } catch (parseError) {
                logger.error('解析通知用户列表失败', {
                    reportId: report.id,
                    notifyUsers: notifyUsers,
                    error: parseError.message
                });
            }
        }

        res.status(201).json({
            success: true,
            message: '检测报告创建成功',
            report
        });
    } catch (error) {
        logger.error('创建检测报告失败:', error);
        res.status(500).json({
            success: false,
            message: '创建检测报告失败: ' + error.message
        });
    }
}

/**
 * 获取检测报告列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getReports(req, res) {
    try {
        const {
            page = 1,
            limit = 10,
            testType,
            status,
            uploadedBy,
            startDate,
            endDate,
            search
        } = req.query;

        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            testType,
            status,
            uploadedBy,
            startDate,
            endDate,
            search
        };

        const result = await qualityService.getReports(options);

        res.json({
            success: true,
            data: result.reports,
            pagination: result.pagination
        });
    } catch (error) {
        logger.error('获取检测报告列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取检测报告列表失败: ' + error.message
        });
    }
}

/**
 * 获取检测报告详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getReportById(req, res) {
    try {
        const { id } = req.params;

        const report = await qualityService.getReportById(id);

        if (!report) {
            return res.status(404).json({
                success: false,
                message: '检测报告不存在'
            });
        }

        res.json({
            success: true,
            report
        });
    } catch (error) {
        logger.error('获取检测报告详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取检测报告详情失败: ' + error.message
        });
    }
}

/**
 * 根据报告编号获取检测报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getReportByNumber(req, res) {
    try {
        const { reportNumber } = req.params;

        const report = await qualityService.getReportByNumber(reportNumber);

        if (!report) {
            return res.status(404).json({
                success: false,
                message: '检测报告不存在'
            });
        }

        res.json({
            success: true,
            report
        });
    } catch (error) {
        logger.error('根据编号获取检测报告失败:', error);
        res.status(500).json({
            success: false,
            message: '获取检测报告失败: ' + error.message
        });
    }
}

/**
 * 更新检测报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function updateReport(req, res) {
    try {
        const { id } = req.params;
        const {
            title,
            description,
            testType,
            testDate,
            sampleInfo,
            testMethod,
            testStandard,
            testResult,
            conclusion,
            status
        } = req.body;

        // 验证必填字段
        if (!title || !testType || !testDate) {
            return res.status(400).json({
                success: false,
                message: '报告标题、检测类型和检测日期为必填项'
            });
        }

        // 验证日期格式
        if (isNaN(Date.parse(testDate))) {
            return res.status(400).json({
                success: false,
                message: '检测日期格式不正确'
            });
        }

        // 检查报告是否存在
        const existingReport = await qualityService.getReportById(id);
        if (!existingReport) {
            return res.status(404).json({
                success: false,
                message: '检测报告不存在'
            });
        }

        // 检查权限：只有报告创建者或系统管理员可以修改
        const isAdmin = req.user.role === 'admin';
        
        if (!isAdmin && existingReport.uploaded_by !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: '没有权限修改此检测报告'
            });
        }

        const updateData = {
            title,
            description,
            testType,
            testDate,
            sampleInfo,
            testMethod,
            testStandard,
            testResult,
            conclusion,
            status
        };

        const updatedReport = await qualityService.updateReport(id, updateData);

        if (!updatedReport) {
            return res.status(500).json({
                success: false,
                message: '更新检测报告失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('质量管理', '更新检测报告', {
            reportId: updatedReport.id,
            reportNumber: updatedReport.report_number,
            title: updatedReport.title
        }, req.user);

        res.json({
            success: true,
            message: '检测报告更新成功',
            report: updatedReport
        });
    } catch (error) {
        logger.error('更新检测报告失败:', error);
        res.status(500).json({
            success: false,
            message: '更新检测报告失败: ' + error.message
        });
    }
}

/**
 * 删除检测报告
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteReport(req, res) {
    try {
        const { id } = req.params;

        // 检查报告是否存在
        const existingReport = await qualityService.getReportById(id);
        if (!existingReport) {
            return res.status(404).json({
                success: false,
                message: '检测报告不存在'
            });
        }

        // 检查权限：只有报告创建者或系统管理员可以删除
        const isSystemAdmin = req.user.role === 'admin' || req.user.role === '管理员';
        const hasQualityManagePermission = req.user.permissions && req.user.permissions.includes('quality_manage');

        if (!isSystemAdmin && !hasQualityManagePermission && existingReport.uploaded_by !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: '没有权限删除此检测报告'
            });
        }

        const success = await qualityService.deleteReport(id);

        if (!success) {
            return res.status(500).json({
                success: false,
                message: '删除检测报告失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('质量管理', '删除检测报告', {
            reportId: existingReport.id,
            reportNumber: existingReport.report_number,
            title: existingReport.title
        }, req.user);

        res.json({
            success: true,
            message: '检测报告删除成功'
        });
    } catch (error) {
        logger.error('删除检测报告失败:', error);
        res.status(500).json({
            success: false,
            message: '删除检测报告失败: ' + error.message
        });
    }
}

/**
 * 下载检测报告文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function downloadFile(req, res) {
    try {
        const { fileId } = req.params;

        const file = await qualityService.getFileById(fileId);

        if (!file) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 检查文件是否存在于磁盘
        if (!fs.existsSync(file.file_path)) {
            logger.error('文件不存在:', {
                fileId: file.id,
                filePath: file.file_path,
                originalName: file.original_filename
            });
            return res.status(404).json({
                success: false,
                message: '文件已丢失'
            });
        }

        // 检查文件大小是否匹配
        const stats = fs.statSync(file.file_path);
        if (stats.size !== file.file_size) {
            logger.warn('文件大小不匹配:', {
                fileId: file.id,
                expectedSize: file.file_size,
                actualSize: stats.size,
                filePath: file.file_path
            });
        }

        // 记录下载事件
        logger.logBusinessEvent('质量管理', '下载检测报告文件', {
            fileId: file.id,
            fileName: file.original_filename,
            reportNumber: file.report_number
        }, req.user);

        // 设置响应头 - 正确处理中文文件名
        const encodedFilename = encodeURIComponent(file.original_filename);
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

        // 根据文件扩展名设置正确的MIME类型
        const fileExtension = path.extname(file.original_filename).toLowerCase();
        let mimeType = file.mime_type;

        // 确保MIME类型正确
        const mimeTypeMap = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.csv': 'text/csv'
        };

        if (mimeTypeMap[fileExtension]) {
            mimeType = mimeTypeMap[fileExtension];
        }

        res.setHeader('Content-Type', mimeType);
        res.setHeader('Content-Length', stats.size); // 使用实际文件大小

        // 发送文件
        const fileStream = fs.createReadStream(file.file_path);

        // 处理流错误
        fileStream.on('error', (error) => {
            logger.error('文件流读取错误:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    success: false,
                    message: '文件读取失败'
                });
            }
        });

        // 处理响应结束
        res.on('close', () => {
            fileStream.destroy();
        });

        fileStream.pipe(res);
    } catch (error) {
        logger.error('下载检测报告文件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载文件失败: ' + error.message
        });
    }
}

module.exports = {
    createReport,
    getReports,
    getReportById,
    getReportByNumber,
    updateReport,
    deleteReport,
    downloadFile
};
