/**
 * 邮件服务
 * 处理邮件发送相关的业务逻辑
 */

const nodemailer = require('nodemailer');
const config = require('../config');
const logger = require('../utils/logger');
const userService = require('./userService');

/**
 * 创建邮件传输器
 * @returns {Object} nodemailer传输器
 */
function createTransporter() {
    try {
        const transporter = nodemailer.createTransport({
            service: config.email.service,
            auth: config.email.auth
        });
        return transporter;
    } catch (error) {
        logger.error('创建邮件传输器失败', { error: error.message });
        throw error;
    }
}

/**
 * 验证邮件配置
 * @returns {Promise<boolean>} 验证结果
 */
async function verifyEmailConfig() {
    try {
        const transporter = createTransporter();
        await transporter.verify();
        logger.info('邮件配置验证成功');
        return true;
    } catch (error) {
        logger.error('邮件配置验证失败', { error: error.message });
        return false;
    }
}

/**
 * 发送邮件（带无限重试机制）
 * @param {Object} mailOptions - 邮件选项
 * @param {number} attempt - 当前尝试次数
 * @returns {Promise<Object>} 发送结果
 */
async function sendEmailWithRetry(mailOptions, attempt = 1) {
    const transporter = createTransporter();

    try {
        const info = await transporter.sendMail(mailOptions);

        // 只在首次成功或重试成功时记录日志
        if (attempt === 1) {
            logger.info('邮件发送成功', {
                messageId: info.messageId,
                to: mailOptions.to,
                subject: mailOptions.subject
            });
        } else {
            logger.info('邮件重试发送成功', {
                messageId: info.messageId,
                to: mailOptions.to,
                subject: mailOptions.subject,
                retriedAfter: attempt - 1
            });
        }

        return { success: true, messageId: info.messageId };
    } catch (error) {
        // 记录重试日志
        if (attempt === 1) {
            logger.error('邮件发送失败，开始无限重试直到成功', {
                error: error.message,
                to: mailOptions.to,
                subject: mailOptions.subject
            });
        } else if (attempt % 10 === 0) {
            // 每10次重试记录一次日志，避免日志过多
            logger.warn('邮件发送持续重试中', {
                error: error.message,
                to: mailOptions.to,
                subject: mailOptions.subject,
                attempts: attempt
            });
        }

        // 计算递增的重试间隔（最大30秒）
        const multiplier = Math.min(Math.ceil(attempt / 5), 10); // 最大10倍
        const delay = Math.min(config.email.retry.delay * multiplier, 30000);

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay));
        return sendEmailWithRetry(mailOptions, attempt + 1);
    }
}

/**
 * 根据角色获取用户邮箱列表
 * @param {string|Array} roles - 角色或角色数组
 * @returns {Array} 邮箱地址列表
 */
function getEmailsByRole(roles) {
    try {
        const roleArray = Array.isArray(roles) ? roles : [roles];
        const users = userService.getAllUsers();

        const emails = users
            .filter(user => {
                // 检查用户是否活跃
                if (!user.active) return false;

                // 检查邮箱是否存在且不为空
                if (!user.email || user.email.trim() === '') return false;

                // 检查角色是否匹配（支持中文角色和英文角色）
                // 排除管理员，管理员不参与审批流程
                return roleArray.some(role => {
                    const userRole = user.role.toLowerCase();
                    const targetRole = role.toLowerCase();

                    // 排除管理员角色
                    if (userRole === 'admin' || user.role === '管理员') {
                        return false;
                    }

                    // 直接匹配
                    if (userRole === targetRole) return true;

                    // 中英文角色映射匹配
                    const roleMapping = {
                        'factory_manager': '厂长',
                        'director': '总监',
                        'manager': '经理',
                        'ceo': 'CEO',
                        'readonly': '只读用户'
                        // 注意：不包含admin/管理员，因为管理员不参与审批
                    };

                    // 检查英文角色对应的中文角色
                    if (roleMapping[targetRole] && userRole === roleMapping[targetRole].toLowerCase()) return true;

                    // 检查中文角色对应的英文角色
                    const reverseMapping = Object.entries(roleMapping).find(([, value]) => value === user.role);
                    if (reverseMapping && targetRole === reverseMapping[0]) return true;

                    return false;
                });
            })
            .map(user => user.email);

        return emails;
    } catch (error) {
        logger.error('获取角色邮箱列表失败', { error: error.message, roles });
        return [];
    }
}

/**
 * 根据用户ID列表获取邮箱列表
 * @param {Array} userIds - 用户ID数组
 * @returns {Array} 邮箱地址列表
 */
function getEmailsByUserIds(userIds) {
    try {
        if (!Array.isArray(userIds) || userIds.length === 0) {
            return [];
        }

        const emails = [];
        for (const userId of userIds) {
            const user = userService.getUserById(userId);
            if (user && user.active && user.email && user.email.trim() !== '') {
                emails.push(user.email);
            }
        }

        return emails;
    } catch (error) {
        logger.error('根据用户ID获取邮箱列表失败', { error: error.message, userIds });
        return [];
    }
}

/**
 * 获取只读用户邮箱列表
 * @returns {Array} 只读用户邮箱地址列表
 */
function getReadOnlyUserEmails() {
    try {
        const users = userService.getAllUsers();
        const readOnlyEmails = users
            .filter(user => user.active && user.role.toLowerCase() === 'readonly' && user.email && user.email.trim() !== '')
            .map(user => user.email);

        return readOnlyEmails;
    } catch (error) {
        logger.error('获取只读用户邮箱列表失败', { error: error.message });
        return [];
    }
}

/**
 * 替换邮件模板变量
 * @param {string} template - 模板字符串
 * @param {Object} variables - 变量对象
 * @returns {string} 替换后的字符串
 */
function replaceTemplateVariables(template, variables) {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`{${key}}`, 'g');
        result = result.replace(regex, value || '');
    }
    return result;
}

/**
 * 生成邮件内容
 * @param {string} type - 邮件类型
 * @param {Object} application - 申请对象
 * @param {Object} approver - 审批人信息
 * @param {string} comment - 审批意见
 * @returns {Object} 邮件内容
 */
function generateEmailContent(type, application, approver = null, comment = '') {
    const variables = {
        applicationNumber: application.applicationNumber,
        applicant: application.applicant,
        department: application.department,
        content: application.content,
        amount: application.amount || '未填写',
        priority: application.priority === 'urgent' ? '紧急' :
                 application.priority === 'medium' ? '中等' : '普通',
        date: application.date,
        approverName: approver ? approver.username : '',
        approverRole: approver ? approver.role : '',
        comment: comment,
        currentStage: getStageDisplayName(application.currentStage),
        status: getStatusDisplayName(application.status)
    };

    let subject = '';
    let htmlContent = '';

    switch (type) {
        case 'submitted':
            subject = replaceTemplateVariables(config.email.templates.subject.submitted, variables);
            htmlContent = generateSubmittedEmailHtml(variables);
            break;
        case 'approved':
            subject = replaceTemplateVariables(config.email.templates.subject.approved, variables);
            htmlContent = generateApprovedEmailHtml(variables);
            break;
        case 'rejected':
            subject = replaceTemplateVariables(config.email.templates.subject.rejected, variables);
            htmlContent = generateRejectedEmailHtml(variables);
            break;
        case 'completed':
            subject = replaceTemplateVariables(config.email.templates.subject.completed, variables);
            htmlContent = generateCompletedEmailHtml(variables);
            break;
        case 'highAmount':
            subject = replaceTemplateVariables(config.email.templates.subject.highAmount, variables);
            htmlContent = generateHighAmountEmailHtml(variables);
            break;
        default:
            throw new Error(`未知的邮件类型: ${type}`);
    }

    return { subject, html: htmlContent };
}

/**
 * 生成文件管理邮件内容
 * @param {Object} fileRecord - 文件记录对象
 * @param {Object} uploader - 上传人信息
 * @returns {Object} 邮件内容
 */
function generateFileManagementEmailContent(fileRecord, uploader) {
    const isFirstVersion = fileRecord.is_first_version;
    const subject = isFirstVersion
        ? `【文件管理】新文件上传通知 - ${fileRecord.title}`
        : `【文件管理】文件更新通知 - ${fileRecord.title}`;

    const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2563eb; margin: 0; font-size: 24px;">
                        ${isFirstVersion ? '📁 新文件上传通知' : '🔄 文件更新通知'}
                    </h1>
                </div>

                <div style="background-color: #f8fafc; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                    <h2 style="color: #1e40af; margin-top: 0; font-size: 18px;">文件信息</h2>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151; width: 120px;">文件编号：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${fileRecord.file_number}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">文件标题：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${fileRecord.title}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">客户名称：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${fileRecord.customer_name}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">产品型号：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${fileRecord.product_model}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">版本号：</td>
                            <td style="padding: 8px 0; color: #6b7280;">V${fileRecord.version}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">上传人：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${uploader.username}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold; color: #374151;">上传时间：</td>
                            <td style="padding: 8px 0; color: #6b7280;">${new Date(fileRecord.uploaded_at).toLocaleString('zh-CN')}</td>
                        </tr>
                    </table>
                </div>

                ${fileRecord.description ? `
                <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <h3 style="color: #0369a1; margin-top: 0; font-size: 16px;">文件描述</h3>
                    <p style="margin: 0; color: #374151; line-height: 1.5;">${fileRecord.description}</p>
                </div>
                ` : ''}

                ${!isFirstVersion && fileRecord.change_description ? `
                <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <h3 style="color: #d97706; margin-top: 0; font-size: 16px;">变更内容</h3>
                    <p style="margin: 0; color: #374151; line-height: 1.5;">${fileRecord.change_description}</p>
                </div>
                ` : ''}

                <div style="background-color: #fef2f2; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <h3 style="color: #dc2626; margin-top: 0; font-size: 16px;">⚠️ 重要提醒</h3>
                    <p style="margin: 0; color: #374151; line-height: 1.5;">
                        请及时登录系统确认收到此文件${isFirstVersion ? '' : '变更'}通知，并下载查看相关文件。
                    </p>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/file-management/notifications"
                       style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                        立即查看并确认
                    </a>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                    <p>如有疑问，请联系系统管理员。</p>
                </div>
            </div>
        </div>
    `;

    return { subject, html: htmlContent };
}

/**
 * 生成文件管理确认完成邮件内容
 * @param {Object} uploaderInfo - 上传者信息
 * @returns {Object} 邮件内容
 */
function generateAllConfirmedEmailContent(uploaderInfo) {
    const subject = `【文件管理】所有用户已确认通知 - ${uploaderInfo.title}`;

    const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #059669; margin: 0; font-size: 24px;">
                        ✅ 所有用户已确认通知
                    </h1>
                </div>

                <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin-bottom: 20px;">
                    <h3 style="color: #059669; margin: 0 0 15px 0;">文件信息</h3>
                    <p style="margin: 5px 0;"><strong>文件编号：</strong>${uploaderInfo.file_number}</p>
                    <p style="margin: 5px 0;"><strong>文件标题：</strong>${uploaderInfo.title}</p>
                    <p style="margin: 5px 0;"><strong>上传人员：</strong>${uploaderInfo.username}</p>
                </div>

                <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #374151; margin: 0 0 15px 0;">确认状态</h3>
                    <p style="margin: 5px 0; color: #059669; font-weight: bold;">✅ 所有被通知的用户已确认了解此文件</p>
                    <p style="margin: 5px 0; color: #6b7280;">确认时间：${new Date().toLocaleString('zh-CN')}</p>
                </div>

                <div style="background-color: #eff6ff; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <p style="margin: 0; color: #1e40af;">
                        <strong>📋 说明：</strong>您上传的文件已经被所有相关人员确认了解，文件通知流程已完成。
                    </p>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                    <p>如有疑问，请联系系统管理员。</p>
                </div>
            </div>
        </div>
    `;

    return { subject, html: htmlContent };
}

/**
 * 生成质量管理邮件内容
 * @param {Object} report - 检测报告对象
 * @param {Object} uploader - 上传人信息
 * @returns {Object} 邮件内容
 */
function generateQualityReportEmailContent(report, uploader) {
    const variables = {
        reportNumber: report.report_number,
        reportTitle: report.title,
        testType: report.test_type,
        testDate: report.test_date,
        uploader: uploader.username,
        uploaderDepartment: uploader.department || '未填写',
        uploadDate: new Date(report.created_at).toLocaleString('zh-CN'),
        conclusion: report.conclusion || '详见报告内容',
        status: report.status === 'published' ? '已发布' : '草稿'
    };

    const subject = `检测报告上传通知 - ${variables.reportNumber}`;
    const htmlContent = generateQualityReportEmailHtml(variables);

    return { subject, html: htmlContent };
}

/**
 * 获取阶段显示名称
 * @param {string} stage - 阶段代码
 * @returns {string} 显示名称
 */
function getStageDisplayName(stage) {
    const stageMap = {
        'factory_manager': '厂长审批',
        'director': '总监审批',
        'manager': '经理审批',
        'ceo': 'CEO审批',
        'completed': '已完成',
        'rejected': '已拒绝'
    };
    return stageMap[stage] || stage;
}

/**
 * 获取状态显示名称
 * @param {string} status - 状态代码
 * @returns {string} 显示名称
 */
function getStatusDisplayName(status) {
    const statusMap = {
        'pending': '待审批',
        'approved': '已通过',
        'rejected': '已拒绝'
    };
    return statusMap[status] || status;
}

/**
 * 生成申请提交邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateSubmittedEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">新申请提交通知</h2>
            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>申请信息</h3>
                <p><strong>申请编号：</strong>${variables.applicationNumber}</p>
                <p><strong>申请人：</strong>${variables.applicant}</p>
                <p><strong>部门：</strong>${variables.department}</p>
                <p><strong>申请日期：</strong>${variables.date}</p>
                <p><strong>申请金额：</strong>${variables.amount}</p>
                <p><strong>优先级：</strong>${variables.priority}</p>
                <p><strong>申请内容：</strong></p>
                <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                    ${variables.content}
                </div>
            </div>
            <p style="color: #64748b;">请登录系统查看详细信息并进行审批。</p>
        </div>
    `;
}

/**
 * 生成审批通过邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateApprovedEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #059669;">申请审批通过通知</h2>
            <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>申请信息</h3>
                <p><strong>申请编号：</strong>${variables.applicationNumber}</p>
                <p><strong>申请人：</strong>${variables.applicant}</p>
                <p><strong>当前阶段：</strong>${variables.currentStage}</p>
                <p><strong>审批人：</strong>${variables.approverName} (${variables.approverRole})</p>
                ${variables.comment ? `<p><strong>审批意见：</strong>${variables.comment}</p>` : ''}
            </div>
            <p style="color: #64748b;">申请已进入下一审批阶段，请相关人员及时处理。</p>
        </div>
    `;
}

/**
 * 生成审批拒绝邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateRejectedEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">申请审批拒绝通知</h2>
            <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>申请信息</h3>
                <p><strong>申请编号：</strong>${variables.applicationNumber}</p>
                <p><strong>申请人：</strong>${variables.applicant}</p>
                <p><strong>拒绝人：</strong>${variables.approverName} (${variables.approverRole})</p>
                <p><strong>拒绝原因：</strong></p>
                <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                    ${variables.comment || '无'}
                </div>
            </div>
            <p style="color: #64748b;">申请已被拒绝，流程结束。如有疑问请联系审批人。</p>
        </div>
    `;
}

/**
 * 生成审批完成邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateCompletedEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #059669;">申请审批完成通知</h2>
            <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>申请信息</h3>
                <p><strong>申请编号：</strong>${variables.applicationNumber}</p>
                <p><strong>申请人：</strong>${variables.applicant}</p>
                <p><strong>部门：</strong>${variables.department}</p>
                <p><strong>申请金额：</strong>${variables.amount}</p>
                <p><strong>最终审批人：</strong>${variables.approverName} (${variables.approverRole})</p>
                ${variables.comment ? `<p><strong>最终审批意见：</strong>${variables.comment}</p>` : ''}
            </div>
            <p style="color: #64748b;">恭喜！您的申请已通过所有审批流程，请登录系统查看详细信息。</p>
        </div>
    `;
}

/**
 * 生成高金额申请邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateHighAmountEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #d97706;">高金额申请通知</h2>
            <div style="background-color: #fffbeb; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>申请信息</h3>
                <p><strong>申请编号：</strong>${variables.applicationNumber}</p>
                <p><strong>申请人：</strong>${variables.applicant}</p>
                <p><strong>部门：</strong>${variables.department}</p>
                <p><strong>申请金额：</strong><span style="color: #dc2626; font-weight: bold;">${variables.amount}</span></p>
                <p><strong>申请内容：</strong></p>
                <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                    ${variables.content}
                </div>
            </div>
            <p style="color: #64748b;">此申请金额超过100,000，请特别关注。</p>
        </div>
    `;
}

/**
 * 生成质量管理检测报告邮件HTML内容
 * @param {Object} variables - 变量对象
 * @returns {string} HTML内容
 */
function generateQualityReportEmailHtml(variables) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #059669;">检测报告上传通知</h2>
            <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>检测报告信息</h3>
                <p><strong>报告编号：</strong>${variables.reportNumber}</p>
                <p><strong>报告标题：</strong>${variables.reportTitle}</p>
                <p><strong>检测类型：</strong>${variables.testType}</p>
                <p><strong>检测日期：</strong>${variables.testDate}</p>
                <p><strong>上传人员：</strong>${variables.uploader}</p>
                <p><strong>上传部门：</strong>${variables.uploaderDepartment}</p>
                <p><strong>上传时间：</strong>${variables.uploadDate}</p>
                <p><strong>报告状态：</strong><span style="color: #059669; font-weight: bold;">${variables.status}</span></p>
                <p><strong>检测结论：</strong></p>
                <div style="background-color: white; padding: 15px; border-radius: 4px; margin-top: 10px; border-left: 4px solid #059669;">
                    ${variables.conclusion}
                </div>
            </div>
            <div style="background-color: #e0f2fe; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; color: #0369a1;">
                    <strong>📋 温馨提示：</strong>新的检测报告已上传完成，请及时查看相关内容。您可以登录系统查看完整的检测报告和相关附件。
                </p>
            </div>
        </div>
    `;
}

/**
 * 发送申请提交通知邮件
 * @param {Object} application - 申请对象
 * @returns {Promise<Object>} 发送结果
 */
async function sendApplicationSubmittedNotification(application) {
    try {
        let recipients = [];
        let recipientType = '';

        // 根据申请类型和当前阶段确定收件人
        if (application.type === 'standard') {
            // 标准申请：通知选中的厂长
            if (application.selectedFactoryManagers && application.selectedFactoryManagers.length > 0) {
                const selectedManagerIds = application.selectedFactoryManagers.map(manager => manager.id);
                recipients = getEmailsByUserIds(selectedManagerIds);
                recipientType = '选中的厂长';

                logger.info('标准申请通知选中的厂长', {
                    applicationId: application.id,
                    selectedManagers: application.selectedFactoryManagers.length,
                    emailsFound: recipients.length
                });
            }
        } else {
            // 其他申请：根据当前阶段通知对应审批人
            switch (application.currentStage) {
                case 'director':
                    recipients = getEmailsByRole(['director', '总监']);
                    recipientType = '总监';
                    logger.info('其他申请通知总监', {
                        applicationId: application.id,
                        emailsFound: recipients.length
                    });
                    break;
                case 'ceo':
                    recipients = getEmailsByRole(['ceo', 'CEO']);
                    recipientType = 'CEO';
                    logger.info('其他申请通知CEO', {
                        applicationId: application.id,
                        emailsFound: recipients.length
                    });
                    break;
                default:
                    logger.warn('其他申请的当前阶段不正确', {
                        applicationId: application.id,
                        currentStage: application.currentStage
                    });
                    break;
            }
        }

        if (recipients.length === 0) {
            logger.warn('没有找到收件人邮箱，跳过邮件通知', {
                applicationId: application.id,
                applicationType: application.type,
                currentStage: application.currentStage,
                recipientType: recipientType
            });
            return { success: true, message: '没有收件人' };
        }

        const emailContent = generateEmailContent('submitted', application);

        const mailOptions = {
            from: config.email.from,
            to: recipients.join(','),
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);
        logger.info('申请提交通知邮件发送成功', {
            applicationId: application.id,
            applicationType: application.type,
            recipientType: recipientType,
            recipients: recipients.length,
            emails: recipients
        });

        return result;
    } catch (error) {
        logger.error('发送申请提交通知邮件失败', {
            error: error.message,
            applicationId: application.id
        });
        throw error;
    }
}

/**
 * 发送审批状态变更通知邮件
 * @param {Object} application - 申请对象
 * @param {Object} approver - 审批人信息
 * @param {string} comment - 审批意见
 * @returns {Promise<Object>} 发送结果
 */
async function sendApprovalStatusNotification(application, approver, comment = '') {
    try {
        let recipients = [];
        let emailType = 'approved';

        // 根据当前阶段确定收件人
        switch (application.currentStage) {
            case 'factory_manager':
                // 厂长审批阶段：通知还未审批的厂长
                if (application.selectedFactoryManagers && application.selectedFactoryManagers.length > 0) {
                    // 获取已审批的厂长ID列表
                    const approvedFactoryManagerIds = [];
                    if (application.approvalHistory && application.approvalHistory.length > 0) {
                        application.approvalHistory
                            .filter(history => history.stage === 'factory_manager' && history.action === 'approve')
                            .forEach(approval => {
                                if (!approvedFactoryManagerIds.includes(approval.approverId)) {
                                    approvedFactoryManagerIds.push(approval.approverId);
                                }
                            });
                    }

                    // 获取还未审批的厂长ID列表
                    const pendingFactoryManagerIds = application.selectedFactoryManagers
                        .map(manager => manager.id)
                        .filter(managerId => !approvedFactoryManagerIds.includes(managerId));

                    if (pendingFactoryManagerIds.length > 0) {
                        recipients = getEmailsByUserIds(pendingFactoryManagerIds);
                        logger.info('通知还未审批的厂长', {
                            applicationId: application.id,
                            totalSelectedFactoryManagers: application.selectedFactoryManagers.length,
                            approvedFactoryManagers: approvedFactoryManagerIds.length,
                            pendingFactoryManagers: pendingFactoryManagerIds.length,
                            emailsFound: recipients.length
                        });
                    } else {
                        logger.info('所有厂长已审批完成，无需发送邮件', {
                            applicationId: application.id,
                            totalSelectedFactoryManagers: application.selectedFactoryManagers.length
                        });
                    }
                } else {
                    // 兼容旧数据：如果没有选中厂长信息，通知所有厂长
                    recipients = getEmailsByRole(['factory_manager', '厂长']);
                    logger.info('通知所有厂长（兼容旧数据）', {
                        applicationId: application.id,
                        emailsFound: recipients.length
                    });
                }
                break;
            case 'director':
                recipients = getEmailsByRole(['director', '总监']);
                break;
            case 'manager':
                // 经理审批阶段：通知还未审批的经理
                if (application.selectedManagers && application.selectedManagers.length > 0) {
                    // 获取已审批的经理ID列表
                    const approvedManagerIds = [];
                    if (application.approvalHistory && application.approvalHistory.length > 0) {
                        application.approvalHistory
                            .filter(history => history.stage === 'manager' && history.action === 'approve')
                            .forEach(approval => {
                                if (!approvedManagerIds.includes(approval.approverId)) {
                                    approvedManagerIds.push(approval.approverId);
                                }
                            });
                    }

                    // 获取还未审批的经理ID列表
                    const pendingManagerIds = application.selectedManagers
                        .map(manager => manager.id)
                        .filter(managerId => !approvedManagerIds.includes(managerId));

                    if (pendingManagerIds.length > 0) {
                        recipients = getEmailsByUserIds(pendingManagerIds);
                        logger.info('通知还未审批的经理', {
                            applicationId: application.id,
                            totalSelectedManagers: application.selectedManagers.length,
                            approvedManagers: approvedManagerIds.length,
                            pendingManagers: pendingManagerIds.length,
                            emailsFound: recipients.length
                        });
                    } else {
                        logger.info('所有经理已审批完成，无需发送邮件', {
                            applicationId: application.id,
                            totalSelectedManagers: application.selectedManagers.length
                        });
                    }
                } else {
                    // 兼容旧数据：如果没有选中经理信息，通知所有经理
                    recipients = getEmailsByRole(['manager', '经理']);
                    logger.info('通知所有经理（兼容旧数据）', {
                        applicationId: application.id,
                        emailsFound: recipients.length
                    });
                }
                break;
            case 'ceo':
                recipients = getEmailsByRole(['ceo', 'CEO']);
                break;
            case 'completed':
                // 审批完成，通知申请人
                const applicantUser = userService.getUserById(application.userId);
                if (applicantUser && applicantUser.email && applicantUser.email.trim() !== '') {
                    recipients = [applicantUser.email];
                    emailType = 'completed';

                    // 记录完成方式
                    if (application.type !== 'standard' && !application.needCeoApproval) {
                        logger.info('申请由总监直接完成', {
                            applicationId: application.id,
                            applicationType: application.type
                        });
                    }
                }
                break;
        }

        if (recipients.length === 0) {
            logger.warn('没有找到收件人邮箱，跳过邮件通知', {
                applicationId: application.id,
                stage: application.currentStage
            });
            return { success: true, message: '没有收件人' };
        }

        const emailContent = generateEmailContent(emailType, application, approver, comment);

        const mailOptions = {
            from: config.email.from,
            to: recipients.join(','),
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);
        logger.info('审批状态变更通知邮件发送成功', {
            applicationId: application.id,
            stage: application.currentStage,
            recipients: recipients.length
        });

        return result;
    } catch (error) {
        logger.error('发送审批状态变更通知邮件失败', {
            error: error.message,
            applicationId: application.id
        });
        throw error;
    }
}

/**
 * 发送申请拒绝通知邮件
 * @param {Object} application - 申请对象
 * @param {Object} approver - 审批人信息
 * @param {string} comment - 拒绝原因
 * @returns {Promise<Object>} 发送结果
 */
async function sendApplicationRejectedNotification(application, approver, comment = '') {
    try {
        // 通知申请人（包括管理员申请人）
        const applicantUser = userService.getUserById(application.userId);
        if (!applicantUser || !applicantUser.email || applicantUser.email.trim() === '') {
            logger.warn('申请人邮箱不存在，跳过邮件通知', {
                applicationId: application.id,
                applicantId: application.userId,
                applicantRole: applicantUser?.role || 'unknown'
            });
            return { success: true, message: '申请人邮箱不存在' };
        }

        const emailContent = generateEmailContent('rejected', application, approver, comment);

        const mailOptions = {
            from: config.email.from,
            to: applicantUser.email,
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);
        logger.info('申请拒绝通知邮件发送成功', {
            applicationId: application.id,
            applicant: applicantUser.email
        });

        return result;
    } catch (error) {
        logger.error('发送申请拒绝通知邮件失败', {
            error: error.message,
            applicationId: application.id
        });
        throw error;
    }
}

/**
 * 发送高金额申请通知邮件
 * @param {Object} application - 申请对象
 * @returns {Promise<Object>} 发送结果
 */
async function sendHighAmountNotification(application) {
    try {
        // 获取只读用户邮箱列表
        const readOnlyEmails = getReadOnlyUserEmails();

        if (readOnlyEmails.length === 0) {
            logger.warn('没有找到只读用户邮箱，跳过高金额通知', { applicationId: application.id });
            return { success: true, message: '没有只读用户' };
        }

        const emailContent = generateEmailContent('highAmount', application);

        const mailOptions = {
            from: config.email.from,
            to: readOnlyEmails.join(','),
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);
        logger.info('高金额申请通知邮件发送成功', {
            applicationId: application.id,
            recipients: readOnlyEmails.length
        });

        return result;
    } catch (error) {
        logger.error('发送高金额申请通知邮件失败', {
            error: error.message,
            applicationId: application.id
        });
        throw error;
    }
}

/**
 * 发送文件管理邮件通知
 * @param {Object} fileRecord - 文件记录对象
 * @param {Object} uploader - 上传人信息
 * @param {Array} notifyUserIds - 要通知的用户ID列表
 * @returns {Promise<Object>} 发送结果
 */
async function sendFileManagementNotification(fileRecord, uploader, notifyUserIds) {
    try {
        // 如果没有指定通知用户，直接返回成功
        if (!notifyUserIds || notifyUserIds.length === 0) {
            logger.info('没有指定通知用户，跳过文件管理邮件通知', {
                fileId: fileRecord.id,
                fileNumber: fileRecord.file_number
            });
            return { success: true, message: '没有指定通知用户' };
        }

        // 获取要通知的用户邮箱列表
        const recipients = getEmailsByUserIds(notifyUserIds);

        if (recipients.length === 0) {
            logger.warn('没有找到有效的收件人邮箱，跳过文件管理邮件通知', {
                fileId: fileRecord.id,
                fileNumber: fileRecord.file_number,
                requestedUserIds: notifyUserIds
            });
            return { success: true, message: '没有有效的收件人邮箱' };
        }

        const emailContent = generateFileManagementEmailContent(fileRecord, uploader);

        const mailOptions = {
            from: config.email.from,
            to: recipients.join(','),
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);

        logger.info('文件管理邮件通知发送成功', {
            fileId: fileRecord.id,
            fileNumber: fileRecord.file_number,
            recipientCount: recipients.length,
            messageId: result.messageId
        });

        return {
            success: true,
            message: '邮件通知发送成功',
            recipientCount: recipients.length,
            messageId: result.messageId
        };

    } catch (error) {
        logger.error('发送文件管理邮件通知失败', {
            fileId: fileRecord?.id,
            fileNumber: fileRecord?.file_number,
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * 发送质量管理检测报告通知邮件
 * @param {Object} report - 检测报告对象
 * @param {Object} uploader - 上传人信息
 * @param {Array} notifyUserIds - 要通知的用户ID列表
 * @returns {Promise<Object>} 发送结果
 */
async function sendQualityReportNotification(report, uploader, notifyUserIds) {
    try {
        // 如果没有指定通知用户，直接返回成功
        if (!notifyUserIds || notifyUserIds.length === 0) {
            logger.info('没有指定通知用户，跳过质量管理邮件通知', {
                reportId: report.id,
                reportNumber: report.report_number
            });
            return { success: true, message: '没有指定通知用户' };
        }

        // 获取要通知的用户邮箱列表
        const recipients = getEmailsByUserIds(notifyUserIds);

        if (recipients.length === 0) {
            logger.warn('没有找到有效的收件人邮箱，跳过质量管理邮件通知', {
                reportId: report.id,
                reportNumber: report.report_number,
                requestedUserIds: notifyUserIds
            });
            return { success: true, message: '没有有效的收件人邮箱' };
        }

        const emailContent = generateQualityReportEmailContent(report, uploader);

        const mailOptions = {
            from: config.email.from,
            to: recipients.join(','),
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);
        logger.info('质量管理检测报告通知邮件发送成功', {
            reportId: report.id,
            reportNumber: report.report_number,
            recipients: recipients.length,
            emails: recipients
        });

        return result;
    } catch (error) {
        logger.error('发送质量管理检测报告通知邮件失败', {
            error: error.message,
            reportId: report.id,
            reportNumber: report.report_number
        });
        throw error;
    }
}

/**
 * 发送所有用户已确认的通知给文件提交者
 * @param {Object} uploaderInfo - 上传者信息
 * @returns {Promise<Object>} 发送结果
 */
async function sendAllConfirmedNotification(uploaderInfo) {
    try {
        // 检查上传者邮箱是否存在
        if (!uploaderInfo.email || uploaderInfo.email.trim() === '') {
            logger.warn('上传者邮箱不存在，跳过确认完成通知', {
                uploaderId: uploaderInfo.uploaded_by,
                uploaderName: uploaderInfo.username
            });
            return { success: true, message: '上传者邮箱不存在' };
        }

        const emailContent = generateAllConfirmedEmailContent(uploaderInfo);

        const mailOptions = {
            from: config.email.from,
            to: uploaderInfo.email,
            subject: emailContent.subject,
            html: emailContent.html
        };

        const result = await sendEmailWithRetry(mailOptions);

        logger.info('文件确认完成通知发送成功', {
            fileNumber: uploaderInfo.file_number,
            uploaderEmail: uploaderInfo.email,
            messageId: result.messageId
        });

        return result;
    } catch (error) {
        logger.error('发送文件确认完成通知失败', {
            error: error.message,
            fileNumber: uploaderInfo.file_number,
            uploaderEmail: uploaderInfo.email
        });
        throw error;
    }
}

/**
 * 发送样品单通知邮件
 * @param {Object} emailData - 邮件数据
 * @param {Object} emailData.sampleRecord - 样品单记录
 * @param {Array} emailData.recipients - 收件人列表
 * @param {Array} emailData.ccRecipients - 抄送人列表
 * @param {Object} emailData.submittedBy - 提交人信息
 */
async function sendSampleFormNotification(emailData) {
    try {
        const { sampleRecord, recipients, ccRecipients = [], submittedBy } = emailData;

        // 生成邮件内容
        const emailContent = generateSampleFormEmailContent(sampleRecord, submittedBy);

        // 准备收件人邮箱列表
        const toEmails = recipients.map(user => user.email).filter(email => email);
        const ccEmails = ccRecipients.map(user => user.email).filter(email => email);

        if (toEmails.length === 0) {
            throw new Error('没有有效的收件人邮箱地址');
        }

        // 邮件选项
        const mailOptions = {
            from: config.email.from,
            to: toEmails.join(', '),
            cc: ccEmails.length > 0 ? ccEmails.join(', ') : undefined,
            subject: `样品单通知 - ${sampleRecord.companyName} - ${sampleRecord.productModel}`,
            html: emailContent
        };

        logger.info('准备发送样品单邮件通知', {
            sampleId: sampleRecord.id,
            toCount: toEmails.length,
            ccCount: ccEmails.length,
            subject: mailOptions.subject
        });

        // 发送邮件
        const result = await sendEmailWithRetry(mailOptions);

        logger.info('样品单邮件通知发送成功', {
            sampleId: sampleRecord.id,
            messageId: result.messageId,
            recipients: toEmails.length,
            ccRecipients: ccEmails.length
        });

        return result;

    } catch (error) {
        logger.error('发送样品单邮件通知失败', {
            error: error.message,
            sampleId: emailData.sampleRecord?.id
        });
        throw error;
    }
}

/**
 * 生成样品单邮件内容
 * @param {Object} sampleRecord - 样品单记录
 * @param {Object} submittedBy - 提交人信息
 * @returns {string} HTML邮件内容
 */
function generateSampleFormEmailContent(sampleRecord, submittedBy) {
    const formatDate = (dateString) => {
        if (!dateString) return '';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    const formatTestItems = (testItems, testSampleCounts) => {
        if (!testItems || testItems.length === 0) return '无';
        
        return testItems.map(item => {
            const count = testSampleCounts && testSampleCounts[item] ? ` (${testSampleCounts[item]}个)` : '';
            return `${item}${count}`;
        }).join('<br>');
    };

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 800px; margin: 0 auto; padding: 20px; }
                .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .content { background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; }
                .section { margin-bottom: 20px; }
                .section-title { font-weight: bold; color: #495057; margin-bottom: 10px; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
                .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px; }
                .info-item { padding: 8px; background-color: #f8f9fa; border-radius: 4px; }
                .info-label { font-weight: bold; color: #6c757d; }
                .footer { margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 8px; font-size: 12px; color: #6c757d; }
                .highlight { background-color: #fff3cd; padding: 10px; border-radius: 4px; border-left: 4px solid #ffc107; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2 style="margin: 0; color: #007bff;">样品单通知</h2>
                    <p style="margin: 5px 0 0 0;">您收到了一份新的样品单，请查看详细信息。</p>
                </div>

                <div class="content">
                    <!-- 基本信息 -->
                    <div class="section">
                        <div class="section-title">基本信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">日期:</div>
                                <div>${formatDate(sampleRecord.date)}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">编号:</div>
                                <div>${sampleRecord.generatedNumber || sampleRecord.number || '自动生成'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">邮寄目的:</div>
                                <div>${sampleRecord.purpose || '未填写'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">提交人:</div>
                                <div>${submittedBy.username || submittedBy.name || '未知用户'}${submittedBy.email && submittedBy.email.trim() !== '' ? ` (${submittedBy.email})` : ''}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 公司信息 -->
                    <div class="section">
                        <div class="section-title">公司信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">公司名称:</div>
                                <div>${sampleRecord.companyName}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">收件人:</div>
                                <div>${sampleRecord.recipient}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">电话:</div>
                                <div>${sampleRecord.application || '未填写'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">地址:</div>
                                <div>${sampleRecord.address || '未填写'}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品信息 -->
                    <div class="section">
                        <div class="section-title">产品信息</div>
                        <div class="highlight">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">产品型号:</div>
                                    <div>${sampleRecord.productModel}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">数量:</div>
                                    <div>${sampleRecord.quantity}</div>
                                </div>
                            </div>
                        </div>
                        ${sampleRecord.productDescription ? `
                        <div class="info-item" style="margin-top: 10px;">
                            <div class="info-label">产品描述:</div>
                            <div>${sampleRecord.productDescription}</div>
                        </div>
                        ` : ''}
                    </div>

                    <!-- 测试信息 -->
                    ${sampleRecord.internalTest === 'yes' && sampleRecord.testItems && sampleRecord.testItems.length > 0 ? `
                    <div class="section">
                        <div class="section-title">测试信息</div>
                        <div class="info-item">
                            <div class="info-label">测试项目:</div>
                            <div>${formatTestItems(sampleRecord.testItems, sampleRecord.testSampleCounts)}</div>
                        </div>
                        ${sampleRecord.testNotes ? `
                        <div class="info-item" style="margin-top: 10px;">
                            <div class="info-label">测试备注:</div>
                            <div>${sampleRecord.testNotes}</div>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    <!-- 包装信息 -->
                    ${sampleRecord.packagingMethod ? `
                    <div class="section">
                        <div class="section-title">包装信息</div>
                        <div class="info-item">
                            <div class="info-label">包装方式:</div>
                            <div>${sampleRecord.packagingMethod}</div>
                        </div>
                    </div>
                    ` : ''}

                    <!-- 补充说明 -->
                    ${sampleRecord.additionalNotes ? `
                    <div class="section">
                        <div class="section-title">补充说明</div>
                        <div class="info-item">
                            <div style="background-color: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 4px solid #2563eb;">
                                ${sampleRecord.additionalNotes.replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>

                <div class="footer">
                    <p><strong>注意事项:</strong></p>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>请及时查看并处理此样品单</li>
                        <li>如有疑问，请联系提交人: ${submittedBy.username || submittedBy.name || '未知用户'}${submittedBy.email && submittedBy.email.trim() !== '' ? ` (${submittedBy.email})` : ''}</li>
                        <li>此邮件由系统自动发送，请勿直接回复</li>
                    </ul>
                    <p style="margin-top: 15px; text-align: center;">
                        发送时间: ${formatDate(new Date().toISOString())} | 
                        系统: 文件管理系统
                    </p>
                </div>
            </div>
        </body>
        </html>
    `;
}

module.exports = {
    createTransporter,
    verifyEmailConfig,
    sendEmailWithRetry,
    getEmailsByRole,
    getEmailsByUserIds,
    getReadOnlyUserEmails,
    generateEmailContent,
    generateQualityReportEmailContent,
    replaceTemplateVariables,
    sendApplicationSubmittedNotification,
    sendApprovalStatusNotification,
    sendApplicationRejectedNotification,
    sendHighAmountNotification,
    sendFileManagementNotification,
    sendQualityReportNotification,
    sendAllConfirmedNotification,
    sendSampleFormNotification
};
