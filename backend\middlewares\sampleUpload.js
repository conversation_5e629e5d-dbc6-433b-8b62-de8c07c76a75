/**
 * 样品单文件上传中间件
 * 处理样品单相关文件的上传配置
 * 目录结构：backend/uploads/samples/公司名称/YYYY/MM/DD/样品单ID/
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * 生成唯一文件名
 * @param {string} uploadDir - 上传目录
 * @param {string} originalName - 原始文件名
 * @param {string} prefix - 文件前缀
 */
function generateUniqueFilename(uploadDir, originalName, prefix = '') {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    
    let counter = 0;
    let filename = prefix ? `${prefix}_${baseName}${ext}` : `${baseName}${ext}`;
    
    // 如果文件已存在，添加数字后缀
    while (fs.existsSync(path.join(uploadDir, filename))) {
        counter++;
        filename = prefix ? 
            `${prefix}_${baseName}_${counter}${ext}` : 
            `${baseName}_${counter}${ext}`;
    }
    
    return filename;
}

/**
 * 创建样品单上传目录
 * 目录结构：backend/uploads/samples/公司名称/YYYY/MM/DD/样品单ID/
 * @param {string} companyName - 公司名称
 * @param {string} sampleId - 样品单ID
 */
function createSampleDirectory(companyName = 'default', sampleId = 'temp') {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // 清理公司名称，移除不安全的字符
    const safeCompanyName = companyName
        .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
        .replace(/\s+/g, '_')           // 替换空格为下划线
        .trim();

    // 清理样品单ID
    const safeSampleId = sampleId
        .replace(/[<>:"/\\|?*]/g, '_')
        .replace(/\s+/g, '_')
        .trim();

    const uploadDir = path.join(
        process.cwd(),
        'backend',
        'uploads',
        'samples',
        safeCompanyName,
        year.toString(),
        month,
        day,
        safeSampleId
    );

    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
        logger.info('创建样品单上传目录:', uploadDir);
    }

    return uploadDir;
}

// 配置样品单文件存储
const sampleStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        try {
            // 使用临时目录，后续在控制器中移动到正确位置
            const tempDir = path.join(__dirname, '../uploads/samples/temp');

            // 确保临时目录存在
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
                logger.info('创建样品单临时目录:', tempDir);
            }

            logger.info('样品单文件上传开始', {
                fieldname: file.fieldname,
                originalname: file.originalname,
                mimetype: file.mimetype,
                size: file.size,
                tempDir,
                tempDirExists: fs.existsSync(tempDir)
            });

            cb(null, tempDir);
        } catch (error) {
            logger.error('创建样品单上传目录失败:', error);
            cb(error);
        }
    },
    filename: function (req, file, cb) {
        try {
            // 根据README.md规范设置文件前缀
            let prefix = '';
            if (file.fieldname === 'maskPrintFiles') {
                prefix = 'mask_print_';  // 口罩印字文件前缀
            } else if (file.fieldname === 'valvePrintFiles') {
                prefix = 'valve_print_'; // 气阀印字文件前缀
            } else if (file.fieldname === 'colorBoxFiles') {
                prefix = 'colorbox_';    // 彩盒图档文件前缀
            } else if (file.fieldname === 'blisterPackFiles') {
                prefix = 'blisterpack_'; // 泡壳吊卡文件前缀
            }

            // 获取原始文件名和扩展名
            const ext = path.extname(file.originalname);
            const baseName = path.basename(file.originalname, ext);

            // 按照README.md规范清理文件名：移除特殊字符，空格替换为下划线
            const cleanBaseName = baseName
                .replace(/[<>:"/\\|?*]/g, '')  // 移除危险字符
                .replace(/\s+/g, '_')          // 空格替换为下划线
                .replace(/_{2,}/g, '_')        // 多个下划线合并为一个
                .replace(/^_|_$/g, '');        // 移除首尾下划线

            // 按照README.md规范生成文件名：前缀+原始文件名.扩展名（不包含时间戳ID）
            const finalFilename = `${prefix}${cleanBaseName}${ext}`;

            logger.info('样品单文件命名（遵循README.md规范）', {
                fieldname: file.fieldname,
                originalname: file.originalname,
                finalFilename,
                prefix,
                cleanBaseName,
                extension: ext
            });

            cb(null, finalFilename);
        } catch (error) {
            logger.error('生成样品单文件名失败:', error);
            cb(error);
        }
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = config.upload.allowedTypes;
    
    if (allowedTypes.test(file.mimetype) || allowedTypes.test(path.extname(file.originalname).toLowerCase())) {
        cb(null, true);
    } else {
        const error = new Error('不支持的文件类型');
        error.code = 'INVALID_FILE_TYPE';
        cb(error, false);
    }
};

// 创建样品单文件上传中间件
const sampleUpload = multer({
    storage: sampleStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: config.upload.maxFileSize, // 10MB
        files: 20 // 最多20个文件（口罩印字+气阀印字）
    },
    // 确保正确处理中文文件名
    preservePath: false,
    // 设置字段名编码
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB for field data
});

// 错误处理中间件
const handleSampleUploadError = (error, req, res, next) => {
    logger.error('样品单文件上传错误:', error);
    
    if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
            success: false,
            message: `文件大小超过限制，最大允许 ${config.upload.maxFileSize / 1024 / 1024}MB`
        });
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({
            success: false,
            message: '文件数量超过限制，最多允许20个文件'
        });
    }
    
    if (error.code === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
            success: false,
            message: '不支持的文件类型，请上传PDF、DOC、DOCX、JPG、PNG格式的文件'
        });
    }
    
    res.status(500).json({
        success: false,
        message: '文件上传失败: ' + error.message
    });
};

module.exports = {
    sampleUpload,
    handleSampleUploadError,
    createSampleDirectory
};
