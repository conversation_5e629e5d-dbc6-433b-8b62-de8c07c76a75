<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理中心 - 管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/file-management/upload.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 进度指示器 -->
    <div class="progress-indicator" id="progressIndicator"></div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto"></div>
            <p class="mt-4 text-gray-700 font-medium">正在加载文件管理中心...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="flex min-h-screen" style="display: none;">
        <!-- 侧边栏 -->
        <sidebar
            :user="currentUser"
            :sidebar-open="sidebarOpen"
        ></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 md:hidden">
                <div class="flex items-center justify-between px-4 py-3">
                    <button
                        @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-600 hover:text-gray-900 focus:outline-none"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">文件上传选择</h1>
                    <div class="w-6"></div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-4 md:p-6 lg:p-8">
                <div class="max-w-6xl mx-auto">
                    <!-- 页面标题 -->
                    <div class="mb-8">
                        <h1 class="page-title text-3xl font-bold text-gray-800 mb-2">文件管理中心</h1>
                        <p class="text-gray-600">选择您要上传的文件类型，支持多种格式和版本管理</p>
                    </div>

                    <!-- 统计信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="stats-card rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">156</div>
                            <div class="text-sm text-gray-600">总文件数</div>
                        </div>
                        <div class="stats-card rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">23</div>
                            <div class="text-sm text-gray-600">本月上传</div>
                        </div>
                        <div class="stats-card rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600">2.3GB</div>
                            <div class="text-sm text-gray-600">存储使用</div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="quick-actions mb-6 flex flex-col md:flex-row justify-between items-center gap-4">
                        <div class="flex flex-wrap items-center gap-3">
                            <div class="format-hint">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                支持 PDF、DOC、XLS、图片等格式
                            </div>
                            <div class="format-hint">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01"></path>
                                </svg>
                                最大 10MB
                            </div>
                        </div>
                        <a href="/file-list" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            查看所有文件 →
                        </a>
                    </div>

                    <!-- 上传选项 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- 客户二认文件上传 -->
                        <div class="upload-card certification rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="card-icon w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">客户二认文件</h3>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                上传客户产品的二次认证文件，支持版本管理和邮件通知
                            </p>
                            <a href="/file-upload/certification"
                               class="upload-btn inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                开始上传
                            </a>
                        </div>

                        <!-- 样品单管理 -->
                        <div class="upload-card sample rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="card-icon w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">样品单管理</h3>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                创建和管理样品单信息，支持状态追踪和数据分析
                            </p>
                            <a href="/file-upload/sample"
                               class="upload-btn inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                开始填写
                            </a>
                        </div>

                        <!-- 生产控制表 -->
                        <div class="upload-card production rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <div class="card-icon w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">生产控制表</h3>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">
                                创建生产控制表单，实现过程管理和质量控制
                            </p>
                            <a href="/file-upload/production-control"
                               class="upload-btn inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                开始填写
                            </a>
                        </div>
                    </div>

                    <!-- 帮助信息 -->
                    <div class="help-section rounded-lg p-4 text-center">
                        <p class="text-gray-700 text-sm">
                            需要帮助？请查看
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">使用说明</a>
                            或
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">联系技术支持</a>
                        </p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 遮罩层（移动端侧边栏） -->
    <div
        v-if="sidebarOpen"
        @click="sidebarOpen = false"
        class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
    ></div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/file-management/upload-main.js"></script>
</body>
</html>
