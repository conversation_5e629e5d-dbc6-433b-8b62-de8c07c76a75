/**
 * PostgreSQL文件管理数据访问层
 * 替换SQLite的fileManagementRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class PostgresFileManagementRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL文件管理数据访问层初始化完成');
        }
    }

    // 客户管理相关方法

    /**
     * 获取所有客户
     */
    async findAllCustomers() {
        try {
            const customers = await this.findMany(`
                SELECT * FROM file_management_customers
                ORDER BY customer_name
            `);
            return customers;
        } catch (error) {
            logger.error('获取所有客户失败:', error);
            throw error;
        }
    }

    /**
     * 获取活跃客户
     */
    async findActiveCustomers() {
        try {
            const customers = await this.findMany(`
                SELECT * FROM file_management_customers
                WHERE active = true
                ORDER BY customer_name
            `);
            return customers;
        } catch (error) {
            logger.error('获取活跃客户失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找客户
     */
    async findCustomerById(id) {
        try {
            const customer = await this.findOne(`
                SELECT * FROM file_management_customers 
                WHERE id = $1 AND active = true
            `, [id]);
            return customer;
        } catch (error) {
            logger.error(`根据ID查找客户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找客户
     */
    async findCustomerByName(customerName) {
        try {
            const customer = await this.findOne(`
                SELECT * FROM file_management_customers
                WHERE customer_name = $1 AND active = true
            `, [customerName]);
            return customer;
        } catch (error) {
            logger.error(`根据名称查找客户失败 (${customerName}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找客户（包括停用的）
     */
    async findCustomerByNameAll(customerName) {
        try {
            const customer = await this.findOne(`
                SELECT * FROM file_management_customers
                WHERE customer_name = $1
            `, [customerName]);
            return customer;
        } catch (error) {
            logger.error(`根据名称查找客户失败 (${customerName}):`, error);
            throw error;
        }
    }

    /**
     * 创建客户
     */
    async createCustomer(customerData) {
        try {
            const now = new Date().toISOString();
            const id = customerData.id || uuidv4();

            const result = await this.query(`
                INSERT INTO file_management_customers
                (id, customer_name, customer_code, contact_person, contact_email,
                 contact_phone, address, description, active, created_by, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                RETURNING *
            `, [
                id, customerData.customer_name, customerData.customer_code,
                customerData.contact_person, customerData.contact_email,
                customerData.contact_phone, customerData.address,
                customerData.description, true, customerData.created_by, now, now
            ]);

            return result.rows[0];
        } catch (error) {
            logger.error('创建客户失败:', error);
            throw error;
        }
    }

    /**
     * 更新客户
     */
    async updateCustomer(id, customerData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE file_management_customers 
                SET customer_name = $1, customer_code = $2, contact_person = $3, 
                    contact_email = $4, contact_phone = $5, address = $6, 
                    description = $7, updated_at = $8
                WHERE id = $9
                RETURNING *
            `, [
                customerData.customer_name, customerData.customer_code,
                customerData.contact_person, customerData.contact_email,
                customerData.contact_phone, customerData.address,
                customerData.description, now, id
            ]);

            return result.rows.length > 0 ? result.rows[0] : null;
        } catch (error) {
            logger.error(`更新客户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除客户
     */
    async deleteCustomer(id) {
        try {
            const result = await this.query('DELETE FROM file_management_customers WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除客户失败 (${id}):`, error);
            throw error;
        }
    }

    // 产品管理相关方法

    /**
     * 根据客户获取产品
     */
    async findProductsByCustomer(customerId) {
        try {
            const products = await this.findMany(`
                SELECT * FROM file_management_products 
                WHERE customer_id = $1 AND active = true 
                ORDER BY product_model
            `, [customerId]);
            return products;
        } catch (error) {
            logger.error(`根据客户获取产品失败 (${customerId}):`, error);
            throw error;
        }
    }

    /**
     * 根据ID查找产品
     */
    async findProductById(id) {
        try {
            const product = await this.findOne(`
                SELECT * FROM file_management_products 
                WHERE id = $1 AND active = true
            `, [id]);
            return product;
        } catch (error) {
            logger.error(`根据ID查找产品失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据客户、型号和批次查找产品
     */
    async findProductByCustomerAndModel(customerId, productModel, batchNumber) {
        try {
            const product = await this.findOne(`
                SELECT * FROM file_management_products 
                WHERE customer_id = $1 AND product_model = $2 AND batch_number = $3 AND active = true
            `, [customerId, productModel, batchNumber]);
            return product;
        } catch (error) {
            logger.error(`根据客户和型号查找产品失败:`, error);
            throw error;
        }
    }

    /**
     * 创建产品
     */
    async createProduct(productData) {
        try {
            const now = new Date().toISOString();
            const id = productData.id || uuidv4();

            const result = await this.query(`
                INSERT INTO file_management_products 
                (id, customer_id, product_model, product_name, batch_number, 
                 specification, description, created_by, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING *
            `, [
                id, productData.customer_id, productData.product_model,
                productData.product_name, productData.batch_number,
                productData.specification, productData.description,
                productData.created_by, now, now
            ]);

            return result.rows[0];
        } catch (error) {
            logger.error('创建产品失败:', error);
            throw error;
        }
    }

    /**
     * 更新产品
     */
    async updateProduct(id, productData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE file_management_products 
                SET product_model = $1, product_name = $2, batch_number = $3,
                    specification = $4, description = $5, updated_at = $6
                WHERE id = $7
                RETURNING *
            `, [
                productData.product_model, productData.product_name,
                productData.batch_number, productData.specification,
                productData.description, now, id
            ]);

            return result.rows.length > 0 ? result.rows[0] : null;
        } catch (error) {
            logger.error(`更新产品失败 (${id}):`, error);
            throw error;
        }
    }

    // 文件管理相关方法

    /**
     * 根据产品获取文件
     */
    async findFilesByProduct(productId) {
        try {
            const files = await this.findMany(`
                SELECT * FROM file_management_files 
                WHERE product_id = $1 AND active = true 
                ORDER BY uploaded_at DESC
            `, [productId]);
            return files;
        } catch (error) {
            logger.error(`根据产品获取文件失败 (${productId}):`, error);
            throw error;
        }
    }

    /**
     * 根据ID查找文件
     */
    async findFileById(id) {
        try {
            const file = await this.findOne(`
                SELECT fmf.*, fmp.product_model, fmc.customer_name
                FROM file_management_files fmf
                LEFT JOIN file_management_products fmp ON fmf.product_id = fmp.id
                LEFT JOIN file_management_customers fmc ON fmp.customer_id = fmc.id
                WHERE fmf.id = $1 AND fmf.active = true
            `, [id]);
            return file;
        } catch (error) {
            logger.error(`根据ID查找文件失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 创建文件记录
     */
    async createFile(fileData) {
        try {
            const now = new Date().toISOString();
            const id = fileData.id || uuidv4();

            const result = await this.query(`
                INSERT INTO file_management_files 
                (id, product_id, original_filename, stored_filename, file_path,
                 file_size, file_type, mime_type, description, uploaded_by, uploaded_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING *
            `, [
                id, fileData.product_id, fileData.original_filename,
                fileData.stored_filename, fileData.file_path,
                fileData.file_size, fileData.file_type, fileData.mime_type,
                fileData.description, fileData.uploaded_by, now
            ]);

            return result.rows[0];
        } catch (error) {
            logger.error('创建文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 删除文件记录
     */
    async deleteFile(id) {
        try {
            const result = await this.query('DELETE FROM file_management_files WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除文件记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 获取文件管理统计信息
     */
    async getFileManagementStats() {
        try {
            const stats = await this.findOne(`
                SELECT 
                    (SELECT COUNT(*) FROM file_management_customers WHERE active = true) as customer_count,
                    (SELECT COUNT(*) FROM file_management_products WHERE active = true) as product_count,
                    (SELECT COUNT(*) FROM file_management_files WHERE active = true) as file_count,
                    (SELECT COALESCE(SUM(file_size), 0) FROM file_management_files WHERE active = true) as total_size
            `);
            
            return {
                customerCount: parseInt(stats.customer_count) || 0,
                productCount: parseInt(stats.product_count) || 0,
                fileCount: parseInt(stats.file_count) || 0,
                totalSize: parseInt(stats.total_size) || 0
            };
        } catch (error) {
            logger.error('获取文件管理统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 搜索文件
     */
    async searchFiles(keyword) {
        try {
            const files = await this.findMany(`
                SELECT fmf.*, fmp.product_model, fmc.customer_name
                FROM file_management_files fmf
                LEFT JOIN file_management_products fmp ON fmf.product_id = fmp.id
                LEFT JOIN file_management_customers fmc ON fmp.customer_id = fmc.id
                WHERE fmf.status != 'deleted' AND (
                    fmf.title ILIKE $1 OR 
                    fmf.description ILIKE $1 OR
                    fmp.product_model ILIKE $1 OR
                    fmc.customer_name ILIKE $1
                )
                ORDER BY fmf.uploaded_at DESC
            `, [`%${keyword}%`]);
            return files;
        } catch (error) {
            logger.error(`搜索文件失败 (${keyword}):`, error);
            throw error;
        }
    }

    /**
     * 获取所有文件记录
     */
    async getAllFiles() {
        try {
            const files = await this.findMany(`
                SELECT fmf.*, fmp.product_model, fmc.customer_name
                FROM file_management_files fmf
                LEFT JOIN file_management_products fmp ON fmf.product_id = fmp.id
                LEFT JOIN file_management_customers fmc ON fmp.customer_id = fmc.id
                WHERE fmf.status != 'deleted'
                ORDER BY fmf.uploaded_at DESC
            `);
            return files;
        } catch (error) {
            logger.error('获取所有文件记录失败:', error);
            throw error;
        }
    }

    /**
     * 根据文件记录ID获取附件信息
     */
    async getAttachmentsByFileRecord(fileRecordId) {
        try {
            const attachments = await this.findMany(`
                SELECT * FROM file_management_attachments
                WHERE file_record_id = $1
                ORDER BY uploaded_at DESC
            `, [fileRecordId]);
            return attachments;
        } catch (error) {
            logger.error(`获取文件附件失败 (${fileRecordId}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户ID获取通知列表
     */
    async getNotificationsByUser(userId) {
        try {
            // 先尝试简单查询，确保基本功能正常
            const notifications = await this.findMany(`
                SELECT * FROM file_management_notifications
                WHERE notified_user_id = $1
                ORDER BY created_at DESC
            `, [userId]);
            return notifications;
        } catch (error) {
            logger.error(`获取用户通知失败 (${userId}):`, error);
            throw error;
        }
    }

    /**
     * 确认通知
     */
    async confirmNotification(notificationId, userId) {
        try {
            const result = await this.query(`
                UPDATE file_management_notifications 
                SET confirmed = true, confirmed_at = NOW(), updated_at = NOW()
                WHERE id = $1 AND notified_user_id = $2
                RETURNING *
            `, [notificationId, userId]);
            return result.rows[0];
        } catch (error) {
            logger.error(`确认通知失败 (${notificationId}):`, error);
            throw error;
        }
    }

    /**
     * 检查文件的所有通知是否都已确认
     */
    async checkAllNotificationsConfirmed(fileRecordId) {
        try {
            const result = await this.findOne(`
                SELECT 
                    COUNT(*) as total_notifications,
                    COUNT(CASE WHEN confirmed = true THEN 1 END) as confirmed_notifications
                FROM file_management_notifications
                WHERE file_record_id = $1
            `, [fileRecordId]);

            const totalNotifications = parseInt(result.total_notifications);
            const confirmedNotifications = parseInt(result.confirmed_notifications);

            return {
                allConfirmed: totalNotifications > 0 && totalNotifications === confirmedNotifications,
                totalNotifications,
                confirmedNotifications
            };
        } catch (error) {
            logger.error(`检查通知确认状态失败 (${fileRecordId}):`, error);
            throw error;
        }
    }

    /**
     * 根据ID获取文件详情（包含关联信息）
     */
    async getFileById(fileId) {
        try {
            const file = await this.findOne(`
                SELECT fmf.*, fmp.product_model, fmc.customer_name
                FROM file_management_files fmf
                LEFT JOIN file_management_products fmp ON fmf.product_id = fmp.id
                LEFT JOIN file_management_customers fmc ON fmp.customer_id = fmc.id
                WHERE fmf.id = $1 AND fmf.status != 'deleted'
            `, [fileId]);
            return file;
        } catch (error) {
            logger.error(`获取文件详情失败 (${fileId}):`, error);
            throw error;
        }
    }
}

module.exports = PostgresFileManagementRepository;
