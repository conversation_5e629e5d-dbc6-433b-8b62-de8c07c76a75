/**
 * 设备健康度计算器
 * 实现四维度评估算法：设备年龄、维修频率、故障严重程度、保养情况
 */

const logger = require('./logger');

class HealthCalculator {
    constructor() {
        // 四维度权重配置
        this.weights = {
            age: 0.2,               // 设备年龄 20%
            repairFrequency: 0.3,   // 维修频率 30%
            faultSeverity: 0.3,     // 故障严重程度 30%
            maintenance: 0.2        // 保养情况 20%
        };

        // 健康度等级划分
        this.healthLevels = [
            { min: 90, max: 100, level: '优秀', description: '设备状态极佳，维护良好' },
            { min: 80, max: 89, level: '良好', description: '设备状态良好，正常运行' },
            { min: 70, max: 79, level: '一般', description: '设备状态一般，需要关注' },
            { min: 60, max: 69, level: '较差', description: '设备状态较差，需要维护' },
            { min: 0, max: 59, level: '危险', description: '设备状态危险，急需检修' }
        ];
    }

    /**
     * 计算所有维度评分
     */
    calculateAllDimensions(equipment, maintenanceRecords) {
        const ageInYears = this.calculateEquipmentAge(equipment.manufacture_date);
        
        return {
            age: this.calculateAgeScore(ageInYears),
            repairFrequency: this.calculateRepairFrequencyScore(maintenanceRecords, ageInYears),
            faultSeverity: this.calculateFaultSeverityScore(maintenanceRecords),
            maintenance: this.calculateMaintenanceScore(maintenanceRecords)
        };
    }

    /**
     * 计算设备年龄（年）
     * 根据当前日期和设备进厂日期计算实际年龄
     */
    calculateEquipmentAge(manufactureDate) {
        // 使用当前日期计算设备实际年龄
        const currentDate = new Date();
        const mfgDate = new Date(manufactureDate);
        return (currentDate - mfgDate) / (365.25 * 24 * 60 * 60 * 1000);
    }

    /**
     * 1. 设备年龄评分算法 (20% 权重)
     */
    calculateAgeScore(ageInYears) {
        let score;
        let category;

        if (ageInYears < 1) {
            score = 100;
            category = '不足1年';
        } else if (ageInYears <= 3) {
            score = 90;
            category = '1-3年';
        } else if (ageInYears <= 5) {
            score = 80;
            category = '3-5年';
        } else if (ageInYears <= 8) {
            score = 70;
            category = '5-8年';
        } else if (ageInYears <= 10) {
            score = 60;
            category = '8-10年';
        } else {
            // 超过10年，每年递减3分
            const excessYears = ageInYears - 10;
            score = Math.max(30, 60 - (excessYears * 3));
            category = `超过10年(${ageInYears.toFixed(1)}年)`;
        }

        return {
            score: Math.round(score),
            weight: this.weights.age,
            details: {
                ageInYears: Math.round(ageInYears * 10) / 10,
                category
            }
        };
    }

    /**
     * 2. 维修频率评分算法 (30% 权重)
     */
    calculateRepairFrequencyScore(maintenanceRecords, ageInYears) {
        // 筛选维修记录（排除保养记录）
        const repairRecords = maintenanceRecords.filter(record => 
            record.type === 'repair' || record.type === 'emergency'
        );

        if (repairRecords.length === 0) {
            return {
                score: 100,
                weight: this.weights.repairFrequency,
                details: {
                    annualRepairRate: 0,
                    totalRepairs: 0,
                    category: '无维修记录'
                }
            };
        }

        const annualRepairRate = repairRecords.length / Math.max(ageInYears, 1);
        let score;
        let category;

        if (annualRepairRate < 0.5) {
            score = 100;
            category = '< 0.5次/年';
        } else if (annualRepairRate <= 1) {
            score = 90;
            category = '0.5-1次/年';
        } else if (annualRepairRate <= 2) {
            score = 80;
            category = '1-2次/年';
        } else if (annualRepairRate <= 3) {
            score = 70;
            category = '2-3次/年';
        } else if (annualRepairRate <= 4) {
            score = 60;
            category = '3-4次/年';
        } else {
            // 超过4次，每次递减10分
            const excessRepairs = annualRepairRate - 4;
            score = Math.max(20, 60 - (excessRepairs * 10));
            category = `> 4次/年(${annualRepairRate.toFixed(1)}次/年)`;
        }

        return {
            score: Math.round(score),
            weight: this.weights.repairFrequency,
            details: {
                annualRepairRate: Math.round(annualRepairRate * 10) / 10,
                totalRepairs: repairRecords.length,
                category
            }
        };
    }

    /**
     * 3. 故障严重程度评分算法 (30% 权重)
     */
    calculateFaultSeverityScore(maintenanceRecords) {
        const repairRecords = maintenanceRecords.filter(record => record.type === 'repair');
        
        if (repairRecords.length === 0) {
            return {
                score: 100,
                weight: this.weights.faultSeverity,
                details: {
                    weightedDeduction: 0,
                    severeCount: 0,
                    moderateCount: 0,
                    minorCount: 0
                }
            };
        }

        // 使用当前日期计算时间权重
        const now = new Date();
        let totalWeightedDeduction = 0;
        let severeCount = 0;
        let moderateCount = 0;
        let minorCount = 0;

        repairRecords.forEach(record => {
            const recordDate = new Date(record.maintenance_date);
            const monthsAgo = (now - recordDate) / (30.44 * 24 * 60 * 60 * 1000);
            
            // 时间权重计算
            let timeWeight;
            if (monthsAgo <= 3) timeWeight = 1.0;
            else if (monthsAgo <= 6) timeWeight = 0.8;
            else if (monthsAgo <= 12) timeWeight = 0.6;
            else if (monthsAgo <= 24) timeWeight = 0.4;
            else timeWeight = 0.2;
            
            // 严重程度扣分
            let severityDeduction;
            const severity = record.severity_level || 'moderate';
            
            switch (severity) {
                case 'severe':
                    severityDeduction = 50;
                    severeCount++;
                    break;
                case 'moderate':
                    severityDeduction = 25;
                    moderateCount++;
                    break;
                case 'minor':
                    severityDeduction = 10;
                    minorCount++;
                    break;
                default:
                    severityDeduction = 25; // 默认一般故障
                    moderateCount++;
            }
            
            totalWeightedDeduction += severityDeduction * timeWeight;
        });

        const score = Math.max(15, 100 - totalWeightedDeduction);

        return {
            score: Math.round(score),
            weight: this.weights.faultSeverity,
            details: {
                weightedDeduction: Math.round(totalWeightedDeduction),
                severeCount,
                moderateCount,
                minorCount
            }
        };
    }

    /**
     * 4. 保养情况评分算法 (20% 权重)
     */
    calculateMaintenanceScore(maintenanceRecords) {
        // 如果没有任何维修记录，认为设备状态良好，给予较高分数
        if (maintenanceRecords.length === 0) {
            return {
                score: 85, // 没有维修记录说明设备状态良好
                weight: this.weights.maintenance,
                details: {
                    maintenanceRatio: 0,
                    daysSinceLastMaintenance: null,
                    ratioScore: 85,
                    recentScore: 85,
                    note: '无维修记录，设备状态良好'
                }
            };
        }

        const maintenanceOnly = maintenanceRecords.filter(record =>
            record.type === 'maintenance' || record.type === 'preventive'
        );

        const repairOnly = maintenanceRecords.filter(record =>
            record.type === 'repair' || record.type === 'emergency'
        );

        // 保养比例得分 (60%权重)
        // 如果只有保养记录没有维修记录，给满分
        // 如果有维修记录，计算保养占比
        let ratioScore;
        if (repairOnly.length === 0 && maintenanceOnly.length > 0) {
            ratioScore = 100; // 只有保养记录，说明维护很好
        } else if (maintenanceRecords.length > 0) {
            const maintenanceRatio = maintenanceOnly.length / maintenanceRecords.length;
            ratioScore = maintenanceRatio * 100;
        } else {
            ratioScore = 85; // 没有记录，默认良好
        }

        // 最近保养时间得分 (40%权重)
        let recentMaintenanceScore = 40; // 默认超过365天

        if (maintenanceOnly.length > 0) {
            const lastMaintenance = maintenanceOnly
                .sort((a, b) => new Date(b.maintenance_date) - new Date(a.maintenance_date))[0];

            const daysSinceLastMaintenance =
                (new Date() - new Date(lastMaintenance.maintenance_date)) / (24 * 60 * 60 * 1000);

            if (daysSinceLastMaintenance <= 30) recentMaintenanceScore = 100;
            else if (daysSinceLastMaintenance <= 60) recentMaintenanceScore = 90;
            else if (daysSinceLastMaintenance <= 90) recentMaintenanceScore = 80;
            else if (daysSinceLastMaintenance <= 180) recentMaintenanceScore = 70;
            else if (daysSinceLastMaintenance <= 365) recentMaintenanceScore = 60;
        } else if (repairOnly.length === 0) {
            // 没有保养记录但也没有维修记录，说明设备状态良好
            recentMaintenanceScore = 85;
        }

        const finalScore = ratioScore * 0.6 + recentMaintenanceScore * 0.4;

        // 计算保养比例用于详情显示
        const maintenanceRatio = maintenanceRecords.length > 0 ?
            maintenanceOnly.length / maintenanceRecords.length : 0;

        return {
            score: Math.round(finalScore),
            weight: this.weights.maintenance,
            details: {
                maintenanceRatio: Math.round(maintenanceRatio * 100) / 100,
                daysSinceLastMaintenance: maintenanceOnly.length > 0 ?
                    Math.round((new Date() - new Date(maintenanceOnly[0].maintenance_date)) / (24 * 60 * 60 * 1000)) : null,
                ratioScore: Math.round(ratioScore),
                recentScore: Math.round(recentMaintenanceScore)
            }
        };
    }

    /**
     * 计算总得分
     */
    calculateTotalScore(dimensions) {
        let totalScore = 0;
        
        for (const [dimensionName, dimensionData] of Object.entries(dimensions)) {
            totalScore += dimensionData.score * dimensionData.weight;
        }
        
        return Math.round(totalScore);
    }

    /**
     * 获取健康度等级
     */
    getHealthLevel(totalScore) {
        for (const level of this.healthLevels) {
            if (totalScore >= level.min && totalScore <= level.max) {
                return level.level;
            }
        }
        return '未知';
    }

    /**
     * 获取健康度等级描述
     */
    getHealthLevelDescription(totalScore) {
        for (const level of this.healthLevels) {
            if (totalScore >= level.min && totalScore <= level.max) {
                return level.description;
            }
        }
        return '状态未知';
    }
}

module.exports = HealthCalculator;
