/**
 * 部门管理组件
 * 提供部门的增删改查功能
 */

import { getDepartments, createDepartment, updateDepartment, deleteDepartment, checkDepartmentName, checkDepartmentUsers } from '../../scripts/api/department.js';

export default {
    props: {
        currentUser: Object
    },
    emits: ['close', 'updated'],
    setup(props, { emit }) {
        const { ref, reactive, computed, onMounted, watch } = Vue;

        // 状态变量
        const departments = ref([]);
        const isLoading = ref(false);
        const showModal = ref(false);
        const isEditing = ref(false);
        const isSubmitting = ref(false);
        const refreshKey = ref(0); // 用于强制刷新

        // 表单数据
        const formData = reactive({
            id: '',
            name: '',
            description: ''
        });

        // 表单验证错误
        const formErrors = reactive({
            name: ''
        });

        // 计算属性确保响应式更新
        const departmentList = computed(() => {
            // 使用refreshKey来强制重新计算
            refreshKey.value;
            return departments.value.slice(); // 创建新数组确保响应式
        });



        // 加载部门列表
        async function loadDepartments(showLoading = true) {
            try {
                if (showLoading) {
                    isLoading.value = true;
                }
                const response = await getDepartments();

                // 使用Vue.nextTick确保DOM更新
                await Vue.nextTick();

                // 强制清空数组后重新赋值，确保响应式更新
                const list = Array.isArray(response?.departments) ? response.departments : [];
                departments.value.splice(0, departments.value.length, ...list);
            } catch (error) {
                console.error('加载部门列表失败:', error);
                alert('加载部门列表失败: ' + (error.response?.data?.message || error.message));
            } finally {
                if (showLoading) {
                    isLoading.value = false;
                }
            }
        }

        // 打开新增部门模态框
        function openAddModal() {
            resetForm();
            isEditing.value = false;
            showModal.value = true;
        }

        // 打开编辑部门模态框
        function openEditModal(department) {
            resetForm();
            formData.id = department.id;
            formData.name = department.name;
            formData.description = department.description || '';
            isEditing.value = true;
            showModal.value = true;
        }

        // 关闭模态框
        function closeModal() {
            showModal.value = false;
            resetForm();
        }

        // 重置表单
        function resetForm() {
            formData.id = '';
            formData.name = '';
            formData.description = '';
            formErrors.name = '';
        }

        // 验证表单
        function validateForm() {
            let isValid = true;
            
            // 验证部门名称
            if (!formData.name.trim()) {
                formErrors.name = '部门名称不能为空';
                isValid = false;
            } else {
                formErrors.name = '';
            }

            return isValid;
        }

        // 提交表单
        async function submitForm() {
            if (!validateForm()) return;

            try {
                isSubmitting.value = true;

                // 检查部门名称是否已存在
                const nameCheckResponse = await checkDepartmentName(formData.name.trim(), formData.id);
                if (nameCheckResponse.exists) {
                    formErrors.name = '部门名称已存在';
                    return;
                }

                let result;
                if (isEditing.value) {
                    // 更新部门
                    result = await updateDepartment(formData.id, {
                        name: formData.name.trim(),
                        description: formData.description.trim()
                    });
                } else {
                    // 创建部门
                    result = await createDepartment({
                        name: formData.name.trim(),
                        description: formData.description.trim()
                    });
                }

                if (result.success) {
                    closeModal();

                    // 如果是创建操作，直接添加到列表
                    if (!isEditing.value && result.department) {
                        departments.value.push(result.department);
                    } else {
                        // 如果是编辑操作，更新列表中的对应项
                        const index = departments.value.findIndex(d => d.id === formData.id);
                        if (index > -1 && result.department) {
                            departments.value.splice(index, 1, result.department);
                        }
                    }

                    // 强制刷新显示
                    refreshKey.value++;

                    // 静默重新加载数据确保同步（不显示加载状态）
                    loadDepartments(false);

                    alert(isEditing.value ? '部门更新成功！' : '部门创建成功！');
                    emit('updated');
                } else {
                    alert((isEditing.value ? '更新' : '创建') + '失败: ' + result.message);
                }
            } catch (error) {
                console.error(isEditing.value ? '更新失败:' : '创建失败:', error);
                alert((isEditing.value ? '更新' : '创建') + '失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 删除部门
        async function deleteDept(department) {
            try {
                // 先检查部门是否有用户关联
                const checkResult = await checkDepartmentUsers(department.id);

                if (checkResult.hasUsers) {
                    alert(`无法删除部门"${department.name}"！\n\n该部门下还有活跃的用户，请先将用户转移到其他部门后再删除。`);
                    return;
                }

                // 如果没有用户关联，显示确认删除对话框
                if (!confirm(`确定要删除部门"${department.name}"吗？\n\n删除后将无法恢复。`)) {
                    return;
                }

                const result = await deleteDepartment(department.id);

                if (result.success) {
                    // 立即从本地数组中移除该部门
                    const index = departments.value.findIndex(d => d.id === department.id);
                    if (index > -1) {
                        departments.value.splice(index, 1);
                    }

                    // 强制刷新显示
                    refreshKey.value++;

                    // 静默重新加载数据确保同步（不显示加载状态）
                    loadDepartments(false);

                    alert('部门删除成功！');
                    emit('updated');
                } else {
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除部门失败:', error);
                const errorMessage = error.response?.data?.message || error.message;
                alert('删除失败: ' + errorMessage);
            }
        }



        // 组件挂载时加载数据
        onMounted(() => {
            loadDepartments();
        });

        return {
            departments,
            departmentList,
            isLoading,
            showModal,
            isEditing,
            isSubmitting,
            refreshKey,
            formData,
            formErrors,
            openAddModal,
            openEditModal,
            closeModal,
            submitForm,
            deleteDept
        };
    },
    template: `
        <div class="space-y-6">
            <!-- 头部操作区 -->
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">部门管理</h2>
                    <p class="text-gray-600 mt-1">管理系统中的部门信息</p>
                </div>
                <button @click="openAddModal"
                        class="px-4 py-2 rounded-lg text-white transition-colors"
                        style="background-color: #3B82F6; hover:background-color: #2563EB;">
                    <i class="fas fa-plus mr-2"></i>新增部门
                </button>
            </div>



            <!-- 部门列表 -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div v-if="isLoading" class="p-8 text-center">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                    <p class="text-gray-500 mt-2">加载中...</p>
                </div>

                <div v-else-if="departmentList.length === 0" class="p-8 text-center">
                    <i class="fas fa-building text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">暂无部门数据</p>
                </div>

                <div v-else class="overflow-x-auto">
                    <table :key="refreshKey" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">部门名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">创建时间</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="(department, index) in departmentList" :key="'dept-' + department.id + '-' + index" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ department.name }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-500">{{ department.description || '-' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ department.createdAt ? new Date(department.createdAt).toLocaleDateString('zh-CN') : '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <div class="flex justify-center space-x-2">
                                        <button @click="openEditModal(department)"
                                                class="inline-flex items-center px-3 py-1 text-xs font-medium text-white rounded transition-colors"
                                                style="background-color: #3B82F6;"
                                                onmouseover="this.style.backgroundColor='#2563EB'"
                                                onmouseout="this.style.backgroundColor='#3B82F6'">
                                            <i class="fas fa-edit mr-1"></i>编辑
                                        </button>
                                        <button @click="deleteDept(department)"
                                                class="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 bg-red-100 rounded hover:bg-red-200 transition-colors">
                                            <i class="fas fa-trash mr-1"></i>删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 部门编辑模态框 -->
            <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">{{ isEditing ? '编辑部门' : '新增部门' }}</h3>
                        <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form @submit.prevent="submitForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">部门名称 *</label>
                            <input v-model="formData.name"
                                   type="text"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   :class="{ 'border-red-500': formErrors.name }"
                                   placeholder="请输入部门名称">
                            <p v-if="formErrors.name" class="text-red-500 text-xs mt-1">{{ formErrors.name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">部门描述</label>
                            <textarea v-model="formData.description"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="请输入部门描述"></textarea>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4">
                            <button type="button"
                                    @click="closeModal"
                                    class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                                取消
                            </button>
                            <button type="submit"
                                    :disabled="isSubmitting"
                                    class="px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50"
                                    style="background-color: #3B82F6;"
                                    onmouseover="if(!this.disabled) this.style.backgroundColor='#2563EB'"
                                    onmouseout="if(!this.disabled) this.style.backgroundColor='#3B82F6'">
                                <i v-if="isSubmitting" class="fas fa-spinner fa-spin mr-2"></i>
                                {{ isEditing ? '更新' : '创建' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `
};
