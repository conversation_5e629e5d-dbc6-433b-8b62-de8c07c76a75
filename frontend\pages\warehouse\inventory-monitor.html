<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存监控 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/warehouse/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            仓库管理 - 库存监控
                        </span>
                    </li>
                </ol>
            </nav>

            <div class="space-y-6">
                <!-- 页面标题 -->
                <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-6">库存监控</h2>

                    <!-- 监控选项卡 -->
                    <div class="border-b border-gray-200 mb-6">
                        <div class="flex space-x-8">
                            <button
                                @click="activeTab = 'overview'"
                                class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                                :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'overview'}">
                                <i class="fas fa-chart-pie mr-1"></i>
                                库存总览
                            </button>
                            <button
                                @click="activeTab = 'monitor'"
                                class="py-2 px-1 font-medium text-gray-600 hover:text-green-600 transition-colors duration-200"
                                :class="{'border-b-2 border-green-500 text-green-600': activeTab === 'monitor'}">
                                <i class="fas fa-desktop mr-1"></i>
                                实时监控
                            </button>
                            <button
                                @click="activeTab = 'alerts'"
                                class="py-2 px-1 font-medium text-gray-600 hover:text-red-600 transition-colors duration-200"
                                :class="{'border-b-2 border-red-500 text-red-600': activeTab === 'alerts'}">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                库存预警
                                <span v-if="alertsCount > 0" class="ml-1 bg-red-500 text-white text-xs px-2 py-1 rounded-full">{{ alertsCount }}</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 库存总览 -->
                <div v-if="activeTab === 'overview'" class="space-y-6">
                    <!-- 统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div class="stats-value">{{ stats.totalMaterials || 0 }}</div>
                            <div class="stats-label">物料种类</div>
                        </div>
                        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <div class="stats-value">{{ stats.totalProducts || 0 }}</div>
                            <div class="stats-label">成品种类</div>
                        </div>
                        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <div class="stats-value">{{ stats.totalTransactions || 0 }}</div>
                            <div class="stats-label">今日事务</div>
                        </div>
                        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                            <div class="stats-value">{{ stats.lowStockItems || 0 }}</div>
                            <div class="stats-label">低库存项目</div>
                        </div>
                    </div>

                    <!-- 库存分布图表 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="warehouse-card">
                            <h3 class="text-lg font-semibold mb-4">
                                <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                                物料库存分布
                            </h3>
                            <div class="space-y-3">
                                <div v-for="item in topMaterials" :key="item.id" class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="stock-indicator" :class="getStockStatusClass(item)"></span>
                                        <span class="text-sm font-medium">{{ item.name }}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-semibold">{{ item.current_stock || 0 }} {{ item.unit }}</div>
                                        <div class="text-xs text-gray-500">{{ item.code }}</div>
                                    </div>
                                </div>
                                <div v-if="topMaterials.length === 0" class="text-center text-gray-500 py-4">
                                    暂无物料数据
                                </div>
                            </div>
                        </div>

                        <div class="warehouse-card">
                            <h3 class="text-lg font-semibold mb-4">
                                <i class="fas fa-chart-bar text-green-500 mr-2"></i>
                                成品库存分布
                            </h3>
                            <div class="space-y-3">
                                <div v-for="item in topProducts" :key="item.id" class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="stock-indicator" :class="getStockStatusClass(item)"></span>
                                        <span class="text-sm font-medium">{{ item.name }}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-semibold">{{ item.current_stock || 0 }} {{ item.unit }}</div>
                                        <div class="text-xs text-gray-500">{{ item.code }}</div>
                                    </div>
                                </div>
                                <div v-if="topProducts.length === 0" class="text-center text-gray-500 py-4">
                                    暂无成品数据
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近事务 -->
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-history text-purple-500 mr-2"></i>
                            最近事务记录
                        </h3>
                        <div v-if="isLoadingTransactions" class="warehouse-loading">
                            <div class="warehouse-spinner"></div>
                            <span class="ml-2 text-gray-600">加载中...</span>
                        </div>
                        <div v-else-if="recentTransactions.length === 0" class="text-center text-gray-500 py-4">
                            暂无事务记录
                        </div>
                        <div v-else class="timeline">
                            <div v-for="transaction in recentTransactions" :key="transaction.id" class="timeline-item">
                                <div class="timeline-content">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center space-x-2">
                                                <span class="operation-badge" :class="transaction.operation_type">
                                                    {{ getOperationTypeText(transaction.operation_type) }}
                                                </span>
                                                <span class="text-sm font-medium">{{ transaction.item_name }}</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">
                                                数量: {{ transaction.quantity }} {{ transaction.item_unit }} | 
                                                操作人: {{ transaction.operator_name }}
                                            </div>
                                            <div v-if="transaction.notes" class="text-xs text-gray-600 mt-1">
                                                备注: {{ transaction.notes }}
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ formatDateTime(transaction.created_at) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时监控 -->
                <div v-if="activeTab === 'monitor'" class="space-y-6">
                    <!-- 监控控制面板 -->
                    <div class="warehouse-card">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">
                                <i class="fas fa-desktop text-green-500 mr-2"></i>
                                实时监控
                            </h3>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600 mr-2">自动刷新:</span>
                                    <label class="inline-flex items-center cursor-pointer">
                                        <input type="checkbox" v-model="autoRefresh" class="sr-only">
                                        <div class="relative">
                                            <div class="w-10 h-6 bg-gray-200 rounded-full shadow-inner" :class="{'bg-green-400': autoRefresh}"></div>
                                            <div class="absolute w-4 h-4 bg-white rounded-full shadow inset-y-1 left-1 transition-transform duration-300 ease-in-out" :class="{'transform translate-x-4': autoRefresh}"></div>
                                        </div>
                                    </label>
                                </div>
                                <button @click="refreshMonitorData" class="action-btn secondary">
                                    <i class="fas fa-sync-alt"></i>
                                    手动刷新
                                </button>
                            </div>
                        </div>

                        <!-- 筛选控制 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="filter-label">类型</label>
                                    <select v-model="monitorFilter.type" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">全部</option>
                                        <option value="material">物料</option>
                                        <option value="finished_product">成品</option>
                                    </select>
                                </div>
                                
                                <div class="filter-group">
                                    <label class="filter-label">库存状态</label>
                                    <select v-model="monitorFilter.status" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">全部状态</option>
                                        <option value="normal">正常</option>
                                        <option value="low">偏低</option>
                                        <option value="critical">紧急</option>
                                        <option value="out">缺货</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label class="filter-label">搜索</label>
                                    <input type="text" v-model="monitorFilter.search" placeholder="搜索编号、名称..."
                                           class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 监控数据表格 -->
                    <div class="warehouse-card">
                        <div v-if="isLoadingMonitor" class="warehouse-loading">
                            <div class="warehouse-spinner"></div>
                            <span class="ml-2 text-gray-600">加载中...</span>
                        </div>
                        <div v-else-if="filteredMonitorItems.length === 0" class="text-center text-gray-500 py-8">
                            没有找到符合条件的数据
                        </div>
                        <div v-else class="overflow-x-auto">
                            <table class="warehouse-table">
                                <thead>
                                    <tr>
                                        <th>类型</th>
                                        <th>编号</th>
                                        <th>名称</th>
                                        <th>当前库存</th>
                                        <th>库存状态</th>
                                        <th>最小库存</th>
                                        <th>最大库存</th>
                                        <th>最后更新</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="item in paginatedMonitorItems" :key="`${item.type}-${item.id}`">
                                        <td>
                                            <span class="operation-badge" :class="item.type === 'material' ? 'inbound' : 'outbound'">
                                                {{ item.type === 'material' ? '物料' : '成品' }}
                                            </span>
                                        </td>
                                        <td class="font-mono">{{ item.code }}</td>
                                        <td class="font-medium">{{ item.name }}</td>
                                        <td class="font-semibold" :class="getStockTextClass(item)">{{ item.current_stock || 0 }} {{ item.unit }}</td>
                                        <td>
                                            <span class="stock-indicator" :class="getStockStatusClass(item)"></span>
                                            {{ getStockStatusText(item) }}
                                        </td>
                                        <td>{{ item.min_stock || '-' }}</td>
                                        <td>{{ item.max_stock || '-' }}</td>
                                        <td class="text-sm text-gray-500">{{ formatDateTime(item.updated_at) }}</td>
                                        <td>
                                            <button @click="viewItemDetails(item)" class="action-btn primary" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                <i class="fas fa-eye"></i>
                                                详情
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控制 -->
                        <div v-if="monitorTotalPages > 1" class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-500">
                                共 {{ filteredMonitorItems.length }} 条记录，第 {{ monitorCurrentPage }} / {{ monitorTotalPages }} 页
                            </div>
                            <div class="flex space-x-2">
                                <button @click="goToMonitorPage(monitorCurrentPage - 1)" :disabled="monitorCurrentPage <= 1" 
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    上一页
                                </button>
                                <button @click="goToMonitorPage(monitorCurrentPage + 1)" :disabled="monitorCurrentPage >= monitorTotalPages"
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 库存预警 -->
                <div v-if="activeTab === 'alerts'" class="space-y-6">
                    <div class="warehouse-card">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                库存预警
                            </h3>
                            <button @click="refreshAlerts" class="action-btn secondary">
                                <i class="fas fa-sync-alt"></i>
                                刷新预警
                            </button>
                        </div>

                        <div v-if="isLoadingAlerts" class="warehouse-loading">
                            <div class="warehouse-spinner"></div>
                            <span class="ml-2 text-gray-600">加载中...</span>
                        </div>
                        <div v-else-if="alerts.length === 0" class="text-center text-gray-500 py-8">
                            <i class="fas fa-check-circle text-green-500 text-4xl mb-2"></i>
                            <p>暂无库存预警</p>
                        </div>
                        <div v-else class="space-y-4">
                            <div v-for="alert in alerts" :key="`${alert.type}-${alert.id}`" 
                                 class="border rounded-lg p-4" 
                                 :class="{
                                     'border-red-200 bg-red-50': alert.alert_level === 'critical',
                                     'border-orange-200 bg-orange-50': alert.alert_level === 'warning',
                                     'border-gray-200 bg-gray-50': alert.alert_level === 'info'
                                 }">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-start space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-triangle text-2xl" 
                                               :class="{
                                                   'text-red-500': alert.alert_level === 'critical',
                                                   'text-orange-500': alert.alert_level === 'warning',
                                                   'text-gray-500': alert.alert_level === 'info'
                                               }"></i>
                                        </div>
                                        <div>
                                            <div class="flex items-center space-x-2">
                                                <span class="operation-badge" :class="alert.type === 'material' ? 'inbound' : 'outbound'">
                                                    {{ alert.type === 'material' ? '物料' : '成品' }}
                                                </span>
                                                <span class="font-medium">{{ alert.name }}</span>
                                                <span class="text-sm text-gray-500">({{ alert.code }})</span>
                                            </div>
                                            <div class="mt-1 text-sm">
                                                <span class="font-medium">当前库存:</span> 
                                                <span :class="{
                                                    'text-red-600': alert.alert_level === 'critical',
                                                    'text-orange-600': alert.alert_level === 'warning'
                                                }">{{ alert.current_stock || 0 }} {{ alert.unit }}</span>
                                                <span class="text-gray-500 ml-2">最小库存: {{ alert.min_stock || 0 }} {{ alert.unit }}</span>
                                            </div>
                                            <div class="mt-1 text-sm text-gray-600">
                                                {{ alert.alert_message }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="text-xs text-gray-400">
                                            {{ formatDateTime(alert.created_at) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详情模态框 -->
        <div v-if="showDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg p-4 md:p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4 sticky top-0 bg-white pt-1">
                    <h3 class="text-lg font-semibold">{{ selectedItemDetails?.type === 'material' ? '物料' : '成品' }}详情</h3>
                    <button @click="closeDetailsModal" class="text-gray-500 hover:text-gray-700 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div v-if="selectedItemDetails" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold mb-3">基本信息</h4>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">编号：</span>{{ selectedItemDetails.code }}</div>
                                <div><span class="font-medium">名称：</span>{{ selectedItemDetails.name }}</div>
                                <div><span class="font-medium">规格：</span>{{ selectedItemDetails.specification || '-' }}</div>
                                <div><span class="font-medium">单位：</span>{{ selectedItemDetails.unit }}</div>
                                <div><span class="font-medium">描述：</span>{{ selectedItemDetails.description || '-' }}</div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold mb-3">库存信息</h4>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">当前库存：</span>
                                    <span class="font-semibold" :class="getStockTextClass(selectedItemDetails)">
                                        {{ selectedItemDetails.current_stock || 0 }} {{ selectedItemDetails.unit }}
                                    </span>
                                </div>
                                <div><span class="font-medium">最小库存：</span>{{ selectedItemDetails.min_stock || '-' }}</div>
                                <div><span class="font-medium">最大库存：</span>{{ selectedItemDetails.max_stock || '-' }}</div>
                                <div><span class="font-medium">库存状态：</span>
                                    <span class="stock-indicator" :class="getStockStatusClass(selectedItemDetails)"></span>
                                    {{ getStockStatusText(selectedItemDetails) }}
                                </div>
                                <div><span class="font-medium">最后更新：</span>{{ formatDateTime(selectedItemDetails.updated_at) }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近事务记录 -->
                    <div>
                        <h4 class="font-semibold mb-3">最近事务记录</h4>
                        <div v-if="itemTransactions.length === 0" class="text-center text-gray-500 py-4">
                            暂无事务记录
                        </div>
                        <div v-else class="space-y-2 max-h-64 overflow-y-auto">
                            <div v-for="transaction in itemTransactions.slice(0, 10)" :key="transaction.id"
                                 class="border border-gray-200 rounded p-3">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="flex items-center space-x-2">
                                            <span class="operation-badge" :class="transaction.operation_type">
                                                {{ getOperationTypeText(transaction.operation_type) }}
                                            </span>
                                            <span class="text-sm font-medium">数量: {{ transaction.quantity }} {{ transaction.item_unit }}</span>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            操作人: {{ transaction.operator_name }}
                                        </div>
                                        <div v-if="transaction.notes" class="text-xs text-gray-600 mt-1">
                                            备注: {{ transaction.notes }}
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        {{ formatDateTime(transaction.created_at) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end pt-4">
                        <button @click="closeDetailsModal" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/warehouse/inventory-monitor.js"></script>
</body>
</html>
