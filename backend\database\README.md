# 数据库管理系统说明

## 📋 概述

本系统使用PostgreSQL作为主数据库，提供完整的数据库初始化、验证和迁移管理功能。

## 🏗️ 系统架构

### 核心组件

1. **`initializeDatabase.js`** - 主数据库初始化脚本
   - 创建所有数据库表结构
   - 执行环境检查和验证
   - 支持任何服务器环境部署

2. **`migrationManager.js`** - PostgreSQL迁移管理器
   - 管理数据库结构变更
   - 支持增量迁移
   - 完全PostgreSQL兼容

3. **`databaseAdapter.js`** - 数据库适配器
   - 统一数据库访问接口
   - 连接池管理
   - 性能监控

## ✅ 方案A改进完成

### 🚨 重要发现和修复

**数据库结构验证结果：**
- ✅ **数据库完全正确** - 所有64个表都有完整的 `created_at` 和 `updated_at` 字段
- ✅ **SQLite语法完全清除** - 修复了 `INTEGER PRIMARY KEY` 和 `REAL` 数据类型
- ⚠️ **初始化脚本部分不一致** - 22个表的定义需要同步更新

**修复的SQLite语法错误：**
1. `performance_logs` 表：`INTEGER PRIMARY KEY` → `VARCHAR(50) PRIMARY KEY`
2. `equipment_health` 表：`REAL` → `DECIMAL(5,4)`
3. 添加了缺失的时间戳字段定义

### 已实现的改进

#### 1. 完善的环境检查
- ✅ PostgreSQL版本检查
- ✅ 数据库连接测试
- ✅ 用户权限验证
- ✅ 字符编码检查
- ✅ 必要扩展检查（UUID等）

#### 2. 表结构完整性验证
- ✅ 自动检测所有数据表
- ✅ 核心表存在性验证
- ✅ 关键字段完整性检查
- ✅ 详细的验证日志

#### 3. PostgreSQL迁移管理器
- ✅ 完全移除SQLite语法
- ✅ 使用PostgreSQL连接池
- ✅ 支持事务管理
- ✅ 错误处理和回滚

#### 4. 智能迁移处理
- ✅ 自动检测过时迁移脚本
- ✅ 清理冗余迁移文件
- ✅ 支持未来增量迁移

## 🚀 系统启动流程

```
系统启动
    ↓
1. 数据库连接测试
    ↓
2. 环境检查
    ├── PostgreSQL版本
    ├── 数据库名称
    ├── 用户权限
    ├── 字符编码
    └── 必要扩展
    ↓
3. 创建所有表结构 (64个表)
    ↓
4. 表结构完整性验证
    ├── 核心表检查
    └── 关键字段验证
    ↓
5. 执行数据库迁移
    ↓
6. 用户和部门初始化
    ↓
✅ 系统启动完成
```

## 📊 数据库表统计

- **总表数**: 64个
- **核心表**: 10个（users, applications, equipment等）
- **功能模块表**: 54个
- **所有表都包含**: `created_at`, `updated_at` 字段

## 🔧 环境兼容性

### 支持的环境
- ✅ Windows Server
- ✅ Linux (Ubuntu/CentOS)
- ✅ Docker容器
- ✅ 云服务器（AWS/Azure/阿里云）

### 数据库要求
- **PostgreSQL**: 12.0+（推荐15.0+）
- **内存**: 最低2GB
- **存储**: 最低10GB可用空间

## 🛠️ 维护指南

### 增加新表
1. 在 `initializeDatabase.js` 中添加 `createXxxTable()` 方法
2. 在 `initializeAllTables()` 中调用新方法
3. 重启系统自动创建新表

### 修改表结构
1. 创建迁移脚本在 `migrations/` 目录
2. 在 `migrationManager.js` 中注册迁移
3. 系统启动时自动执行迁移

### 数据库检查
系统启动时自动执行：
- 连接测试
- 环境验证
- 表结构检查
- 迁移状态检查

## 🚨 故障排除

### 常见问题

1. **连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接参数（.env文件）
   - 确认防火墙设置

2. **权限错误**
   - 确保数据库用户有CREATE权限
   - 检查数据库访问权限

3. **表创建失败**
   - 查看详细错误日志
   - 检查磁盘空间
   - 验证PostgreSQL版本兼容性

## 📝 日志说明

### 启动日志示例
```
✅ 数据库连接测试成功
✅ PostgreSQL版本: PostgreSQL 15.13
✅ 当前数据库: makrite_system
✅ 数据库用户: postgres
✅ UUID扩展已启用
✅ 数据库编码: UTF8
✅ 发现 64 个数据表
✅ 所有核心表结构验证通过
✅ 没有待执行的迁移脚本
✅ 数据库表初始化完成！
```

## 🔮 未来扩展

### 计划功能
- 自动备份机制
- 性能监控面板
- 数据库健康检查API
- 自动表结构同步工具

---

**注意**: 本系统已完全移除SQLite依赖，专为PostgreSQL优化设计。
