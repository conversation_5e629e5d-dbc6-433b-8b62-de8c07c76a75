<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检测报告列表 - 质量管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/quality/common.css">

</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto"></div>
            <p class="mt-4 text-gray-600">加载中...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="flex min-h-screen" style="display: none;">
        <!-- 侧边栏 -->
        <sidebar 
            :user="currentUser" 
            :sidebar-open="sidebarOpen"
        ></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 md:hidden">
                <div class="flex items-center justify-between px-4 py-3">
                    <button 
                        @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-600 hover:text-gray-900 focus:outline-none"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">检测报告</h1>
                    <div class="w-6"></div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6">
                <!-- 页面标题和操作按钮 -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">检测报告</h1>
                        <p class="mt-2 text-gray-600">查看和管理质量检测报告</p>
                    </div>
                    <div class="mt-4 sm:mt-0">
                        <a
                            v-if="canUpload"
                            href="/quality-upload"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            上传检测报告
                        </a>
                    </div>
                </div>

                <!-- 报告列表 -->
                <quality-report-list
                    ref="listComponent"
                    :current-user="currentUser"
                    @view-detail="handleViewDetail"
                    @edit-report="handleEditReport"
                    @delete-report="handleDeleteReport"
                ></quality-report-list>
            </main>
        </div>

        <!-- 报告详情模态框 -->
        <div v-if="showDetailModal" class="modal-overlay" @click.self="closeDetailModal">
            <div class="modal-content">
                <quality-report-detail
                    :report="selectedReport"
                    :current-user="currentUser"
                    @close="closeDetailModal"
                    @edit="handleEditFromDetail"
                    @delete="handleDeleteFromDetail"
                ></quality-report-detail>
            </div>
        </div>
    </div>

    <!-- 遮罩层（移动端侧边栏） -->
    <div 
        v-if="sidebarOpen" 
        @click="sidebarOpen = false"
        class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
    ></div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/quality/list.js"></script>
</body>
</html>
