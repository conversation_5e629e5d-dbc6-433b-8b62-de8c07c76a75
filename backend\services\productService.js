/**
 * 产品服务层
 * 处理产品相关的业务逻辑
 */

const { databaseAdapter } = require('../database/databaseAdapter');
const { ProductModel, ProductionProcessModel } = require('../models/productModel');
const logger = require('../utils/logger');

class ProductService {
    constructor() {
        this.productRepository = databaseAdapter.getProductRepository();
    }

    /**
     * 创建产品
     * @param {Object} productData 产品数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createProduct(productData, userId) {
        try {
            // 生成产品ID
            const productId = ProductModel.generateId();
            
            // 创建产品模型
            const product = new ProductModel({
                ...productData,
                id: productId
            });

            // 验证数据
            const validation = product.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 检查产品编码是否已存在
            const existingProduct = await this.productRepository.findByCode(product.code);
            if (existingProduct) {
                return {
                    success: false,
                    message: '产品编码已存在'
                };
            }

            // 创建产品
            const createdProduct = await this.productRepository.create(product);

            logger.info('产品创建成功', { 
                productId: createdProduct.id, 
                productCode: createdProduct.code,
                createdBy: userId 
            });

            return {
                success: true,
                message: '产品创建成功',
                data: createdProduct
            };
        } catch (error) {
            logger.error('产品创建失败', { error: error.message, userId });
            return {
                success: false,
                message: '产品创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取产品列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 产品列表结果
     */
    async getProducts(options = {}) {
        try {
            const result = await this.productRepository.findAll(options);

            logger.info('获取产品列表成功', { 
                total: result.total, 
                page: result.page 
            });

            return {
                success: true,
                data: result
            };
        } catch (error) {
            logger.error('获取产品列表失败', { error: error.message });
            return {
                success: false,
                message: '获取产品列表失败',
                error: error.message
            };
        }
    }

    /**
     * 根据ID获取产品详情
     * @param {string} productId 产品ID
     * @returns {Promise<Object>} 产品详情结果
     */
    async getProductById(productId) {
        try {
            const product = await this.productRepository.findById(productId);
            
            if (!product) {
                return {
                    success: false,
                    message: '产品不存在'
                };
            }

            // 获取产品的工艺流程
            const processes = await this.productRepository.getProductionProcesses(productId);

            logger.info('获取产品详情成功', { productId });

            return {
                success: true,
                data: {
                    product,
                    processes
                }
            };
        } catch (error) {
            logger.error('获取产品详情失败', { error: error.message, productId });
            return {
                success: false,
                message: '获取产品详情失败',
                error: error.message
            };
        }
    }

    /**
     * 更新产品
     * @param {string} productId 产品ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 更新用户ID
     * @returns {Promise<Object>} 更新结果
     */
    async updateProduct(productId, updateData, userId) {
        try {
            // 检查产品是否存在
            const existingProduct = await this.productRepository.findById(productId);
            if (!existingProduct) {
                return {
                    success: false,
                    message: '产品不存在'
                };
            }

            // 如果更新产品编码，检查是否与其他产品冲突
            if (updateData.code && updateData.code !== existingProduct.code) {
                const productWithSameCode = await this.productRepository.findByCode(updateData.code);
                if (productWithSameCode) {
                    return {
                        success: false,
                        message: '产品编码已存在'
                    };
                }
            }

            // 更新产品
            const updatedProduct = await this.productRepository.update(productId, updateData);

            logger.info('产品更新成功', { 
                productId, 
                updatedBy: userId 
            });

            return {
                success: true,
                message: '产品更新成功',
                data: updatedProduct
            };
        } catch (error) {
            logger.error('产品更新失败', { error: error.message, productId, userId });
            return {
                success: false,
                message: '产品更新失败',
                error: error.message
            };
        }
    }

    /**
     * 删除产品
     * @param {string} productId 产品ID
     * @param {string} userId 删除用户ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteProduct(productId, userId) {
        try {
            // 检查产品是否存在
            const existingProduct = await this.productRepository.findById(productId);
            if (!existingProduct) {
                return {
                    success: false,
                    message: '产品不存在'
                };
            }

            // 删除产品的工艺流程
            await this.productRepository.deleteProcessesByProductId(productId);

            // 删除产品
            const deleted = await this.productRepository.delete(productId);

            if (!deleted) {
                return {
                    success: false,
                    message: '产品删除失败'
                };
            }

            logger.info('产品删除成功', { 
                productId, 
                deletedBy: userId 
            });

            return {
                success: true,
                message: '产品删除成功'
            };
        } catch (error) {
            logger.error('产品删除失败', { error: error.message, productId, userId });
            return {
                success: false,
                message: '产品删除失败',
                error: error.message
            };
        }
    }

    /**
     * 创建生产工艺流程
     * @param {string} productId 产品ID
     * @param {Array} processesData 工艺流程数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createProductionProcesses(productId, processesData, userId) {
        try {
            // 检查产品是否存在
            const product = await this.productRepository.findById(productId);
            if (!product) {
                return {
                    success: false,
                    message: '产品不存在'
                };
            }

            // 删除现有的工艺流程
            await this.productRepository.deleteProcessesByProductId(productId);

            // 创建新的工艺流程
            const createdProcesses = [];
            for (const processData of processesData) {
                const processId = ProductionProcessModel.generateId();
                const process = new ProductionProcessModel({
                    ...processData,
                    id: processId,
                    productId: productId
                });

                // 验证数据
                const validation = process.validate();
                if (!validation.isValid) {
                    return {
                        success: false,
                        message: `工序"${process.processName}"数据验证失败`,
                        errors: validation.errors
                    };
                }

                const createdProcess = await this.productRepository.createProcess(process);
                createdProcesses.push(createdProcess);
            }

            logger.info('生产工艺流程创建成功', { 
                productId, 
                processCount: createdProcesses.length,
                createdBy: userId 
            });

            return {
                success: true,
                message: '生产工艺流程创建成功',
                data: createdProcesses
            };
        } catch (error) {
            logger.error('生产工艺流程创建失败', { error: error.message, productId, userId });
            return {
                success: false,
                message: '生产工艺流程创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取所有产品的简要信息（用于下拉选择）
     * @returns {Promise<Object>} 产品选项列表
     */
    async getProductOptions() {
        try {
            const products = await this.productRepository.findAll();

            const options = products.map(product => ({
                id: product.id,
                code: product.code || product.id,
                name: product.name,
                label: `${product.code || product.id} - ${product.name}`
            }));

            logger.info('获取产品选项成功', { count: options.length });

            return {
                success: true,
                data: options
            };
        } catch (error) {
            logger.error('获取产品选项失败', { error: error.message });
            return {
                success: false,
                message: '获取产品选项失败',
                error: error.message
            };
        }
    }
}

module.exports = ProductService;
