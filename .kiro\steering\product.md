# 产品概述

## 智能化企业管理系统 V2.0

基于 Node.js 和 Vue.js 构建的现代化智能企业管理系统，集成AI智能排产算法，提供完整的申请流程管理、智能生产排程、产能优化、质量管理和设备管理功能。

## 核心特性

- **现代化架构**: 前后端分离，Vue.js 3 + Node.js + Express
- **AI智能排产**: 5种优化策略算法，排程效率提升40%
- **企业级安全**: JWT认证，RBAC权限控制，审计追踪
- **响应式设计**: 完美适配桌面端和移动端
- **完整工作流**: 多级审批流程，实时状态跟踪
- **智能分析**: 实时监控，性能分析，预测性维护
- **现代化UI**: Tailwind CSS，专业美观的界面
- **文件管理**: 多格式文件上传，安全存储
- **高可用性**: 支持本地和局域网部署，高并发处理

## 系统模块

### 已实现模块 (8个)
1. 用户认证与权限管理
2. 申请管理系统
3. 智能排产系统
4. 产品管理系统
5. 设备管理系统
6. 质量管理系统
7. 生产排程管理
8. 仓库管理系统

### 规划中模块 (1个)
9. 文档管理系统

## 目标用户

- 制造业企业
- 生产管理人员
- 工厂管理员
- 质量控制团队
- 设备维护人员
- 系统管理员

## 部署特点

- **本地部署**: 支持本地和局域网访问
- **企业级**: PostgreSQL数据库，支持高并发
- **安全性**: 完整的权限控制和审计追踪
- **性能**: 通过缓存和优化实现200%+整体性能提升