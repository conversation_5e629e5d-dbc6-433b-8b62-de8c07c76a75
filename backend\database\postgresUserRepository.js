/**
 * PostgreSQL用户数据访问层
 * 替换SQLite的userRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresUserRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL用户数据访问层初始化完成');
        }
    }

    /**
     * 获取所有用户
     */
    async findAll() {
        try {
            const users = await this.findMany(`
                SELECT * FROM users 
                ORDER BY created_at DESC
            `);
            return users.map(user => this.transformUser(user));
        } catch (error) {
            logger.error('获取所有用户失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找用户
     */
    async findById(id) {
        try {
            const user = await this.findOne('SELECT * FROM users WHERE id = $1', [id]);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据ID查找用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户代码查找用户
     */
    async findByUsercode(usercode) {
        try {
            const user = await this.findOne('SELECT * FROM users WHERE usercode = $1', [usercode]);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据用户代码查找用户失败 (${usercode}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户名查找用户
     */
    async findByUsername(username) {
        try {
            const user = await this.findOne('SELECT * FROM users WHERE username = $1', [username]);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据用户名查找用户失败 (${username}):`, error);
            throw error;
        }
    }

    /**
     * 根据邮箱查找用户
     */
    async findByEmail(email) {
        try {
            const user = await this.findOne('SELECT * FROM users WHERE email = $1', [email]);
            return user ? this.transformUser(user) : null;
        } catch (error) {
            logger.error(`根据邮箱查找用户失败 (${email}):`, error);
            throw error;
        }
    }

    /**
     * 创建新用户
     */
    async create(userData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO users (
                    id, usercode, username, password, role, department, email, 
                    active, permissions, has_signature, signature_path, signature_base64,
                    created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                RETURNING *
            `, [
                userData.id,
                userData.usercode,
                userData.username,
                userData.password,
                userData.role,
                userData.department || '',
                userData.email || '',
                userData.active !== false,
                JSON.stringify(userData.permissions || []),
                userData.hasSignature || false,
                userData.signaturePath || null,
                userData.signatureBase64 || null,
                now,
                now
            ]);

            return this.transformUser(result.rows[0]);
        } catch (error) {
            logger.error('创建用户失败:', error);
            throw error;
        }
    }

    /**
     * 更新用户
     */
    async update(id, userData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE users SET 
                    usercode = $2, username = $3, password = $4, role = $5,
                    department = $6, email = $7, active = $8, permissions = $9,
                    has_signature = $10, signature_path = $11, signature_base64 = $12,
                    updated_at = $13
                WHERE id = $1
                RETURNING *
            `, [
                id,
                userData.usercode,
                userData.username,
                userData.password,
                userData.role,
                userData.department || '',
                userData.email || '',
                userData.active !== false,
                JSON.stringify(userData.permissions || []),
                userData.hasSignature || false,
                userData.signaturePath || null,
                userData.signatureBase64 || null,
                now
            ]);

            return result.rows.length > 0 ? this.transformUser(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 更新最后登录时间
     */
    async updateLastLogin(id) {
        try {
            const now = new Date().toISOString();
            
            await this.query(`
                UPDATE users SET 
                    last_login_at = $2, last_active_at = $2, updated_at = $2
                WHERE id = $1
            `, [id, now]);

            return true;
        } catch (error) {
            logger.error(`更新最后登录时间失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除用户
     */
    async delete(id) {
        try {
            const rowCount = await super.delete('DELETE FROM users WHERE id = $1', [id]);
            return rowCount > 0;
        } catch (error) {
            logger.error(`删除用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 停用用户
     */
    async deactivate(id) {
        try {
            const now = new Date().toISOString();
            const rowCount = await super.update(
                'UPDATE users SET active = false, updated_at = $2 WHERE id = $1',
                [id, now]
            );
            return rowCount > 0;
        } catch (error) {
            logger.error(`停用用户失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查用户代码是否存在
     */
    async isUsercodeExists(usercode, excludeId = null) {
        try {
            let query = 'SELECT COUNT(*) as count FROM users WHERE usercode = $1';
            let params = [usercode];
            
            if (excludeId) {
                query += ' AND id != $2';
                params.push(excludeId);
            }
            
            const result = await this.query(query, params);
            return parseInt(result.rows[0].count) > 0;
        } catch (error) {
            logger.error(`检查用户代码是否存在失败 (${usercode}):`, error);
            throw error;
        }
    }

    /**
     * 根据角色查找用户
     */
    async findByRole(role) {
        try {
            const users = await this.findMany(
                'SELECT * FROM users WHERE role = $1 AND active = true ORDER BY username',
                [role]
            );
            return users.map(user => this.transformUser(user));
        } catch (error) {
            logger.error(`根据角色查找用户失败 (${role}):`, error);
            throw error;
        }
    }

    /**
     * 根据部门查找用户
     */
    async findByDepartment(department) {
        try {
            const users = await this.findMany(
                'SELECT * FROM users WHERE department = $1 AND active = true ORDER BY username',
                [department]
            );
            return users.map(user => this.transformUser(user));
        } catch (error) {
            logger.error(`根据部门查找用户失败 (${department}):`, error);
            throw error;
        }
    }

    /**
     * 获取活跃用户
     */
    async findActive() {
        try {
            const users = await this.findMany(
                'SELECT * FROM users WHERE active = true ORDER BY username'
            );
            return users.map(user => this.transformUser(user));
        } catch (error) {
            logger.error('获取活跃用户失败:', error);
            throw error;
        }
    }

    /**
     * 转换用户数据
     */
    transformUser(user) {
        if (!user) return null;
        
        return {
            id: user.id,
            usercode: user.usercode,
            username: user.username,
            password: user.password,
            role: user.role,
            department: user.department,
            email: user.email,
            active: user.active,
            permissions: this.parseJSON(user.permissions, []),
            hasSignature: user.has_signature,
            signaturePath: user.signature_path,
            signatureBase64: user.signature_base64,
            lastLoginAt: user.last_login_at,
            lastActiveAt: user.last_active_at,
            createdAt: user.created_at,
            updatedAt: user.updated_at
        };
    }

    /**
     * 生成用户ID
     */
    generateId() {
        return super.generateId();
    }
}

module.exports = PostgresUserRepository;
