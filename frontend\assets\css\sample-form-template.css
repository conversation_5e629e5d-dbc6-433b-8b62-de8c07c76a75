/**
 * 样品单模板样式
 * 包括A4纸张样式、表格样式、响应式设计和打印样式
 */

/* 样品单模板样式 */
.sample-form-template {
    width: 100%;
    font-family: "Microsoft YaHei", SimSun, serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    padding: 0;
    box-sizing: border-box;
    max-height: 267mm; /* A4高度减去边距 */
    overflow: hidden;
}

/* 模板头部 */
.template-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #2563eb;
    padding-bottom: 20px;
}

.template-title {
    font-size: 28px;
    font-weight: bold;
    color: #2563eb;
    margin: 0 0 15px 0;
}

.template-info {
    margin-top: 15px;
}

.info-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.info-label {
    font-weight: bold;
    color: #374151;
}

.info-value {
    color: #111827;
    border-bottom: 1px solid #d1d5db;
    padding-bottom: 2px;
    min-width: 120px;
    text-align: center;
}

/* 模板区块 */
.template-section {
    margin-bottom: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    background-color: #fafafa;
    page-break-inside: avoid;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #2563eb;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #d1d5db;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.info-item .label {
    font-weight: bold;
    color: #374151;
    min-width: 100px;
    flex-shrink: 0;
}

.info-item .value {
    color: #111827;
    flex: 1;
    border-bottom: 1px solid #d1d5db;
    padding-bottom: 2px;
    margin-left: 10px;
    min-height: 20px;
}

/* 子部分样式 */
.subsection {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #e5e7eb;
}

.subsection-title {
    font-size: 14px;
    font-weight: bold;
    color: #1f2937;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #f3f4f6;
}

/* 包装选项样式 */
.packaging-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.packaging-option {
    display: flex;
    flex-direction: column;
    padding: 8px;
    background-color: #f9fafb;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.option-label {
    font-weight: bold;
    color: #374151;
    font-size: 12px;
}

.option-value {
    color: #111827;
    font-size: 12px;
    margin-top: 2px;
}

.option-detail {
    color: #6b7280;
    font-size: 11px;
    margin-top: 4px;
    font-style: italic;
}

/* 测试项目样式 */
.test-items-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
}

.test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px;
    background-color: #f3f4f6;
    border-radius: 4px;
    border: 1px solid #d1d5db;
}

.test-name {
    font-size: 12px;
    color: #374151;
    flex: 1;
}

.test-level {
    font-size: 11px;
    color: #2563eb;
    background-color: #dbeafe;
    padding: 1px 6px;
    border-radius: 3px;
    margin-left: 8px;
}

.test-count {
    font-size: 11px;
    color: #059669;
    background-color: #d1fae5;
    padding: 1px 6px;
    border-radius: 3px;
    margin-left: 4px;
}

.test-notes {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    color: #374151;
    white-space: pre-wrap;
}

/* 附件显示样式 */
.attachments-section {
    margin-top: 20px;
}

.attachments-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

/* 图档显示样式 */
.attachment-image-item {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
    page-break-inside: avoid;
}

.attachment-image-header {
    padding: 8px 12px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attachment-type-label {
    font-size: 12px;
    font-weight: bold;
    color: #2563eb;
    background-color: #dbeafe;
    padding: 2px 8px;
    border-radius: 4px;
}

.attachment-filename {
    font-size: 11px;
    color: #64748b;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.attachment-image-container {
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;
    max-height: 300px;
    background-color: #f9fafb;
}

.attachment-image {
    max-width: 100%;
    max-height: 280px;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 文件附件样式 */
.attachment-file-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #f3f4f6;
    border-radius: 6px;
    border: 1px solid #d1d5db;
}

.attachment-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    color: #2563eb;
}

.attachment-name {
    font-size: 12px;
    color: #374151;
    flex: 1;
    word-break: break-all;
}

.attachment-type {
    font-size: 10px;
    color: #6b7280;
    background-color: #e5e7eb;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

/* 测试信息样式 */
.test-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.test-item {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 10px;
}

.test-item-name {
    font-weight: bold;
    color: #374151;
    margin-bottom: 5px;
}

.test-item-level {
    color: #2563eb;
    font-size: 12px;
}

/* A4纸张样式 */
.a4-page {
    width: 210mm;
    min-height: 297mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 15mm;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
}

/* 确保内容适应A4页面 */
.sample-form-template {
    width: 100%;
    max-width: 180mm; /* 留出边距 */
    margin: 0 auto;
    font-size: 12px;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sample-form-template {
        font-size: 12px;
        padding: 15px;
    }
    
    .template-title {
        font-size: 24px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .a4-page {
        width: 100%;
        min-height: auto;
        padding: 10mm;
        box-shadow: none;
    }
}

/* 打印样式 */
@media print {
    .sample-form-template {
        font-size: 11px;
        color: black;
    }

    .template-header {
        border-bottom: 2px solid black;
    }

    .template-title {
        color: black;
        font-size: 24px;
    }

    .section-title {
        color: black;
        border-bottom: 1px solid black;
        font-size: 14px;
    }

    .template-section {
        border: 1px solid black;
        background-color: white;
        page-break-inside: avoid;
        margin-bottom: 10px;
    }

    .info-item .value {
        border-bottom: 1px solid black;
    }

    .info-value {
        border-bottom: 1px solid black;
    }

    .a4-page {
        width: 100%;
        height: auto;
        margin: 0;
        padding: 15mm;
        box-shadow: none;
        page-break-after: always;
    }

    /* 图档打印优化 */
    .attachment-image-item {
        border: 1px solid black;
        page-break-inside: avoid;
        margin-bottom: 10px;
    }

    .attachment-image-header {
        background-color: #f0f0f0;
        border-bottom: 1px solid black;
    }

    .attachment-image {
        max-height: 200px;
        width: auto;
    }

    .attachment-type-label {
        background-color: #e0e0e0;
        color: black;
    }

    /* 隐藏不需要打印的元素 */
    .no-print {
        display: none !important;
    }
}

/* 特殊内容样式 */
.multi-line-content {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.boolean-value {
    font-weight: bold;
}

.boolean-value.true {
    color: #059669;
}

.boolean-value.false {
    color: #dc2626;
}

/* 表格样式（如果需要） */
.template-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.template-table th,
.template-table td {
    border: 1px solid #d1d5db;
    padding: 8px 12px;
    text-align: left;
}

.template-table th {
    background-color: #f3f4f6;
    font-weight: bold;
    color: #374151;
}

.template-table tr:nth-child(even) {
    background-color: #f9fafb;
}

/* 签名区域样式 */
.signature-section {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.signature-item {
    text-align: center;
    min-width: 150px;
}

.signature-label {
    font-weight: bold;
    margin-bottom: 30px;
    color: #374151;
}

.signature-line {
    border-bottom: 1px solid #374151;
    height: 1px;
    margin-bottom: 5px;
}

.signature-date {
    font-size: 12px;
    color: #6b7280;
}
