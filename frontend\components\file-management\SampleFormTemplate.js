/**
 * 样品单PDF模板组件
 * 用于生成专业的样品寄送通知单PDF文档
 */

export default {
    name: 'SampleFormTemplate',
    props: {
        sampleForm: {
            type: Object,
            required: true
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        const { computed, nextTick } = Vue;

        // 关闭模板
        function closeSampleTemplate() {
            emit('close');
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        // 格式化布尔值
        function formatBoolean(value) {
            return value ? '是' : '否';
        }

        // 格式化数组
        function formatArray(arr) {
            if (!arr || !Array.isArray(arr)) return '';
            return arr.join('、');
        }

        // 动态加载PDF库
        async function loadPdfLibraries() {
            return new Promise((resolve, reject) => {
                if (window.html2canvas && (window.jsPDF || window.jspdf)) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 2;

                function checkComplete() {
                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        setTimeout(() => resolve(), 100);
                    }
                }

                // 加载 html2canvas
                if (!window.html2canvas) {
                    const script1 = document.createElement('script');
                    script1.src = '/js/libs/html2canvas.min.js';
                    script1.onload = checkComplete;
                    script1.onerror = () => reject(new Error('html2canvas库加载失败'));
                    document.head.appendChild(script1);
                } else {
                    checkComplete();
                }

                // 加载 jsPDF
                if (!window.jsPDF && !window.jspdf) {
                    const script2 = document.createElement('script');
                    script2.src = '/js/libs/jspdf.umd.min.js';
                    script2.onload = checkComplete;
                    script2.onerror = () => reject(new Error('jsPDF库加载失败'));
                    document.head.appendChild(script2);
                } else {
                    checkComplete();
                }
            });
        }

        // 响应式图片数据
        const { ref, computed } = Vue;
        const attachmentImages = ref({});
        const isLoadingImages = ref(false);

        // 加载单个图片
        async function loadSingleImage(attachment) {
            try {
                const apiUrl = `/api/file-management/sample/${props.sampleForm.id}/attachments/${attachment.id}/download`;
                console.log('加载图片:', attachment.original_filename, 'API:', apiUrl);

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const imageUrl = URL.createObjectURL(blob);

                    // 更新响应式数据
                    attachmentImages.value = {
                        ...attachmentImages.value,
                        [attachment.id]: imageUrl
                    };

                    console.log('图片加载成功:', attachment.original_filename);
                    return imageUrl;
                } else {
                    console.error('图片下载失败:', response.status);
                    return null;
                }
            } catch (error) {
                console.error('加载图片失败:', error);
                return null;
            }
        }

        // 加载所有图片
        async function loadAttachmentImages() {
            if (!props.sampleForm.attachments || props.sampleForm.attachments.length === 0) {
                return;
            }

            isLoadingImages.value = true;
            console.log('开始加载所有图片...');

            const imageAttachments = props.sampleForm.attachments.filter(attachment =>
                attachment.mime_type && attachment.mime_type.startsWith('image/')
            );

            for (const attachment of imageAttachments) {
                await loadSingleImage(attachment);
            }

            isLoadingImages.value = false;
            console.log('所有图片加载完成');
        }



        // 下载样品单PDF
        async function downloadSampleFormPDF() {
            const element = document.getElementById('sampleFormTemplatePage');
            if (!element) {
                alert('样品单模板元素未找到');
                return;
            }

            const filename = `样品寄送通知单_${props.sampleForm.companyName}_${props.sampleForm.sampleNumber}_${new Date().toISOString().slice(0, 10)}.pdf`;

            // 显示加载提示
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            loadingMsg.textContent = '正在生成PDF，请稍候...';
            document.body.appendChild(loadingMsg);

            try {
                // 加载PDF库
                await loadPdfLibraries();

                // 加载附件图档（采用占位符替换机制）
                await loadAttachmentImages();

                // 等待DOM更新和图片渲染（参考申请管理的处理方式）
                await nextTick();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待图片完全渲染

                console.log('开始生成PDF，所有图片应该已经加载完成');

                // 使用html2canvas将元素转换为canvas
                const canvas = await window.html2canvas(element, {
                    scale: 2, // 提高清晰度
                    useCORS: true, // 允许加载跨域图片
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    imageTimeout: 15000, // 增加图片超时时间
                    removeContainer: false
                });

                // 使用jsPDF将canvas转换为PDF
                let jsPDF;
                if (window.jspdf && window.jspdf.jsPDF) {
                    jsPDF = window.jspdf.jsPDF;
                } else if (window.jsPDF) {
                    jsPDF = window.jsPDF;
                } else {
                    throw new Error('jsPDF库未正确加载');
                }

                const pdf = new jsPDF('p', 'mm', 'a4');

                // 计算宽高比例
                const imgData = canvas.toDataURL('image/png');
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;
                const ratio = Math.min(pageWidth / canvasWidth, pageHeight / canvasHeight);
                const imgWidth = canvasWidth * ratio;
                const imgHeight = canvasHeight * ratio;

                // 添加图像到PDF
                pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

                // 下载PDF
                pdf.save(filename);

                console.log('样品单PDF生成成功:', filename);

            } catch (error) {
                console.error('生成样品单PDF失败:', error);
                alert('生成PDF失败: ' + error.message);
            } finally {
                // 移除加载提示
                if (loadingMsg && loadingMsg.parentNode) {
                    loadingMsg.parentNode.removeChild(loadingMsg);
                }
            }
        }

        // 生成样品单模板HTML
        const templateHTML = computed(() => {
            if (!props.sampleForm) return '';

            const form = props.sampleForm;

            // 调试：检查附件数据
            console.log('模板生成时的样品单数据:', form);
            console.log('附件数据:', form.attachments);
            console.log('附件数量:', form.attachments ? form.attachments.length : 0);

            // 预处理数据，避免在模板字符串中调用函数
            const processedData = {
                date: formatDate(form.date || form.createdAt),
                colorBox: formatBoolean(form.colorBox),
                tape: formatBoolean(form.tape),
                blisterPack: formatBoolean(form.blisterPack),
                other: formatBoolean(form.other),
                testItems: formatArray(form.testItems),
                packagingMaterials: formatArray(form.packagingMaterials),
                recipients: formatArray(form.recipients?.map(r => r.username || r.name)),
                ccRecipients: formatArray(form.ccRecipients?.map(r => r.username || r.name)),
                hasValve: form.valve === 'with',
                needValvePrint: form.valvePrint === 'need',
                hasAdjustmentBuckle: form.adjustmentBuckle === 'with',
                needInternalTest: form.internalTest === 'yes'
            };

            return `
                <div class="sample-form-template">
                    <!-- 标题 -->
                    <div class="template-header">
                        <h1 class="template-title">样品寄送通知单</h1>
                        <div class="template-info">
                            <div class="info-row">
                                <span class="info-label">编号：</span>
                                <span class="info-value">${form.sampleNumber || ''}</span>
                                <span class="info-label">日期：</span>
                                <span class="info-value">${processedData.date}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="template-section">
                        <h3 class="section-title">基本信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">公司名称：</span>
                                <span class="value">${form.companyName || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">收件人：</span>
                                <span class="value">${form.recipient || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">联系方式：</span>
                                <span class="value">${form.contactInfo || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">地址：</span>
                                <span class="value">${form.address || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">邮寄目的：</span>
                                <span class="value">${form.purpose || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">申请用途：</span>
                                <span class="value">${form.application || ''}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 产品规格 -->
                    <div class="template-section">
                        <h3 class="section-title">产品规格</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">产品型号：</span>
                                <span class="value">${form.productModel || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">产品描述：</span>
                                <span class="value">${form.productDescription || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">外层：</span>
                                <span class="value">${form.outerLayer || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">中层：</span>
                                <span class="value">${form.middleLayer || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">内层：</span>
                                <span class="value">${form.innerLayer || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">头带：</span>
                                <span class="value">${form.headband || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">鼻梁条：</span>
                                <span class="value">${form.noseBridge || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">耳带：</span>
                                <span class="value">${form.earLoop || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">气阀：</span>
                                <span class="value">${processedData.hasValve ? '有' : '无'}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">调整扣：</span>
                                <span class="value">${processedData.hasAdjustmentBuckle ? '有' : '无'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 气阀详细信息 -->
                    ${processedData.hasValve ? `
                    <div class="template-section">
                        <h3 class="section-title">气阀详细信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">气阀上下盖：</span>
                                <span class="value">${form.valveTopCover || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">气阀片：</span>
                                <span class="value">${form.valveSheet || ''}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">气阀颜色：</span>
                                <span class="value">${form.valveColor || ''}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    <!-- 客制化说明 -->
                    <div class="template-section">
                        <h3 class="section-title">客制化说明</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">数量：</span>
                                <span class="value">${form.quantity || ''} 个</span>
                            </div>
                            <div class="info-item">
                                <span class="label">头带颜色：</span>
                                <span class="value">${form.headbandColor || ''}</span>
                            </div>
                        </div>

                        <!-- 口罩印字信息 -->
                        <div class="subsection">
                            <h4 class="subsection-title">口罩印字</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">印字颜色：</span>
                                    <span class="value">${form.printColor || ''}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">印字尺寸：</span>
                                    <span class="value">${form.printSize || ''}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 气阀印字信息 -->
                        ${processedData.needValvePrint ? `
                        <div class="subsection">
                            <h4 class="subsection-title">气阀印字</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">印字颜色：</span>
                                    <span class="value">${form.printMaterialColor || ''}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">印字尺寸：</span>
                                    <span class="value">${form.printMaterialSize || ''}</span>
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <!-- 包装信息 -->
                    <div class="template-section">
                        <h3 class="section-title">包装信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">包装方式：</span>
                                <span class="value">${form.packagingMethod || ''}</span>
                            </div>
                        </div>

                        <div class="subsection">
                            <h4 class="subsection-title">包装材质&尺寸</h4>
                            <div class="packaging-options">
                                <div class="packaging-option">
                                    <span class="option-label">彩盒：</span>
                                    <span class="option-value">${processedData.colorBox}</span>
                                </div>
                                <div class="packaging-option">
                                    <span class="option-label">胶带：</span>
                                    <span class="option-value">${processedData.tape}</span>
                                    ${form.tape && form.tapeMaterial ? `<div class="option-detail">材质尺寸：${form.tapeMaterial}</div>` : ''}
                                </div>
                                <div class="packaging-option">
                                    <span class="option-label">泡壳：</span>
                                    <span class="option-value">${processedData.blisterPack}</span>
                                </div>
                                <div class="packaging-option">
                                    <span class="option-label">其他：</span>
                                    <span class="option-value">${processedData.other}</span>
                                    ${form.other && form.otherDescription ? `<div class="option-detail">描述：${form.otherDescription}</div>` : ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试信息 -->
                    ${processedData.needInternalTest ? `
                    <div class="template-section">
                        <h3 class="section-title">测试项目及数量</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">是否内测：</span>
                                <span class="value">是</span>
                            </div>
                        </div>

                        ${form.testItems && form.testItems.length > 0 ? `
                        <div class="subsection">
                            <h4 class="subsection-title">选择的测试项目</h4>
                            <div class="test-items-list">
                                ${form.testItems.map(item => `
                                    <div class="test-item">
                                        <span class="test-name">${item}</span>
                                        ${form.testLevels && form.testLevels[item] ? `<span class="test-level">(${form.testLevels[item]})</span>` : ''}
                                        ${form.testSampleCounts && form.testSampleCounts[item] ? `<span class="test-count">${form.testSampleCounts[item]}个</span>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${form.testNotes ? `
                        <div class="subsection">
                            <h4 class="subsection-title">测试备注</h4>
                            <div class="test-notes">${form.testNotes}</div>
                        </div>
                        ` : ''}
                    </div>
                    ` : `
                    <div class="template-section">
                        <h3 class="section-title">测试信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">是否内测：</span>
                                <span class="value">否</span>
                            </div>
                        </div>
                    </div>
                    `}

                    <!-- 附件图档显示 -->
                    <div class="template-section">
                        <h3 class="section-title">附件图档</h3>
                        <div class="attachments-display" id="attachmentsDisplayArea">
                            <!-- 图片将通过Vue动态渲染 -->
                        </div>
                    </div>

                    <!-- 邮件通知设置 -->
                    ${(processedData.recipients || processedData.ccRecipients) ? `
                    <div class="template-section">
                        <h3 class="section-title">邮件通知设置</h3>
                        <div class="info-grid">
                            ${processedData.recipients ? `
                            <div class="info-item">
                                <span class="label">收件人：</span>
                                <span class="value">${processedData.recipients}</span>
                            </div>
                            ` : ''}
                            ${processedData.ccRecipients ? `
                            <div class="info-item">
                                <span class="label">抄送人：</span>
                                <span class="value">${processedData.ccRecipients}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    ` : ''}

                    <!-- 补充说明 -->
                    ${form.additionalNotes ? `
                    <div class="template-section">
                        <h3 class="section-title">补充说明</h3>
                        <div class="multi-line-content">${form.additionalNotes}</div>
                    </div>
                    ` : ''}
                </div>
            `;
        });

        // 获取附件类型标签
        function getAttachmentTypeLabel(fileType) {
            const typeLabels = {
                'maskPrintFiles': '口罩印字图档',
                'valvePrintFiles': '气阀印字图档',
                'colorBoxFiles': '彩盒图档',
                'blisterPackFiles': '吸塑包装图档'
            };
            return typeLabels[fileType] || '';
        }

        // 动态渲染附件图片
        function renderAttachmentImages() {
            const container = document.getElementById('attachmentsDisplayArea');
            if (!container || !props.sampleForm.attachments) return;

            console.log('开始渲染附件图片...');

            const imageAttachments = props.sampleForm.attachments.filter(attachment =>
                attachment.mime_type && attachment.mime_type.startsWith('image/')
            );

            container.innerHTML = imageAttachments.map(attachment => {
                const typeLabel = getAttachmentTypeLabel(attachment.file_type);
                const imageUrl = attachmentImages.value[attachment.id];

                return `
                    <div class="attachment-image-item" style="margin-bottom: 20px; border: 2px solid red; padding: 10px;">
                        <div class="attachment-image-header" style="margin-bottom: 10px;">
                            <span class="attachment-type-label" style="font-weight: bold; color: blue;">${typeLabel || '图档'}</span>
                            <span class="attachment-filename" style="margin-left: 10px;">${attachment.original_filename}</span>
                        </div>
                        <div class="attachment-image-container" style="border: 2px solid green; background-color: lightgreen; min-height: 200px; padding: 10px;">
                            ${imageUrl ? `
                                <img src="${imageUrl}"
                                     alt="${attachment.original_filename}"
                                     style="max-width: 100%; max-height: 280px; object-fit: contain; display: block; border: 2px solid orange;"
                                     crossorigin="anonymous" />
                            ` : `
                                <div style="padding: 50px; text-align: center; color: #666; background-color: yellow;">
                                    图片加载中... (ID: ${attachment.id})
                                </div>
                            `}
                        </div>
                    </div>
                `;
            }).join('');

            console.log('附件图片渲染完成，容器内容:', container.innerHTML);
        }

        // 监听模板显示状态和图片数据变化
        const { watch } = Vue;

        watch(() => props.visible, async (newVal) => {
            if (newVal) {
                console.log('样品单模板显示，开始加载图片');
                await loadAttachmentImages();
                renderAttachmentImages();
            }
        });

        watch(() => attachmentImages.value, () => {
            console.log('图片数据变化，重新渲染');
            renderAttachmentImages();
        }, { deep: true });

        return {
            templateHTML,
            attachmentImages,
            isLoadingImages,
            closeSampleTemplate,
            downloadSampleFormPDF,
            renderAttachmentImages,
            formatDate,
            formatBoolean,
            formatArray,
            getAttachmentTypeLabel
        };
    },
    template: `
        <!-- 样品单模板遮罩 -->
        <div v-if="visible" 
             class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
             @click.self="closeSampleTemplate">
            
            <!-- 样品单模板内容 -->
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- 模板头部 -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <h2 class="text-xl font-semibold text-gray-900">样品单模板预览</h2>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                            {{ sampleForm.sampleNumber }}
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button @click="downloadSampleFormPDF" 
                                class="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1.5">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span>下载PDF</span>
                        </button>
                        <button @click="closeSampleTemplate" 
                                class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 模板内容区域 -->
                <div class="overflow-auto max-h-[calc(90vh-80px)]">
                    <div class="a4-page" id="sampleFormTemplatePage">
                        <div id="sampleFormTemplateContent" v-html="templateHTML"></div>
                    </div>
                </div>
            </div>
        </div>
    `
};
