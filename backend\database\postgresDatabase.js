/**
 * PostgreSQL数据库连接和初始化
 * 替换SQLite，使用pg驱动连接PostgreSQL
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class PostgreSQLManager {
    constructor() {
        this.pool = null;
        this.migrationCompleted = false;
        this.init();
    }

    /**
     * 初始化PostgreSQL连接池
     */
    init() {
        try {
            // PostgreSQL连接配置
            const config = {
                user: process.env.POSTGRES_USER || 'postgres',
                host: process.env.POSTGRES_HOST || 'localhost',
                database: process.env.POSTGRES_DATABASE || 'makrite_system',
                password: process.env.POSTGRES_PASSWORD || 'postgres',
                port: parseInt(process.env.POSTGRES_PORT) || 5432,
                max: parseInt(process.env.POSTGRES_POOL_MAX) || 20, // 连接池最大连接数
                min: parseInt(process.env.POSTGRES_POOL_MIN) || 2, // 连接池最小连接数
                idleTimeoutMillis: parseInt(process.env.POSTGRES_POOL_IDLE_TIMEOUT) || 30000, // 空闲连接超时时间
                connectionTimeoutMillis: parseInt(process.env.POSTGRES_POOL_CONNECTION_TIMEOUT) || 2000, // 连接超时时间
            };

            // 创建连接池
            this.pool = new Pool(config);

            // 监听连接池事件
            this.pool.on('connect', (client) => {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.debug('PostgreSQL客户端连接成功');
                }
            });

            this.pool.on('error', (err, client) => {
                logger.error('PostgreSQL连接池错误:', err);
            });

            // 测试连接
            this.testConnection();

            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('PostgreSQL数据库连接池初始化成功');
            }

        } catch (error) {
            logger.error('PostgreSQL数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const client = await this.pool.connect();
            const result = await client.query('SELECT NOW()');
            client.release();
            
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('PostgreSQL连接测试成功:', result.rows[0]);
            }
        } catch (error) {
            logger.error('PostgreSQL连接测试失败:', error);
            throw error;
        }
    }

    /**
     * 执行查询
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     * @returns {Promise} 查询结果
     */
    async query(text, params = []) {
        try {
            const start = Date.now();
            const result = await this.pool.query(text, params);
            const duration = Date.now() - start;
            
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.debug('执行查询:', { text, duration, rows: result.rowCount });
            }
            
            return result;
        } catch (error) {
            logger.error('查询执行失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 执行事务
     * @param {Function} callback 事务回调函数
     * @returns {Promise} 事务结果
     */
    async transaction(callback) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            logger.error('事务执行失败:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 获取连接池
     */
    getPool() {
        if (!this.pool) {
            throw new Error('PostgreSQL连接池未初始化');
        }
        return this.pool;
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            logger.info('PostgreSQL连接池已关闭');
        }
    }

    /**
     * 获取连接池状态
     */
    getPoolStatus() {
        if (!this.pool) {
            return null;
        }
        
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount
        };
    }

    /**
     * 创建数据库表结构
     */
    async createTables() {
        try {
            // 用户表
            await this.query(`
                CREATE TABLE IF NOT EXISTS users (
                    id VARCHAR(50) PRIMARY KEY,
                    usercode VARCHAR(50) UNIQUE NOT NULL,
                    username VARCHAR(100) NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    role VARCHAR(50) NOT NULL,
                    department VARCHAR(100),
                    email VARCHAR(255),
                    active BOOLEAN DEFAULT true,
                    permissions JSONB DEFAULT '[]',
                    has_signature BOOLEAN DEFAULT false,
                    signature_path VARCHAR(500),
                    signature_base64 TEXT,
                    last_login_at TIMESTAMP,
                    last_active_at TIMESTAMP,
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
                )
            `);

            // 申请表
            await this.query(`
                CREATE TABLE IF NOT EXISTS applications (
                    id VARCHAR(50) PRIMARY KEY,
                    application_number VARCHAR(100) UNIQUE NOT NULL,
                    user_id VARCHAR(50) NOT NULL,
                    applicant VARCHAR(100) NOT NULL,
                    department VARCHAR(100),
                    date DATE NOT NULL,
                    content TEXT NOT NULL,
                    amount VARCHAR(100),
                    priority VARCHAR(20) DEFAULT 'normal',
                    type VARCHAR(50) DEFAULT 'standard',
                    status VARCHAR(20) DEFAULT 'pending',
                    current_stage VARCHAR(50),
                    need_manager_approval BOOLEAN DEFAULT false,
                    need_ceo_approval BOOLEAN DEFAULT true,
                    selected_factory_managers JSONB DEFAULT '[]',
                    selected_managers JSONB DEFAULT '[]',
                    pdf_path VARCHAR(500),
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // 审批记录表
            await this.query(`
                CREATE TABLE IF NOT EXISTS application_approvals (
                    id VARCHAR(50) PRIMARY KEY,
                    application_id VARCHAR(50) NOT NULL,
                    approver_id VARCHAR(50) NOT NULL,
                    approver_name VARCHAR(100) NOT NULL,
                    stage VARCHAR(50) NOT NULL,
                    action VARCHAR(20) NOT NULL,
                    comments TEXT,
                    approved_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    FOREIGN KEY (application_id) REFERENCES applications (id),
                    FOREIGN KEY (approver_id) REFERENCES users (id)
                )
            `);

            logger.info('PostgreSQL基础表结构创建完成');
        } catch (error) {
            logger.error('创建表结构失败:', error);
            throw error;
        }
    }
}

// 创建全局实例
const postgresManager = new PostgreSQLManager();

module.exports = postgresManager;
