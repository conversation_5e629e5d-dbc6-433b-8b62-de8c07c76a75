/**
 * 文件列表组件
 * 显示文件管理列表
 */

import { getFileRecords, getFileById, deleteFileRecord, downloadFile, fileManagementUtils } from '../../scripts/api/file-management.js';
import FileDetailModal from './FileDetailModal.js';
import SampleFormTemplate from './SampleFormTemplate.js';
import eventManager, { EVENTS } from '../../scripts/utils/eventManager.js';

const { createApp, ref, reactive, computed, onMounted, onUnmounted } = Vue;

export default {
    name: 'FileList',
    components: {
        FileDetailModal,
        SampleFormTemplate
    },
    setup() {
        // 响应式数据
        const files = ref([]);
        const loading = ref(false);
        const searchQuery = ref('');
        const selectedCustomer = ref('');
        const selectedStatus = ref('');
        const selectedType = ref(''); // 表单类型筛选
        const currentPage = ref(1);
        const itemsPerPage = ref(10);
        const expandedItems = ref(new Set()); // 展开的项目ID集合

        // 详情模态框相关
        const showDetailModal = ref(false);
        const selectedFile = ref(null);

        // 计算属性
        const filteredFiles = computed(() => {
            let result = files.value;

            // 搜索过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                result = result.filter(file =>
                    (file.title && file.title.toLowerCase().includes(query)) ||
                    (file.file_number && file.file_number.toLowerCase().includes(query)) ||
                    (file.sampleNumber && file.sampleNumber.toLowerCase().includes(query)) ||
                    (file.customer_name && file.customer_name.toLowerCase().includes(query)) ||
                    (file.companyName && file.companyName.toLowerCase().includes(query)) ||
                    (file.product_model && file.product_model.toLowerCase().includes(query)) ||
                    (file.productModel && file.productModel.toLowerCase().includes(query))
                );
            }

            // 客户过滤
            if (selectedCustomer.value) {
                result = result.filter(file =>
                    file.customer_name === selectedCustomer.value ||
                    file.companyName === selectedCustomer.value
                );
            }

            // 表单类型过滤
            if (selectedType.value) {
                if (selectedType.value === 'sample_form') {
                    result = result.filter(file => file.type === 'sample_form');
                } else if (selectedType.value === 'file_record') {
                    result = result.filter(file => file.type !== 'sample_form');
                }
            }

            return result;
        });

        const paginatedFiles = computed(() => {
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filteredFiles.value.slice(start, end);
        });

        const totalPages = computed(() => {
            return Math.ceil(filteredFiles.value.length / itemsPerPage.value);
        });

        const customers = computed(() => {
            const customerSet = new Set(files.value.map(file => file.customer_name));
            return Array.from(customerSet).sort();
        });

        // 生命周期
        // 事件处理函数引用，用于清理
        const sampleFormSubmittedHandler = (eventData) => {
            console.log('收到样品单提交事件，刷新文件列表', eventData);
            // 延迟刷新，确保后端处理完成
            setTimeout(() => {
                loadFiles();
            }, 1000);
        };

        const fileListRefreshHandler = (eventData) => {
            console.log('收到文件列表刷新事件', eventData);
            loadFiles();
        };

        const fileUploadedHandler = (eventData) => {
            console.log('收到文件上传事件，刷新文件列表', eventData);
            loadFiles();
        };

        const fileDeletedHandler = (eventData) => {
            console.log('收到文件删除事件', eventData);
            // 删除事件触发时，重新加载文件列表以确保数据同步
            loadFiles();
        };

        onMounted(() => {
            loadFiles();

            // 检查URL参数，如果有刷新标志则显示提示
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('refresh') === 'true') {
                const action = urlParams.get('action');
                if (action === 'submit') {
                    console.log('检测到样品单提交，文件列表已自动刷新');
                } else if (action === 'delete') {
                    console.log('检测到文件删除，文件列表已自动刷新');
                }

                // 清除URL参数，避免刷新页面时重复提示
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }

            // 监听样品单提交事件，自动刷新列表
            eventManager.on(EVENTS.SAMPLE_FORM_SUBMITTED, sampleFormSubmittedHandler);

            // 监听文件列表刷新事件
            eventManager.on(EVENTS.FILE_LIST_REFRESH, fileListRefreshHandler);

            // 监听文件上传事件
            eventManager.on(EVENTS.FILE_UPLOADED, fileUploadedHandler);

            // 监听文件删除事件
            eventManager.on(EVENTS.FILE_DELETED, fileDeletedHandler);
        });

        onUnmounted(() => {
            // 清理事件监听器
            eventManager.off(EVENTS.SAMPLE_FORM_SUBMITTED, sampleFormSubmittedHandler);
            eventManager.off(EVENTS.FILE_LIST_REFRESH, fileListRefreshHandler);
            eventManager.off(EVENTS.FILE_UPLOADED, fileUploadedHandler);
            eventManager.off(EVENTS.FILE_DELETED, fileDeletedHandler);
        });

        // 方法
        async function loadFiles() {
            try {
                loading.value = true;
                console.log('开始加载文件列表...');

                // 添加时间戳参数，强制刷新缓存
                const response = await getFileRecords({ _t: Date.now() });
                if (response.success) {
                    files.value = response.data;
                    console.log('文件列表加载成功，记录数:', response.data.length);
                } else {
                    console.error('文件列表加载失败:', response.message);
                    alert('加载文件列表失败: ' + response.message);
                }
            } catch (error) {
                console.error('加载文件列表失败:', error);
                alert('加载文件列表失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        async function viewFileDetail(file) {
            try {
                // 获取完整的文件详情（包括附件列表）
                const response = await getFileById(file.id);

                if (response.success) {
                    selectedFile.value = response.data;
                    showDetailModal.value = true;
                } else {
                    throw new Error(response.message || '获取文件详情失败');
                }
            } catch (error) {
                console.error('获取文件详情失败:', error);
                alert('获取文件详情失败: ' + error.message);
            }
        }

        function closeDetailModal() {
            showDetailModal.value = false;
            selectedFile.value = null;
        }

        function handleDownloadFile(file, attachment) {
            downloadFile(file.id, attachment.id);
        }

        async function deleteFile(file) {
            const fileName = file.title || file.sampleNumber || '未知文件';
            if (!confirm(`确定要删除 "${fileName}" 吗？`)) {
                return;
            }

            try {
                const response = await deleteFileRecord(file.id);
                if (response.success) {
                    alert('删除成功');

                    // 触发删除事件，通知其他可能需要更新的组件
                    eventManager.emit(EVENTS.FILE_DELETED, {
                        fileId: file.id,
                        fileType: file.type || 'sample_form',
                        timestamp: new Date()
                    });

                    // 立即重新加载文件列表，确保数据同步
                    await loadFiles();
                } else {
                    throw new Error(response.message || '删除失败');
                }
            } catch (error) {
                console.error('删除文件失败:', error);
                alert('删除失败: ' + error.message);
                // 删除失败时也重新加载列表，确保数据一致性
                await loadFiles();
            }
        }

        function resetFilters() {
            searchQuery.value = '';
            selectedCustomer.value = '';
            selectedStatus.value = '';
            selectedType.value = '';
            currentPage.value = 1;
        }

        function goToPage(page) {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
            }
        }

        // 切换展开/折叠状态
        function toggleExpanded(fileId) {
            const expanded = expandedItems.value;
            if (expanded.has(fileId)) {
                expanded.delete(fileId);
            } else {
                expanded.add(fileId);
            }
        }

        // 检查是否展开
        function isExpanded(fileId) {
            return expandedItems.value.has(fileId);
        }

        // 状态中文化
        function getStatusText(file) {
            if (file.type === 'sample_form') {
                // 样品单状态
                switch (file.status) {
                    case 'submitted': return '已提交';
                    case 'processing': return '处理中';
                    case 'completed': return '已完成';
                    case 'cancelled': return '已取消';
                    default: return '已提交';
                }
            } else {
                // 文件记录状态
                switch (file.status) {
                    case 'active': return '正常';
                    case 'inactive': return '停用';
                    case 'pending': return '待审核';
                    case 'approved': return '已审核';
                    case 'rejected': return '已拒绝';
                    default: return '正常';
                }
            }
        }

        // 获取附件类型显示名称
        function getAttachmentTypeLabel(attachment) {
            if (!attachment.file_type) return '';

            const typeLabels = {
                'maskPrintFiles': '口罩印字图档',
                'valvePrintFiles': '气阀印字图档',
                'colorBoxFiles': '彩盒图档',
                'blisterPackFiles': '吸塑包装图档'
            };

            return typeLabels[attachment.file_type] || '';
        }

        // 获取文件时间
        function getFileDateTime(file) {
            if (file.type === 'sample_form') {
                // 样品单使用创建时间
                return file.createdAt || file.uploaded_at;
            } else {
                // 文件记录使用上传时间
                return file.uploaded_at || file.createdAt;
            }
        }

        // 新增统计方法
        function getTodayUploads() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return files.value.filter(file => {
                const uploadDate = new Date(file.uploaded_at);
                uploadDate.setHours(0, 0, 0, 0);
                return uploadDate.getTime() === today.getTime();
            }).length;
        }

        function getMonthUploads() {
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();
            return files.value.filter(file => {
                const uploadDate = new Date(file.uploaded_at);
                return uploadDate.getMonth() === currentMonth && uploadDate.getFullYear() === currentYear;
            }).length;
        }

        // 优化分页显示
        function getPageNumbers() {
            const total = totalPages.value;
            const current = currentPage.value;
            const pages = [];

            if (total <= 7) {
                // 如果总页数小于等于7，显示所有页码
                for (let i = 1; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                // 复杂分页逻辑
                if (current <= 4) {
                    // 当前页在前面
                    for (let i = 1; i <= 5; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                } else if (current >= total - 3) {
                    // 当前页在后面
                    pages.push(1);
                    pages.push('...');
                    for (let i = total - 4; i <= total; i++) {
                        pages.push(i);
                    }
                } else {
                    // 当前页在中间
                    pages.push(1);
                    pages.push('...');
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                }
            }

            return pages;
        }

        return {
            files,
            loading,
            searchQuery,
            selectedCustomer,
            selectedStatus,
            currentPage,
            itemsPerPage,
            filteredFiles,
            paginatedFiles,
            totalPages,
            customers,
            showDetailModal,
            selectedFile,
            selectedType,
            expandedItems,
            loadFiles,
            viewFileDetail,
            closeDetailModal,
            handleDownloadFile,
            deleteFile,
            resetFilters,
            goToPage,
            toggleExpanded,
            isExpanded,
            getStatusText,
            getAttachmentTypeLabel,
            getFileDateTime,
            getTodayUploads,
            getMonthUploads,
            getPageNumbers,
            fileManagementUtils
        };
    },
    template: `
        <div class="file-list">
            <!-- 页面头部统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100 text-sm font-medium">总文件数</p>
                            <p class="text-2xl font-bold">{{ files.length }}</p>
                        </div>
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100 text-sm font-medium">客户数量</p>
                            <p class="text-2xl font-bold">{{ customers.length }}</p>
                        </div>
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-100 text-sm font-medium">今日上传</p>
                            <p class="text-2xl font-bold">{{ getTodayUploads() }}</p>
                        </div>
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-orange-100 text-sm font-medium">本月上传</p>
                            <p class="text-2xl font-bold">{{ getMonthUploads() }}</p>
                        </div>
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m0 0V7a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h8z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和过滤区域 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" v-model="searchQuery"
                                   placeholder="搜索文件标题、编号、客户名称或产品型号..."
                                   class="w-full pl-9 pr-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                        <select v-model="selectedCustomer"
                                class="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-sm">
                            <option value="">所有客户</option>
                            <option v-for="customer in customers" :key="customer" :value="customer">
                                {{ customer }}
                            </option>
                        </select>

                        <select v-model="selectedType"
                                class="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-sm">
                            <option value="">所有类型</option>
                            <option value="file_record">文件记录</option>
                            <option value="sample_form">样品单</option>
                        </select>

                        <button @click="resetFilters"
                                class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                            重置筛选
                        </button>

                        <button @click="loadFiles"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center space-x-1.5">
                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span>刷新</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="flex flex-col items-center justify-center py-12">
                <div class="relative">
                    <div class="w-12 h-12 border-3 border-blue-200 rounded-full animate-spin"></div>
                    <div class="w-12 h-12 border-3 border-blue-600 border-t-transparent rounded-full animate-spin absolute top-0 left-0"></div>
                </div>
                <p class="mt-3 text-gray-600 text-sm">正在加载文件列表...</p>
            </div>

            <!-- 文件列表 -->
            <div v-else-if="paginatedFiles.length > 0" class="space-y-4">
                <div v-for="file in paginatedFiles" :key="file.id"
                     class="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 overflow-hidden">

                    <!-- 文件头部信息 -->
                    <div class="p-4 border-b border-gray-50">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <div :class="file.type === 'sample_form' ? 'w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center' : 'w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center'">
                                        <!-- 样品单图标 -->
                                        <svg v-if="file.type === 'sample_form'" class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                        </svg>
                                        <!-- 文件图标 -->
                                        <svg v-else class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">{{ file.title }}</h3>
                                        <p class="text-sm text-gray-500 mt-1">
                                            {{ file.type === 'sample_form' ? '样品单编号' : '文件编号' }}: {{ file.file_number || file.sampleNumber }}
                                        </p>
                                    </div>
                                </div>

                                <div class="flex flex-wrap items-center gap-2 mb-3">
                                    <!-- 类型标签 -->
                                    <span v-if="file.type === 'sample_form'" class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                        样品单
                                    </span>
                                    <span v-else class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                        版本 {{ file.version }}
                                    </span>
                                    <span v-if="file.type !== 'sample_form' && !file.is_first_version" class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                                        变更版本
                                    </span>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                                        {{ getStatusText(file) }}
                                    </span>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex space-x-3 ml-6">
                                <button @click="toggleExpanded(file.id)"
                                        class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm flex items-center space-x-1.5">
                                    <svg class="w-3.5 h-3.5 transition-transform" :class="{ 'rotate-180': isExpanded(file.id) }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                    <span>{{ isExpanded(file.id) ? '收起' : '详情' }}</span>
                                </button>
                                <button @click="viewFileDetail(file)"
                                        class="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center space-x-1.5">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    <span>查看详情</span>
                                </button>
                                <button @click="deleteFile(file)"
                                        class="px-3 py-1.5 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm flex items-center space-x-1.5">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    <span>删除</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文件详细信息（可展开） -->
                    <transition name="slide-down">
                        <div v-if="isExpanded(file.id)" class="p-4 border-t border-gray-100 bg-gray-50">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <div class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">{{ file.type === 'sample_form' ? '公司名称' : '客户名称' }}</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ file.customer_name || file.companyName }}</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">产品型号</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ file.product_model || file.productModel }}</p>
                                </div>
                            </div>

                            <!-- 样品单特有信息 -->
                            <div v-if="file.type === 'sample_form'" class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">收件人</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ file.recipient }}</p>
                                </div>
                            </div>

                            <div v-if="file.type === 'sample_form'" class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">数量</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ file.quantity }} 个</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">{{ file.type === 'sample_form' ? '提交时间' : '上传时间' }}</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ fileManagementUtils.formatDateTime(getFileDateTime(file)) }}</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-2.5">
                                <div class="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">附件数量</p>
                                    <p class="text-sm font-semibold text-gray-900">{{ file.attachments ? file.attachments.length : 0 }} 个文件</p>
                                </div>
                            </div>
                        </div>

                        <!-- 描述信息 -->
                        <div v-if="file.description || file.purpose" class="mb-3 p-3 bg-gray-50 rounded-lg">
                            <p class="text-xs text-gray-500 mb-1">{{ file.type === 'sample_form' ? '邮寄目的' : '文件描述' }}</p>
                            <p class="text-sm text-gray-900">{{ file.description || file.purpose }}</p>
                        </div>

                        <!-- 变更说明 -->
                        <div v-if="!file.is_first_version && file.change_description" class="mb-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                            <p class="text-xs text-orange-600 mb-1 font-medium">变更说明</p>
                            <p class="text-sm text-orange-800">{{ file.change_description }}</p>
                        </div>

                        <!-- 附件列表 -->
                        <div v-if="file.attachments && file.attachments.length > 0" class="space-y-2">
                            <p class="text-xs font-medium text-gray-700 flex items-center space-x-1.5">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                <span>附件文件 ({{ file.attachments.length }})</span>
                            </p>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                <button v-for="attachment in file.attachments" :key="attachment.id"
                                        @click="handleDownloadFile(file, attachment)"
                                        class="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left">
                                    <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <span class="text-blue-600 text-xs font-bold">{{ fileManagementUtils.getFileTypeIcon(attachment.original_filename) }}</span>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <p class="text-xs font-medium text-gray-900 truncate">{{ attachment.original_filename }}</p>
                                            <span v-if="getAttachmentTypeLabel(attachment)" class="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full whitespace-nowrap">
                                                {{ getAttachmentTypeLabel(attachment) }}
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500">{{ fileManagementUtils.formatFileSize(attachment.file_size) }}</p>
                                    </div>
                                    <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        </div>
                    </transition>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无文件记录</h3>
                <p class="text-gray-500 mb-4">还没有上传任何文件，开始上传您的第一个文件吧</p>
                <a href="/file-upload" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    上传文件
                </a>
            </div>

            <!-- 分页 -->
            <div v-if="totalPages > 1" class="flex flex-col sm:flex-row items-center justify-between mt-6 space-y-3 sm:space-y-0">
                <div class="text-sm text-gray-600">
                    显示第 <span class="font-semibold">{{ (currentPage - 1) * itemsPerPage + 1 }}</span> -
                    <span class="font-semibold">{{ Math.min(currentPage * itemsPerPage, filteredFiles.length) }}</span> 条，
                    共 <span class="font-semibold">{{ filteredFiles.length }}</span> 条记录
                </div>

                <div class="flex items-center space-x-2">
                    <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1"
                            class="px-3 py-1.5 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                        上一页
                    </button>

                    <div class="flex space-x-1">
                        <button v-for="page in getPageNumbers()" :key="page"
                                @click="goToPage(page)"
                                :class="[
                                    'px-2.5 py-1.5 rounded-lg text-sm transition-colors',
                                    page === currentPage
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-700 hover:bg-gray-100'
                                ]">
                            {{ page }}
                        </button>
                    </div>

                    <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="px-3 py-1.5 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                        下一页
                    </button>
                </div>
            </div>

            <!-- 文件详情模态框 -->
            <file-detail-modal
                :show="showDetailModal"
                :file="selectedFile"
                @close="closeDetailModal">
            </file-detail-modal>
        </div>

        <style scoped>
        .slide-down-enter-active, .slide-down-leave-active {
            transition: all 0.3s ease;
            max-height: 1000px;
            overflow: hidden;
        }
        .slide-down-enter-from, .slide-down-leave-to {
            max-height: 0;
            opacity: 0;
        }
        </style>
    `
};
