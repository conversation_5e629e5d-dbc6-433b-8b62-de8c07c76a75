<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户管理 - 系统管理 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            系统管理 - 客户管理
                        </span>
                    </li>
                </ol>
            </nav>

            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <!-- 页面标题和操作按钮 -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-800">客户管理</h2>
                        <p class="text-sm text-gray-600 mt-1">管理文件管理系统中的客户信息</p>
                    </div>
                    <div class="mt-4 sm:mt-0">
                        <button @click="showAddModal = true" 
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            添加客户
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="mb-6">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input v-model="searchQuery" 
                                   type="text" 
                                   placeholder="搜索客户名称、联系人或邮箱..."
                                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex gap-2">
                            <select v-model="statusFilter" 
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">停用</option>
                            </select>
                            <button @click="loadCustomers" 
                                    class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 客户列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户信息</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- 加载状态 -->
                            <tr v-if="loading">
                                <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                    <div class="flex items-center justify-center">
                                        <div class="loading-spinner mr-2"></div>
                                        加载中...
                                    </div>
                                </td>
                            </tr>

                            <!-- 空状态 -->
                            <tr v-else-if="paginatedCustomers.length === 0">
                                <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                    <div class="text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无客户</h3>
                                        <p class="mt-1 text-sm text-gray-500">开始添加第一个客户吧</p>
                                        <div class="mt-6">
                                            <button @click="showAddModal = true"
                                                    class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                添加客户
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>

                            <!-- 客户列表 -->
                            <tr v-else v-for="customer in paginatedCustomers" :key="customer.id" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ customer.customer_name }}</div>
                                        <div v-if="customer.description" class="text-sm text-gray-500">{{ customer.description }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.contact_person || '-' }}</div>
                                    <div class="text-sm text-gray-500">{{ customer.contact_email || '-' }}</div>
                                    <div class="text-sm text-gray-500">{{ customer.contact_phone || '-' }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="customer.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ customer.active ? '活跃' : '停用' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formatDate(customer.created_at) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button @click="editCustomer(customer)"
                                                class="text-blue-600 hover:text-blue-900">编辑</button>
                                        <button @click="toggleCustomerStatus(customer)"
                                                :class="customer.active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'">
                                            {{ customer.active ? '停用' : '启用' }}
                                        </button>
                                        <button @click="deleteCustomer(customer)"
                                                class="text-red-600 hover:text-red-900">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div v-if="totalPages > 1" class="mt-6 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 {{ (currentPage - 1) * itemsPerPage + 1 }} 到 {{ Math.min(currentPage * itemsPerPage, filteredCustomers.length) }} 条，
                        共 {{ filteredCustomers.length }} 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button @click="currentPage = Math.max(1, currentPage - 1)" 
                                :disabled="currentPage === 1"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                            上一页
                        </button>
                        <span class="px-3 py-1 text-sm text-gray-700">
                            第 {{ currentPage }} / {{ totalPages }} 页
                        </span>
                        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                                :disabled="currentPage === totalPages"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加/编辑客户模态框 -->
        <customer-form-modal
            :show="showAddModal || showEditModal"
            :customer="editingCustomer"
            :is-editing="showEditModal"
            @close="closeModal"
            @save="handleSaveCustomer">
        </customer-form-modal>
    </div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/system-management/customer-management.js"></script>
</body>
</html>
