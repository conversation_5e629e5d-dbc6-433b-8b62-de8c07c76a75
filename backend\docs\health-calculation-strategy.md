# 设备健康度计算策略

## 📋 概述

本文档定义了设备健康度计算的触发时机和策略，确保系统性能和数据准确性的平衡。

## 🎯 设计原则

### 1. 按需计算原则
- **不在系统启动时自动计算** - 避免启动时间过长
- **不在设备列表加载时自动计算** - 避免影响页面加载性能
- **只在真正需要时计算** - 确保计算的必要性

### 2. 数据一致性原则
- **优先使用已有记录** - 从数据库获取最新的健康度记录
- **提供估算值作为备用** - 当没有记录时使用基于设备状态的估算
- **保持历史记录** - 每次计算都保存到历史表

### 3. 用户体验原则
- **手动触发为主** - 用户主动点击计算按钮
- **自动触发为辅** - 仅在数据变更时自动触发
- **异步处理** - 避免阻塞用户界面

## 🔄 健康度计算触发时机

### ✅ 应该触发计算的情况

#### 1. 用户手动触发
- **设备详情页面** - 用户点击"重新计算健康度"按钮
- **设备列表页面** - 用户点击单个设备的"计算健康度"按钮
- **批量操作** - 用户选择多个设备进行批量计算

#### 2. 数据变更自动触发
- **设备信息更新** - 影响健康度的关键字段变更时
  - `manufacture_date` - 制造日期
  - `status` - 设备状态
  - `last_maintenance_date` - 最后维护日期
  - `next_maintenance_date` - 下次维护日期
- **维修记录创建** - 新增维修保养记录时
- **维修记录更新** - 修改现有维修记录时

#### 3. 定期任务触发（可选）
- **每日定时任务** - 凌晨自动计算所有设备（可配置）
- **每周汇总** - 生成健康度趋势报告

### ❌ 不应该触发计算的情况

#### 1. 系统启动时
- **服务器重启** - 不自动计算所有设备健康度
- **应用部署** - 不在部署过程中计算

#### 2. 数据查询时
- **设备列表加载** - 只显示已有的健康度记录或估算值
- **健康度统计** - 基于现有数据计算统计信息
- **报表生成** - 使用已有的健康度数据

#### 3. 非关键字段更新
- **设备名称** - 不影响健康度计算
- **设备描述** - 不影响健康度计算
- **设备位置** - 不影响健康度计算（除非影响维护便利性）

## 📊 数据获取策略

### 1. 设备列表显示
```javascript
// 优先级顺序
1. 数据库中的最新健康度记录
2. 基于设备状态的估算值（如果没有记录）
3. 默认值（75分）
```

### 2. 设备详情显示
```javascript
// 优先级顺序
1. 数据库中的最新健康度记录（包含完整计算详情）
2. 实时计算（如果用户明确要求）
3. 基于设备状态的估算值
```

### 3. 健康度统计
```javascript
// 基于现有数据
1. 统计所有设备的最新健康度记录
2. 对于没有记录的设备，使用估算值
3. 生成分布图和趋势图
```

## 🔧 实现细节

### 1. 估算值计算算法
```javascript
calculateFixedHealthScore(equipment) {
    let healthScore = 75; // 基础分数
    
    // 根据设备状态调整
    switch (equipment.status) {
        case 'active': healthScore = 80; break;
        case 'maintenance': healthScore = 60; break;
        case 'fault': healthScore = 30; break;
        case 'inactive': healthScore = 50; break;
    }
    
    // 根据设备年龄调整
    const age = calculateAge(equipment.manufacture_date);
    if (age > 10) healthScore -= 15;
    else if (age > 5) healthScore -= 10;
    else if (age > 3) healthScore -= 5;
    
    // 基于设备ID的固定微调（确保一致性）
    const adjustment = (hashFromId(equipment.id) % 11) - 5;
    
    return Math.max(30, Math.min(100, healthScore + adjustment));
}
```

### 2. 自动触发条件检查
```javascript
// 设备信息更新时的检查
const healthRelevantFields = [
    'manufacture_date', 
    'status', 
    'area', 
    'last_maintenance_date', 
    'next_maintenance_date'
];

const hasHealthRelevantChanges = healthRelevantFields.some(
    field => updateData.hasOwnProperty(field)
);
```

### 3. 异步计算处理
```javascript
// 避免阻塞主流程
if (hasHealthRelevantChanges) {
    // 异步执行，不等待结果
    setImmediate(async () => {
        try {
            await healthService.calculateEquipmentHealth(id, 'system_auto');
        } catch (error) {
            logger.error('自动健康度计算失败', { equipmentId: id, error });
        }
    });
}
```

## 📈 性能优化

### 1. 缓存策略
- **内存缓存** - 缓存最近计算的健康度结果（5分钟）
- **数据库索引** - 在关键查询字段上建立索引
- **批量处理** - 支持批量计算多个设备

### 2. 并发控制
- **计算队列** - 避免同时计算过多设备
- **重复计算检测** - 避免短时间内重复计算同一设备
- **超时控制** - 设置计算超时时间

### 3. 资源管理
- **连接池** - 合理管理数据库连接
- **内存监控** - 监控计算过程中的内存使用
- **错误恢复** - 计算失败时的优雅降级

## 🔍 监控和日志

### 1. 关键指标
- **计算频率** - 每小时/每天的计算次数
- **计算耗时** - 单次计算的平均耗时
- **成功率** - 计算成功的比例
- **错误类型** - 计算失败的原因分析

### 2. 日志记录
- **计算开始** - 记录触发原因和参数
- **计算完成** - 记录结果和耗时
- **计算失败** - 记录错误信息和堆栈

### 3. 告警机制
- **计算失败率过高** - 超过10%时告警
- **计算耗时过长** - 超过30秒时告警
- **系统资源不足** - 内存或CPU使用率过高时告警

## 📝 配置参数

```javascript
// 健康度计算配置
const HEALTH_CALCULATION_CONFIG = {
    // 是否启用自动计算
    AUTO_CALCULATION_ENABLED: true,
    
    // 计算超时时间（秒）
    CALCULATION_TIMEOUT: 30,
    
    // 缓存有效期（分钟）
    CACHE_DURATION: 5,
    
    // 最大并发计算数
    MAX_CONCURRENT_CALCULATIONS: 5,
    
    // 重复计算间隔（分钟）
    MIN_CALCULATION_INTERVAL: 10
};
```

## 🎯 最佳实践

1. **定期审查** - 每月审查健康度计算策略的有效性
2. **性能监控** - 持续监控计算性能和系统资源使用
3. **用户反馈** - 收集用户对健康度准确性的反馈
4. **算法优化** - 根据实际使用情况优化计算算法
5. **文档更新** - 及时更新策略文档和实现细节
