/**
 * 部门控制器
 * 处理部门相关的HTTP请求
 */

const departmentService = require('../services/departmentService');
const logger = require('../utils/logger');

/**
 * 获取所有部门
 */
async function getAllDepartments(req, res) {
    try {
        const departments = await departmentService.getAllDepartments();

        res.json({
            success: true,
            departments
        });
    } catch (error) {
        logger.error('获取部门列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取部门列表失败: ' + error.message
        });
    }
}

/**
 * 根据ID获取部门
 */
async function getDepartmentById(req, res) {
    try {
        const { id } = req.params;
        const department = await departmentService.getDepartmentById(id);

        res.json({
            success: true,
            department
        });
    } catch (error) {
        logger.error('获取部门失败:', error);
        const statusCode = error.message === '部门不存在' ? 404 : 500;
        res.status(statusCode).json({
            success: false,
            message: error.message
        });
    }
}

/**
 * 创建新部门
 */
async function createDepartment(req, res) {
    try {
        // 检查权限 - 只有admin角色才能创建部门
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限创建部门，只有系统管理员才能创建部门'
            });
        }

        const { name, description } = req.body;

        // 验证必填字段
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: '部门名称不能为空'
            });
        }

        const newDepartment = await departmentService.createDepartment({
            name: name.trim(),
            description: description?.trim() || ''
        });

        // 记录业务事件
        logger.logBusinessEvent('部门管理', '创建部门', {
            departmentId: newDepartment.id,
            departmentName: newDepartment.name
        }, req.user);

        res.status(201).json({
            success: true,
            message: '部门创建成功',
            department: newDepartment
        });
    } catch (error) {
        logger.error('创建部门失败:', error);
        const statusCode = error.message.includes('已存在') ? 400 : 500;
        res.status(statusCode).json({
            success: false,
            message: error.message
        });
    }
}

/**
 * 更新部门
 */
async function updateDepartment(req, res) {
    try {
        // 检查权限 - 只有admin角色才能更新部门
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限更新部门，只有系统管理员才能更新部门'
            });
        }

        const { id } = req.params;
        const { name, description, active } = req.body;

        // 验证必填字段
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: '部门名称不能为空'
            });
        }

        const updatedDepartment = await departmentService.updateDepartment(id, {
            name: name.trim(),
            description: description?.trim() || '',
            active: active !== false
        });

        // 记录业务事件
        logger.logBusinessEvent('部门管理', '更新部门', {
            departmentId: updatedDepartment.id,
            departmentName: updatedDepartment.name
        }, req.user);

        res.json({
            success: true,
            message: '部门更新成功',
            department: updatedDepartment
        });
    } catch (error) {
        logger.error('更新部门失败:', error);
        let statusCode = 500;
        if (error.message === '部门不存在') {
            statusCode = 404;
        } else if (error.message.includes('已存在')) {
            statusCode = 400;
        }
        
        res.status(statusCode).json({
            success: false,
            message: error.message
        });
    }
}

/**
 * 删除部门
 */
async function deleteDepartment(req, res) {
    try {
        // 检查权限 - 只有admin角色才能删除部门
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限删除部门，只有系统管理员才能删除部门'
            });
        }

        const { id } = req.params;

        // 获取部门信息用于日志记录
        const department = await departmentService.getDepartmentById(id);

        const result = await departmentService.deleteDepartment(id);

        if (result) {
            // 记录业务事件
            logger.logBusinessEvent('部门管理', '删除部门', {
                departmentId: id,
                departmentName: department.name
            }, req.user);

            res.json({
                success: true,
                message: '部门删除成功'
            });
        } else {
            res.status(500).json({
                success: false,
                message: '删除部门失败'
            });
        }
    } catch (error) {
        logger.error('删除部门失败:', error);
        const statusCode = error.message === '部门不存在' ? 404 : 500;
        res.status(statusCode).json({
            success: false,
            message: error.message
        });
    }
}

/**
 * 检查部门名称是否存在
 */
async function checkDepartmentName(req, res) {
    try {
        const { name } = req.query;
        const { excludeId } = req.query;

        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: '部门名称不能为空'
            });
        }

        const exists = await departmentService.isDepartmentNameExists(name.trim(), excludeId);

        res.json({
            success: true,
            exists
        });
    } catch (error) {
        logger.error('检查部门名称失败:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
}



/**
 * 检查部门是否有用户关联
 */
async function checkDepartmentUsers(req, res) {
    try {
        const { id } = req.params;

        // 获取部门信息
        const department = await departmentService.getDepartmentById(id);
        if (!department) {
            return res.status(404).json({
                success: false,
                message: '部门不存在'
            });
        }

        // 检查是否有用户关联
        const hasUsers = await departmentService.checkDepartmentHasUsers(department.name);

        res.json({
            success: true,
            hasUsers,
            departmentName: department.name
        });
    } catch (error) {
        logger.error('检查部门用户关联失败:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
}

module.exports = {
    getAllDepartments,
    getDepartmentById,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    checkDepartmentName,
    checkDepartmentUsers
};
