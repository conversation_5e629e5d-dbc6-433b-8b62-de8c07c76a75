/**
 * PostgreSQL设备数据访问层
 * 替换SQLite的equipmentRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresEquipmentRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL设备数据访问层初始化完成');
        }
    }

    /**
     * 获取所有设备
     */
    async findAll() {
        try {
            const equipment = await this.findMany(`
                SELECT * FROM equipment 
                ORDER BY created_at DESC
            `);
            return equipment.map(eq => this.transformEquipment(eq));
        } catch (error) {
            logger.error('获取所有设备失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找设备
     */
    async findById(id) {
        try {
            const equipment = await this.findOne('SELECT * FROM equipment WHERE id = $1', [id]);
            return equipment ? this.transformEquipment(equipment) : null;
        } catch (error) {
            logger.error(`根据ID查找设备失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据设备代码查找设备
     */
    async findByCode(code) {
        try {
            const equipment = await this.findOne('SELECT * FROM equipment WHERE code = $1', [code]);
            return equipment ? this.transformEquipment(equipment) : null;
        } catch (error) {
            logger.error(`根据代码查找设备失败 (${code}):`, error);
            throw error;
        }
    }

    /**
     * 根据厂区获取设备
     */
    async findByArea(area) {
        try {
            const equipment = await this.findMany('SELECT * FROM equipment WHERE area = $1', [area]);
            return equipment.map(eq => this.transformEquipment(eq));
        } catch (error) {
            logger.error(`根据厂区获取设备失败 (${area}):`, error);
            throw error;
        }
    }

    /**
     * 创建设备
     */
    async create(equipmentData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO equipment (
                    id, code, name, area, location, responsible,
                    manufacture_date, status, specifications, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING *
            `, [
                equipmentData.id,
                equipmentData.code,
                equipmentData.name,
                equipmentData.area,
                equipmentData.location,
                equipmentData.responsible,
                equipmentData.manufacture_date,
                equipmentData.status || 'active',
                JSON.stringify(equipmentData.specifications || {}),
                now,
                now
            ]);

            return this.transformEquipment(result.rows[0]);
        } catch (error) {
            logger.error('创建设备失败:', error);
            throw error;
        }
    }

    /**
     * 更新设备
     */
    async update(id, equipmentData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE equipment SET
                    code = $1, name = $2, area = $3, location = $4, responsible = $5,
                    manufacture_date = $6, status = $7, specifications = $8, updated_at = $9
                WHERE id = $10
                RETURNING *
            `, [
                equipmentData.code,
                equipmentData.name,
                equipmentData.area,
                equipmentData.location,
                equipmentData.responsible,
                equipmentData.manufacture_date,
                equipmentData.status,
                JSON.stringify(equipmentData.specifications || {}),
                now,
                id
            ]);

            return result.rows.length > 0 ? this.transformEquipment(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新设备失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除设备
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM equipment WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除设备失败 (${id}):`, error);
            throw error;
        }
    }

    // 厂区管理方法

    /**
     * 获取所有厂区
     */
    async findAllFactories() {
        try {
            const factories = await this.findMany(`
                SELECT * FROM factories 
                ORDER BY created_at DESC
            `);
            return factories.map(factory => this.transformFactory(factory));
        } catch (error) {
            logger.error('获取所有厂区失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找厂区
     */
    async findFactoryById(id) {
        try {
            const factory = await this.findOne('SELECT * FROM factories WHERE id = $1', [id]);
            return factory ? this.transformFactory(factory) : null;
        } catch (error) {
            logger.error(`根据ID查找厂区失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找厂区
     */
    async findFactoryByName(name) {
        try {
            const factory = await this.findOne('SELECT * FROM factories WHERE name = $1', [name]);
            return factory ? this.transformFactory(factory) : null;
        } catch (error) {
            logger.error(`根据名称查找厂区失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 创建厂区
     */
    async createFactory(factoryData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO factories (id, name, description, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING *
            `, [
                factoryData.id,
                factoryData.name,
                factoryData.description || '',
                now,
                now
            ]);

            return this.transformFactory(result.rows[0]);
        } catch (error) {
            logger.error('创建厂区失败:', error);
            throw error;
        }
    }

    /**
     * 更新厂区
     */
    async updateFactory(id, factoryData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE factories SET name = $1, description = $2, updated_at = $3
                WHERE id = $4
                RETURNING *
            `, [
                factoryData.name,
                factoryData.description || '',
                now,
                id
            ]);

            return result.rows.length > 0 ? this.transformFactory(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新厂区失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除厂区
     */
    async deleteFactory(id) {
        try {
            const result = await this.query('DELETE FROM factories WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除厂区失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 转换设备数据格式
     */
    transformEquipment(equipment) {
        if (!equipment) return null;

        return {
            ...equipment,
            specifications: this.parseJSON(equipment.specifications, {})
        };
    }

    /**
     * 转换厂区数据格式
     */
    transformFactory(factory) {
        if (!factory) return null;
        return factory;
    }

    /**
     * 获取筛选选项
     */
    async getFilterOptions() {
        try {
            // 获取所有设备的统计信息
            const equipmentResult = await this.query(`
                SELECT
                    area,
                    status,
                    location,
                    responsible,
                    COUNT(*) as count
                FROM equipment
                GROUP BY area, status, location, responsible
                ORDER BY area, status, location, responsible
            `);

            const areas = [];
            const statuses = [];
            const locations = [];
            const responsibles = [];

            equipmentResult.rows.forEach(row => {
                if (row.area && !areas.find(a => a.area === row.area)) {
                    areas.push({ area: row.area, count: 0 });
                }
                if (row.status && !statuses.find(s => s.status === row.status)) {
                    statuses.push({ status: row.status, count: 0 });
                }
                if (row.location && !locations.find(l => l.location === row.location)) {
                    locations.push({ location: row.location, count: 0 });
                }
                if (row.responsible && !responsibles.find(r => r.responsible === row.responsible)) {
                    responsibles.push({ responsible: row.responsible, count: 0 });
                }
            });

            // 计算每个选项的数量
            const areaCountResult = await this.query(`
                SELECT area, COUNT(*) as count
                FROM equipment
                WHERE area IS NOT NULL
                GROUP BY area
            `);
            areaCountResult.rows.forEach(row => {
                const area = areas.find(a => a.area === row.area);
                if (area) area.count = parseInt(row.count);
            });

            const statusCountResult = await this.query(`
                SELECT status, COUNT(*) as count
                FROM equipment
                WHERE status IS NOT NULL
                GROUP BY status
            `);
            statusCountResult.rows.forEach(row => {
                const status = statuses.find(s => s.status === row.status);
                if (status) status.count = parseInt(row.count);
            });

            const locationCountResult = await this.query(`
                SELECT location, COUNT(*) as count
                FROM equipment
                WHERE location IS NOT NULL
                GROUP BY location
            `);
            locationCountResult.rows.forEach(row => {
                const location = locations.find(l => l.location === row.location);
                if (location) location.count = parseInt(row.count);
            });

            const responsibleCountResult = await this.query(`
                SELECT responsible, COUNT(*) as count
                FROM equipment
                WHERE responsible IS NOT NULL
                GROUP BY responsible
            `);
            responsibleCountResult.rows.forEach(row => {
                const responsible = responsibles.find(r => r.responsible === row.responsible);
                if (responsible) responsible.count = parseInt(row.count);
            });

            return {
                areas,
                statuses,
                locations,
                responsibles
            };
        } catch (error) {
            logger.error('获取筛选选项失败:', error);
            return {
                areas: [],
                statuses: [],
                locations: [],
                responsibles: []
            };
        }
    }

    /**
     * 获取设备统计信息
     */
    async getStatistics() {
        try {
            const totalResult = await this.query('SELECT COUNT(*) as count FROM equipment');
            const total = parseInt(totalResult.rows[0].count) || 0;

            const statusResult = await this.query(`
                SELECT status, COUNT(*) as count
                FROM equipment
                GROUP BY status
            `);

            const areaResult = await this.query(`
                SELECT area, COUNT(*) as count
                FROM equipment
                WHERE area IS NOT NULL
                GROUP BY area
            `);

            return {
                total,
                byStatus: statusResult.rows.map(row => ({
                    status: row.status,
                    count: parseInt(row.count)
                })),
                byArea: areaResult.rows.map(row => ({
                    area: row.area,
                    count: parseInt(row.count)
                }))
            };
        } catch (error) {
            logger.error('获取设备统计信息失败:', error);
            return {
                total: 0,
                byStatus: [],
                byArea: []
            };
        }
    }

    /**
     * 分页查询设备
     */
    async findAllWithPagination(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                search = '',
                area = '',
                status = '',
                location = '',
                responsible = ''
            } = options;

            let whereConditions = [];
            let params = [];
            let paramIndex = 1;

            // 搜索条件
            if (search) {
                whereConditions.push(`(code ILIKE $${paramIndex} OR name ILIKE $${paramIndex} OR model ILIKE $${paramIndex})`);
                params.push(`%${search}%`);
                paramIndex++;
            }

            // 筛选条件
            if (area) {
                whereConditions.push(`area = $${paramIndex}`);
                params.push(area);
                paramIndex++;
            }

            if (status) {
                whereConditions.push(`status = $${paramIndex}`);
                params.push(status);
                paramIndex++;
            }

            if (location) {
                whereConditions.push(`location = $${paramIndex}`);
                params.push(location);
                paramIndex++;
            }

            if (responsible) {
                whereConditions.push(`responsible = $${paramIndex}`);
                params.push(responsible);
                paramIndex++;
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            // 获取总数
            const countQuery = `SELECT COUNT(*) as total FROM equipment ${whereClause}`;
            const countResult = await this.findOne(countQuery, params);
            const total = parseInt(countResult.total) || 0;

            // 获取数据
            const offset = (page - 1) * limit;
            const dataQuery = `
                SELECT * FROM equipment
                ${whereClause}
                ORDER BY created_at DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;
            params.push(limit, offset);

            const equipment = await this.findMany(dataQuery, params);

            return {
                data: equipment.map(eq => this.transformEquipment(eq)),
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('分页查询设备失败:', error);
            throw error;
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
}

module.exports = PostgresEquipmentRepository;
