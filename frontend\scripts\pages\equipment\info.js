/**
 * 设备信息管理页面脚本
 * 处理设备信息页面的业务逻辑
 */

import { createStandardApp } from '../../common/pageInit.js';
import { logUserInfo, debugLog } from '../../common/debugUtils.js';
import EquipmentAPI from '../../api/equipment.js';

// 创建页面应用
const app = createStandardApp({
    setup() {
        const { ref, computed, onMounted, onUnmounted, watch, nextTick } = Vue;

        // 响应式数据
        const loading = ref(false);
        const refreshing = ref(false);
        const showAddModal = ref(false);
        const showAddFactoryModal = ref(false);
        const showAddForm = ref(false);
        const showViewModal = ref(false);
        const showEditModal = ref(false);

        // 防止模态框操作过快导致的闪烁
        const modalOperationInProgress = ref(false);

        // 调试：监控 showViewModal 状态变化
        watch(showViewModal, (newValue, oldValue) => {
            console.log('🔍 showViewModal 状态变化:', {
                from: oldValue,
                to: newValue,
                timestamp: new Date().toISOString(),
                pageInitialized: pageInitialized.value
            });
            if (newValue && !pageInitialized.value) {
                console.error('❌ 警告：页面未初始化完成就尝试显示模态框！');
                console.trace('调用堆栈:');
                // 强制关闭
                nextTick(() => {
                    showViewModal.value = false;
                });
            }
        });

        // 分页相关状态
        const currentPage = ref(1);
        const totalPages = ref(1);
        const itemsPerPage = ref(10);
        const totalEquipment = ref(0);

        // 移动端侧边栏控制
        // 从localStorage获取侧边栏状态，默认为false（桌面端展开，移动端折叠）
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);
        const isMobile = ref(window.innerWidth < 768);
        const searchKeyword = ref('');
        const selectedArea = ref('');
        const selectedStatus = ref('');
        const selectedLocation = ref('');
        const selectedResponsible = ref('');
        const selectAll = ref(false);
        const selectedEquipment = ref([]);

        // 新设备表单数据
        const newEquipment = ref({
            code: '',
            name: '',
            area: '',
            location: '',
            responsible: '',
            manufacture_date: '',
            status: '启用'
        });

        // 当前查看/编辑的设备数据
        const currentEquipment = ref({});

        // 厂区基础数据
        const factoryBaseList = ref([]);

        // 厂区列表数据（用于模态框显示）
        const factoryModalList = ref([]);

        // 新厂区表单数据
        const newFactory = ref({
            id: '',
            name: '',
            description: ''
        });

        // 编辑厂区表单数据
        const editingFactory = ref({
            id: '',
            name: '',
            description: ''
        });

        // 编辑厂区模态框状态
        const showEditFactoryModal = ref(false);

        // 页面初始化状态
        const pageInitialized = ref(false);

        // 模态框安全显示控制
        const canShowModals = computed(() => {
            return pageInitialized.value && !modalOperationInProgress.value;
        });

        // 重置所有模态框状态
        const resetAllModals = () => {
            showAddModal.value = false;
            showAddFactoryModal.value = false;
            showEditFactoryModal.value = false;
            showViewModal.value = false;
            showEditModal.value = false;
            showAddForm.value = false;
            modalOperationInProgress.value = false;
        };

        // 统计数据
        const statistics = ref({
            totalEquipment: 0,
            runningEquipment: 0,
            runningPercentage: 0,
            faultEquipment: 0,
            faultPercentage: 0,
            averageUsage: 0
        });

        // 设备数据
        const equipmentList = ref([]);
        const allFilteredEquipment = ref([]); // 所有筛选后的设备数据（用于图表）

        // 筛选选项数据
        const filterOptions = ref({
            locations: [],
            responsibles: [],
            areas: [],
            statuses: []
        });

        // 排序状态
        const sortField = ref(''); // 当前排序字段
        const sortOrder = ref(''); // 排序方向：'asc' 或 'desc'

        // 图表实例
        let statusChart = null;
        let factoryChart = null;

        // 初始化标志
        const isInitializing = ref(true);

        // 销毁所有图表
        const destroyCharts = () => {
            if (statusChart) {
                statusChart.destroy();
                statusChart = null;
            }
            if (factoryChart) {
                factoryChart.destroy();
                factoryChart = null;
            }
        };

        // 初始化设备状态分布图
        const initStatusChart = () => {
            const ctx = document.getElementById('statusChart');
            if (!ctx) return;

            // 确保画布没有被其他图表使用
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            // 计算状态分布数据（使用所有筛选后的数据）
            const activeCount = allFilteredEquipment.value.filter(eq => eq.status === 'active').length;
            const inactiveCount = allFilteredEquipment.value.filter(eq => eq.status === 'inactive').length;

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['启用', '停用'],
                    datasets: [{
                        data: [activeCount, inactiveCount],
                        backgroundColor: [
                            '#10B981', // 绿色 - 启用
                            '#EF4444'  // 红色 - 停用
                        ],
                        borderWidth: 0,
                        cutout: '60%'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false // 使用自定义图例
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = activeCount + inactiveCount;
                                    const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                    return `${context.label}: ${context.parsed}台 (${percentage}%)`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateRotate: true,
                        duration: 1000
                    }
                }
            });
        };

        // 初始化厂区设备分布图
        const initFactoryChart = () => {
            const ctx = document.getElementById('factoryChart');
            if (!ctx) return;

            // 确保画布没有被其他图表使用
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            // 按照图片中的厂区顺序和名称映射
            const factoryMapping = {
                '东莞迅安': '东莞迅安',
                '泰国知勉': '泰国知勉',
                '湖北知腾': '湖北知腾'
            };

            // 计算厂区分布数据，按固定顺序
            const factoryOrder = ['东莞迅安', '泰国知勉', '湖北知腾'];
            const factoryData = {};

            // 初始化所有厂区为0
            factoryOrder.forEach(factory => {
                factoryData[factory] = 0;
            });

            // 统计实际数据（使用所有筛选后的数据）
            allFilteredEquipment.value.forEach(eq => {
                const area = eq.area;
                if (factoryMapping[area]) {
                    factoryData[factoryMapping[area]]++;
                }
            });

            const labels = factoryOrder;
            const data = factoryOrder.map(factory => factoryData[factory]);

            // 动态计算Y轴最大值
            const maxValue = Math.max(...data);
            let yAxisMax, stepSize;

            if (maxValue === 0) {
                // 没有数据时
                yAxisMax = 5;
                stepSize = 1;
            } else if (maxValue <= 5) {
                // 数据较少时
                yAxisMax = 5;
                stepSize = 1;
            } else if (maxValue <= 10) {
                // 中等数据量
                yAxisMax = 10;
                stepSize = 2;
            } else if (maxValue <= 20) {
                // 较大数据量
                yAxisMax = Math.ceil(maxValue * 1.2);
                stepSize = Math.ceil(yAxisMax / 5);
            } else {
                // 大数据量
                yAxisMax = Math.ceil(maxValue * 1.1);
                stepSize = Math.ceil(yAxisMax / 6);
            }



            factoryChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '设备数量',
                        data: data,
                        backgroundColor: '#3B82F6', // 统一蓝色，如图所示
                        borderRadius: 8, // 圆角
                        borderSkipped: false,
                        barThickness: 40, // 固定柱子宽度
                        maxBarThickness: 50
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#3B82F6',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed.y}台设备`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: yAxisMax, // 动态设置最大值
                            ticks: {
                                stepSize: stepSize, // 动态设置步长
                                color: '#6B7280',
                                font: {
                                    size: 12
                                },
                                callback: function(value) {
                                    return value;
                                }
                            },
                            grid: {
                                color: '#E5E7EB',
                                lineWidth: 1,
                                drawBorder: false
                            },
                            border: {
                                display: false
                            }
                        },
                        x: {
                            ticks: {
                                color: '#6B7280',
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10,
                            left: 10,
                            right: 10
                        }
                    }
                }
            });
        };

        // 刷新图表
        const refreshCharts = () => {
            // 确保DOM元素存在且数据已加载
            if (!document.getElementById('statusChart') || !document.getElementById('factoryChart')) {
                return;
            }

            // 先销毁所有现有图表
            destroyCharts();

            // 使用setTimeout确保DOM完全渲染和销毁完成
            setTimeout(() => {
                initStatusChart();
                initFactoryChart();
            }, 150);
        };

        // 中文排序辅助函数
        const compareChineseStrings = (a, b, order) => {
            // 处理空值
            if (!a && !b) return 0;
            if (!a) return order === 'asc' ? 1 : -1;
            if (!b) return order === 'asc' ? -1 : 1;

            // 使用 Intl.Collator 进行更精确的中文排序
            const collator = new Intl.Collator(['zh-CN', 'zh-TW', 'zh-HK'], {
                numeric: true,           // 支持数字排序
                sensitivity: 'accent',   // 区分重音
                ignorePunctuation: false,
                caseFirst: 'lower'
            });

            const comparison = collator.compare(a, b);
            return order === 'asc' ? comparison : -comparison;
        };

        // 排序函数
        const handleSort = (field) => {
            if (sortField.value === field) {
                // 如果点击的是当前排序字段，切换排序方向
                if (sortOrder.value === 'asc') {
                    sortOrder.value = 'desc';
                } else if (sortOrder.value === 'desc') {
                    // 如果已经是降序，取消排序
                    sortField.value = '';
                    sortOrder.value = '';
                } else {
                    sortOrder.value = 'asc';
                }
            } else {
                // 如果点击的是新字段，设置为升序
                sortField.value = field;
                sortOrder.value = 'asc';
            }
        };

        // 获取排序图标的样式
        const getSortIconClass = (field) => {
            if (sortField.value !== field) {
                return 'text-gray-400'; // 默认灰色
            }
            if (sortOrder.value === 'asc') {
                return 'text-blue-600'; // 升序蓝色
            } else if (sortOrder.value === 'desc') {
                return 'text-blue-600'; // 降序蓝色
            }
            return 'text-gray-400';
        };

        // 获取排序图标的路径
        const getSortIconPath = (field) => {
            if (sortField.value !== field || sortOrder.value === '') {
                return "M8 9l4-4 4 4m0 6l-4 4-4-4"; // 双向箭头
            }
            if (sortOrder.value === 'asc') {
                return "M8 15l4-4 4 4"; // 向上箭头
            } else {
                return "M8 9l4 4 4-4"; // 向下箭头
            }
        };

        // 计算属性 - 过滤和排序后的设备列表
        const filteredEquipment = computed(() => {
            let result = [...equipmentList.value];

            // 如果有排序字段，进行排序
            if (sortField.value && sortOrder.value) {
                result.sort((a, b) => {
                    let aValue = a[sortField.value];
                    let bValue = b[sortField.value];

                    // 处理空值
                    if (aValue == null && bValue == null) return 0;
                    if (aValue == null) return sortOrder.value === 'asc' ? 1 : -1;
                    if (bValue == null) return sortOrder.value === 'asc' ? -1 : 1;

                    // 处理不同数据类型的排序
                    if (sortField.value === 'manufacture_date') {
                        // 日期排序
                        const dateA = new Date(aValue || '1900-01-01');
                        const dateB = new Date(bValue || '1900-01-01');
                        const comparison = dateA.getTime() - dateB.getTime();
                        return sortOrder.value === 'asc' ? comparison : -comparison;
                    } else if (typeof aValue === 'string' && typeof bValue === 'string') {
                        // 使用专门的中文排序函数
                        return compareChineseStrings(aValue, bValue, sortOrder.value);
                    } else {
                        // 数值或其他类型排序
                        if (aValue < bValue) {
                            return sortOrder.value === 'asc' ? -1 : 1;
                        }
                        if (aValue > bValue) {
                            return sortOrder.value === 'asc' ? 1 : -1;
                        }
                        return 0;
                    }
                });
            }

            return result;
        });

        // 计算属性 - 带设备数量的厂区列表（直接使用后端统计数据）
        const factoryModalListWithCount = computed(() => {
            // 确保数据稳定性，避免在模态框显示时频繁更新
            if (!showAddFactoryModal.value && factoryModalList.value.length === 0) {
                return [];
            }

            // 直接返回后端已经统计好的数据，不在前端重新计算
            // 因为equipmentList.value只包含当前页面的设备，统计会不准确
            return factoryModalList.value.map(factory => ({
                ...factory,
                equipmentCount: factory.equipmentCount || 0
            }));
        });

        // 计算属性 - 动态厂区列表（基于真实设备数据）
        const factoryList = computed(() => {
            // 统计每个厂区的设备数量
            const factoryStats = {};
            equipmentList.value.forEach(equipment => {
                if (factoryStats[equipment.area]) {
                    factoryStats[equipment.area]++;
                } else {
                    factoryStats[equipment.area] = 1;
                }
            });

            // 生成厂区列表，包含基础厂区和动态发现的厂区
            const factories = [];

            // 添加基础厂区
            factoryBaseList.value.forEach(baseFactory => {
                factories.push({
                    id: baseFactory.id,
                    name: baseFactory.name,
                    description: baseFactory.description,
                    equipmentCount: factoryStats[baseFactory.name] || 0
                });
            });

            // 添加设备中发现的新厂区（不在基础列表中的）
            Object.keys(factoryStats).forEach(areaName => {
                const existsInBase = factoryBaseList.value.some(base => base.name === areaName);
                if (!existsInBase) {
                    // 生成简单的ID（取厂区名称的前两个字符）
                    const id = areaName.length >= 2 ? areaName.substring(0, 2).toUpperCase() : areaName.toUpperCase();
                    factories.push({
                        id: id,
                        name: areaName,
                        description: '-',
                        equipmentCount: factoryStats[areaName]
                    });
                }
            });

            return factories.filter(factory => factory.equipmentCount > 0 || factoryBaseList.value.some(base => base.name === factory.name));
        });

        // 获取设备的厂区ID
        const getFactoryId = (areaName) => {
            if (!areaName) return '';

            // 从基础厂区列表中查找对应的ID
            const factory = factoryBaseList.value.find(f => f.name === areaName);
            if (factory) {
                return factory.id;
            }

            // 如果没找到，生成简单的ID
            return areaName.length >= 2 ? areaName.substring(0, 2).toUpperCase() : areaName.toUpperCase();
        };

        // 状态映射
        const statusMap = {
            'active': '启用',
            'inactive': '停用'
        };

        // 方法
        const getStatusClass = (status) => {
            switch (status) {
                case 'active':
                case '启用':
                    return 'status-active';
                case 'inactive':
                case '停用':
                    return 'status-inactive';
                default:
                    return 'status-active';
            }
        };

        // 获取状态显示文本
        const getStatusText = (status) => {
            return statusMap[status] || status;
        };

        // 格式化日期
        const formatDate = (dateString) => {
            if (!dateString) return '-';
            try {
                // 如果是 YYYY-MM-DD 格式，直接返回
                if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                    return dateString;
                }

                // 如果包含时间，提取日期部分
                if (dateString.includes('T')) {
                    return dateString.split('T')[0];
                }

                // 其他情况，解析日期并格式化
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        };

        const toggleSelectAll = () => {
            if (selectAll.value) {
                selectedEquipment.value = filteredEquipment.value.map(eq => eq.id);
            } else {
                selectedEquipment.value = [];
            }
        };

        // 监听选择状态变化，自动更新全选状态
        watch([selectedEquipment, filteredEquipment], () => {
            if (filteredEquipment.value.length === 0) {
                selectAll.value = false;
            } else {
                const allSelected = filteredEquipment.value.every(eq =>
                    selectedEquipment.value.includes(eq.id)
                );
                selectAll.value = allSelected;
            }
        }, { deep: true });

        // 分页相关函数
        const goToPage = (page) => {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
                fetchEquipmentList();
            }
        };

        const handlePageSizeChange = () => {
            currentPage.value = 1; // 重置到第一页
            fetchEquipmentList();
        };

        // 防抖定时器
        let filterChangeTimer = null;
        let chartRefreshTimer = null;

        // 监听搜索和过滤条件变化，重置到第一页
        watch([searchKeyword, selectedArea, selectedStatus, selectedLocation, selectedResponsible], () => {
            currentPage.value = 1;
            // 使用防抖避免频繁调用
            if (filterChangeTimer) {
                clearTimeout(filterChangeTimer);
            }
            filterChangeTimer = setTimeout(() => {
                fetchEquipmentList();
                fetchAllFilteredEquipment(); // 同时获取所有筛选数据用于图表
            }, 200); // 200ms防抖
        });

        // 监听所有筛选后的设备数据变化，更新图表（防抖处理）
        watch(allFilteredEquipment, () => {
            // 初始化期间不自动刷新图表
            if (isInitializing.value) {
                return;
            }

            if (chartRefreshTimer) {
                clearTimeout(chartRefreshTimer);
            }
            chartRefreshTimer = setTimeout(() => {
                refreshCharts();
            }, 300); // 300ms防抖
        }, { deep: true });



        const viewEquipment = (equipment) => {
            console.log('🔍 viewEquipment 被调用:', equipment);
            console.trace('viewEquipment 调用堆栈:');

            // 检查页面是否已初始化
            if (!pageInitialized.value) {
                console.error('❌ 页面未初始化完成，拒绝显示设备详情');
                return;
            }

            // 检查是否有有效的设备数据
            if (!equipment || !equipment.id) {
                console.error('❌ 无效的设备数据:', equipment);
                return;
            }

            currentEquipment.value = { ...equipment };
            showViewModal.value = true;
        };

        const editEquipment = (equipment) => {
            // 复制设备数据并转换状态为中文
            const equipmentCopy = { ...equipment };
            // 将后端的英文状态转换为前端的中文状态
            if (equipmentCopy.status === 'active') {
                equipmentCopy.status = '启用';
            } else if (equipmentCopy.status === 'inactive') {
                equipmentCopy.status = '停用';
            }
            currentEquipment.value = equipmentCopy;
            showEditModal.value = true;
        };

        // 更新设备
        const updateEquipment = async () => {
            // 验证必填字段
            if (!currentEquipment.value.code || !currentEquipment.value.name || !currentEquipment.value.area ||
                !currentEquipment.value.location || !currentEquipment.value.responsible || !currentEquipment.value.manufacture_date) {
                alert('请填写所有必填字段');
                return;
            }

            try {
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                // 转换状态为API格式
                const apiStatus = currentEquipment.value.status === '启用' ? 'active' :
                                 (currentEquipment.value.status === '停用' ? 'inactive' : currentEquipment.value.status);

                const response = await fetch(`/api/equipment/${currentEquipment.value.id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: currentEquipment.value.code,
                        name: currentEquipment.value.name,
                        area: currentEquipment.value.area,
                        location: currentEquipment.value.location,
                        responsible: currentEquipment.value.responsible,
                        manufacture_date: currentEquipment.value.manufacture_date,
                        status: apiStatus
                    })
                });

                if (!response.ok) {
                    const result = await response.json();
                    throw new Error(result.message || '更新设备失败');
                }

                const result = await response.json();
                if (result.success) {
                    // 关闭模态框
                    showEditModal.value = false;

                    // 强制刷新数据以获取最新状态
                    await fetchEquipmentList(false, true); // 强制刷新
                    await fetchAllFilteredEquipment(); // 刷新图表数据

                    // 强制刷新图表
                    setTimeout(() => {
                        refreshCharts();
                    }, 100);

                    // 显示成功消息
                    alert('设备更新成功！');
                } else {
                    throw new Error(result.message || '更新设备失败');
                }
            } catch (error) {
                console.error('更新设备失败:', error);
                alert('更新设备失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        const deleteEquipment = async (equipment) => {
            if (confirm(`确定要删除设备 ${equipment.name} 吗？`)) {
                try {
                    loading.value = true;

                    const token = sessionStorage.getItem('authToken');
                    const response = await fetch(`/api/equipment/${equipment.id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {

                        // 检查当前页面的数据量，决定是否需要跳转页面
                        const currentPageItemCount = equipmentList.value.length;

                        // 如果当前页只有1条数据且不是第1页，删除后应该跳转到前一页
                        if (currentPageItemCount === 1 && currentPage.value > 1) {
                            currentPage.value = currentPage.value - 1;
                        }

                        // 立即强制刷新数据以获取最新的分页信息
                        await fetchEquipmentList(false, true); // 强制刷新
                        await fetchAllFilteredEquipment(); // 刷新图表数据

                        // 强制刷新图表，不依赖watch监听器
                        setTimeout(() => {
                            refreshCharts();
                        }, 100);

                        // 验证设备是否真的从列表中消失了
                        const stillExists = equipmentList.value.find(eq => eq.id === equipment.id);
                        if (stillExists) {
                            console.error('❌ 警告：设备仍然存在于前端列表中！', stillExists);
                            // 再次尝试刷新
                            setTimeout(async () => {
                                await fetchEquipmentList(false, true);
                                await fetchAllFilteredEquipment(); // 同时刷新图表数据
                            }, 1000);
                        }

                        // 使用非阻塞通知
                        if (window.showNotification) {
                            window.showNotification('设备删除成功！', 'success');
                        }
                    } else {
                        console.error('❌ 后端删除失败:', result.message);
                        throw new Error(result.message || '删除设备失败');
                    }
                } catch (error) {
                    console.error('❌ 删除设备失败:', error);
                    alert('删除设备失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            }
        };

        // 批量删除设备
        const batchDeleteEquipment = async () => {
            if (selectedEquipment.value.length === 0) {
                alert('请先选择要删除的设备');
                return;
            }

            // 获取选中设备的名称用于确认
            const selectedNames = selectedEquipment.value.map(id => {
                const equipment = equipmentList.value.find(eq => eq.id === id);
                return equipment ? equipment.name : id;
            }).join('、');

            if (confirm(`确定要删除以下 ${selectedEquipment.value.length} 台设备吗？\n\n${selectedNames}\n\n此操作不可撤销！`)) {
                try {
                    loading.value = true;

                    const token = sessionStorage.getItem('authToken');
                    const response = await fetch('/api/equipment/batch', {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            equipmentIds: selectedEquipment.value
                        })
                    });

                    if (!response.ok) {
                        const result = await response.json();
                        throw new Error(result.message || '批量删除设备失败');
                    }

                    const result = await response.json();

                    // 清空选择
                    selectedEquipment.value = [];
                    selectAll.value = false;

                    // 检查当前页面的数据量，决定是否需要跳转页面
                    const currentPageItemCount = equipmentList.value.length;
                    const deletedCount = result.data && result.data.success ? result.data.success.length : 0;

                    // 如果删除的数量等于当前页面的数量且不是第1页，删除后应该跳转到前一页
                    if (deletedCount >= currentPageItemCount && currentPage.value > 1) {
                        currentPage.value = currentPage.value - 1;
                    }

                    // 立即强制刷新数据以获取最新状态
                    await fetchEquipmentList(false, true); // 强制刷新
                    await fetchAllFilteredEquipment(); // 刷新图表数据

                    // 强制刷新图表
                    setTimeout(() => {
                        refreshCharts();
                    }, 100);

                    // 显示详细结果
                    if (result.data) {
                        const { success, failed, total } = result.data;
                        let message = `批量删除完成！\n\n`;
                        message += `总计: ${total} 台设备\n`;
                        message += `成功: ${success.length} 台\n`;

                        if (failed.length > 0) {
                            message += `失败: ${failed.length} 台\n\n失败详情:\n`;
                            failed.forEach(item => {
                                // 查找设备名称来显示更友好的信息
                                const equipment = equipmentList.value.find(eq => eq.id === item.id);
                                const equipmentName = equipment ? equipment.name : item.id;
                                message += `- ${equipmentName}: ${item.reason}\n`;
                            });
                        }
                        // 使用非阻塞通知而不是alert
                        if (window.showNotification) {
                            window.showNotification(message, failed.length > 0 ? 'warning' : 'success');
                        }
                    } else {
                        const msg = result.success ? '批量删除成功！' : result.message || '批量删除失败';
                        if (window.showNotification) {
                            window.showNotification(msg, result.success ? 'success' : 'error');
                        }
                    }
                } catch (error) {
                    console.error('批量删除设备失败:', error);
                    alert('批量删除设备失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            }
        };

        // 添加设备
        const addEquipment = async () => {
            // 验证必填字段
            if (!newEquipment.value.code || !newEquipment.value.name || !newEquipment.value.area ||
                !newEquipment.value.location || !newEquipment.value.responsible || !newEquipment.value.manufacture_date) {
                alert('请填写所有必填字段');
                return;
            }

            try {
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                // 状态映射：前端中文状态转换为后端英文状态
                let apiStatus = 'active'; // 默认状态
                switch (newEquipment.value.status) {
                    case '启用':
                        apiStatus = 'active';
                        break;
                    case '停用':
                        apiStatus = 'inactive';
                        break;
                    default:
                        apiStatus = 'active';
                }

                const response = await fetch('/api/equipment', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: newEquipment.value.code,
                        name: newEquipment.value.name,
                        area: newEquipment.value.area,
                        location: newEquipment.value.location,
                        responsible: newEquipment.value.responsible,
                        manufacture_date: newEquipment.value.manufacture_date,
                        status: apiStatus
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('添加设备API错误响应:', errorText);
                    throw new Error(`添加设备失败 (${response.status}): ${errorText}`);
                }

                const result = await response.json();
                if (result.success) {
                    // 重置表单
                    newEquipment.value = {
                        code: '',
                        name: '',
                        area: '',
                        location: '',
                        responsible: '',
                        manufacture_date: '',
                        status: '启用'
                    };

                    // 关闭模态框
                    showAddModal.value = false;

                    // 强制刷新数据以获取最新状态
                    await fetchEquipmentList(false, true); // 强制刷新
                    await fetchAllFilteredEquipment(); // 刷新图表数据

                    // 强制刷新图表
                    setTimeout(() => {
                        refreshCharts();
                    }, 100);

                    // 显示成功消息
                    alert('设备添加成功！');
                } else {
                    throw new Error(result.message || '添加设备失败');
                }
            } catch (error) {
                console.error('添加设备失败:', error);
                alert('添加设备失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // API调用函数
        const fetchEquipmentList = async (skipLoading = false, forceRefresh = false) => {
            try {
                if (!skipLoading) {
                    loading.value = true;
                }



                // 构建查询参数
                const params = {
                    page: currentPage.value,
                    limit: itemsPerPage.value,
                    search: searchKeyword.value,
                    area: selectedArea.value,
                    status: selectedStatus.value,
                    location: selectedLocation.value,
                    responsible: selectedResponsible.value
                };

                // 如果是强制刷新，添加时间戳防止缓存
                if (forceRefresh) {
                    params._t = Date.now();
                }

                // 使用EquipmentAPI获取设备列表
                const result = await EquipmentAPI.getEquipment(params);

                if (result.success) {
                    equipmentList.value = result.data.equipment || [];

                    // 更新分页信息
                    if (result.data.pagination) {
                        totalPages.value = result.data.pagination.totalPages;
                        totalEquipment.value = result.data.pagination.total;
                    }

                    // 更新统计数据
                    const stats = result.data.statistics;
                    if (stats) {
                        statistics.value.totalEquipment = stats.total || 0;

                        // 确保byStatus数组存在
                        const byStatus = stats.byStatus || [];

                        // 计算启用设备数量
                        const activeCount = byStatus.find(s => s.status === 'active')?.count || 0;
                        statistics.value.runningEquipment = activeCount;
                        statistics.value.runningPercentage = stats.total > 0 ? Math.round((activeCount / stats.total) * 100) : 0;

                        // 计算停用设备数量
                        const inactiveCount = byStatus.find(s => s.status === 'inactive')?.count || 0;
                        statistics.value.faultEquipment = inactiveCount;
                        statistics.value.faultPercentage = stats.total > 0 ? Math.round((inactiveCount / stats.total) * 100) : 0;

                        statistics.value.averageUsage = statistics.value.runningPercentage / 10; // 简单计算
                    }

                    // 图表会通过 watch filteredEquipment 自动更新，这里不需要手动刷新
                } else {
                    throw new Error(result.message || '获取设备列表失败');
                }
            } catch (error) {
                console.error('获取设备列表失败:', error);
                // 只在非导入刷新时显示错误提示
                if (!window.isImportRefresh) {
                    alert('获取设备列表失败: ' + error.message);
                }
            } finally {
                if (!skipLoading) {
                    loading.value = false;
                }
            }
        };

        // 获取所有筛选后的设备数据（用于图表）
        const fetchAllFilteredEquipment = async () => {
            try {
                const token = sessionStorage.getItem('authToken');

                // 构建查询参数（不包含分页参数）
                const params = {
                    search: searchKeyword.value,
                    area: selectedArea.value,
                    status: selectedStatus.value,
                    location: selectedLocation.value,
                    responsible: selectedResponsible.value,
                    limit: 1000 // 获取大量数据，确保包含所有筛选结果
                };

                // 移除空参数
                Object.keys(params).forEach(key => {
                    if (params[key] === '' || params[key] === null || params[key] === undefined) {
                        delete params[key];
                    }
                });

                const queryString = new URLSearchParams(params).toString();
                // 添加时间戳防止缓存
                const timestamp = Date.now();
                const urlWithTimestamp = `/api/equipment?${queryString}&_t=${timestamp}`;

                const response = await fetch(urlWithTimestamp, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (!response.ok) {
                    throw new Error(`获取筛选数据失败 (${response.status})`);
                }

                const result = await response.json();
                if (result.success) {
                    allFilteredEquipment.value = result.data.equipment || [];
                } else {
                    throw new Error(result.message || '获取筛选数据失败');
                }
            } catch (error) {
                console.error('获取所有筛选数据失败:', error);
                allFilteredEquipment.value = [];
            }
        };

        // 获取筛选选项
        const fetchFilterOptions = async () => {
            try {
                const token = sessionStorage.getItem('authToken');
                const response = await fetch('/api/equipment/filter-options', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (!response.ok) {
                    throw new Error(`获取筛选选项失败 (${response.status})`);
                }

                const result = await response.json();
                if (result.success) {
                    filterOptions.value = result.data;
                } else {
                    throw new Error(result.message || '获取筛选选项失败');
                }
            } catch (error) {
                console.error('获取筛选选项失败:', error);
            }
        };

        // 获取厂区列表
        const fetchFactoryList = async (skipLoading = false, forceRefresh = false) => {
            try {
                const token = sessionStorage.getItem('authToken');



                // 构建URL，添加防缓存参数
                let url = '/api/equipment/factories';
                if (forceRefresh) {
                    const params = new URLSearchParams({
                        _t: Date.now(),
                        _r: Math.random().toString(36).substring(7)
                    });
                    url += '?' + params.toString();
                }

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        // 添加防缓存请求头
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });



                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('厂区API错误响应:', errorText);
                    throw new Error(`获取厂区列表失败 (${response.status}): ${errorText}`);
                }

                const result = await response.json();


                if (result.success) {
                    factoryBaseList.value = result.data || [];
                    // 直接使用后端返回的设备数量统计，不要重新初始化
                    factoryModalList.value = result.data || [];
                } else {
                    throw new Error(result.message || '获取厂区列表失败');
                }
            } catch (error) {
                console.error('获取厂区列表失败:', error);
            }
        };

        // 简单的消息提示函数
        const showSuccessMessage = (message) => {
            // 创建临时的成功提示元素
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
            toast.textContent = message;
            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        };

        const showErrorMessage = (message) => {
            // 创建临时的错误提示元素
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
            toast.textContent = message;
            document.body.appendChild(toast);

            // 5秒后自动移除（错误消息显示时间稍长）
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 5000);
        };

        // 刷新数据
        const refreshData = async (skipLoading = false) => {
            // 如果不跳过加载状态，则显示刷新状态
            if (!skipLoading) {
                refreshing.value = true;
            }

            // 保存当前选中状态，避免刷新时按钮闪烁
            const savedSelectedEquipment = [...selectedEquipment.value];
            const savedSelectAll = selectAll.value;

            try {
                // 暂时禁用筛选条件监听器，避免重复调用
                if (filterChangeTimer) {
                    clearTimeout(filterChangeTimer);
                }

                // 始终跳过全局loading状态，使用我们自己的refreshing状态
                await fetchEquipmentList(true, true); // skipLoading=true, forceRefresh=true
                await fetchFactoryList(true, true); // skipLoading=true, forceRefresh=true
                await fetchFilterOptions(); // 刷新筛选选项
                await fetchAllFilteredEquipment(); // 刷新图表数据

                // 恢复选中状态（只保留仍然存在的设备）
                if (savedSelectedEquipment.length > 0) {
                    const currentEquipmentIds = equipmentList.value.map(eq => eq.id);
                    selectedEquipment.value = savedSelectedEquipment.filter(id => currentEquipmentIds.includes(id));
                    // 更新全选状态
                    selectAll.value = selectedEquipment.value.length > 0 && selectedEquipment.value.length === filteredEquipment.value.length;
                }

                // 显示成功提示（仅在用户主动点击刷新时显示）
                if (!skipLoading) {
                    showSuccessMessage('页面数据已全部刷新完成！');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                // 恢复原始状态
                selectedEquipment.value = savedSelectedEquipment;
                selectAll.value = savedSelectAll;
                // 显示错误提示
                showErrorMessage('刷新数据失败: ' + error.message);
            } finally {
                // 确保刷新状态被重置
                if (!skipLoading) {
                    refreshing.value = false;
                }
            }
        };

        // 导出设备数据
        const isExporting = ref(false);
        const exportEquipment = async () => {
            // 防止重复点击
            if (isExporting.value) {
                return;
            }

            try {
                isExporting.value = true;
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                const response = await fetch('/api/equipment/export', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('导出失败');
                }

                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let fileName = '设备信息.xlsx';
                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                    if (fileNameMatch) {
                        fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
                    }
                }

                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('导出设备数据失败:', error);
                alert('导出设备数据失败: ' + error.message);
            } finally {
                loading.value = false;
                isExporting.value = false;
            }
        };

        // 导入设备数据
        const importEquipment = () => {
            // 触发文件选择
            const fileInput = document.querySelector('input[type="file"]');
            fileInput.click();
        };

        // 处理文件选择
        const handleFileSelect = async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (!allowedTypes.includes(file.type)) {
                alert('请选择Excel文件（.xlsx或.xls格式）');
                return;
            }

            try {
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/equipment/import', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                // 检查是否有导入数据
                if (result.data && result.data.total > 0) {
                    // 如果有成功的记录，强制刷新设备列表
                    if (result.data.success && result.data.success.length > 0) {
                        try {
                            await fetchEquipmentList(false, true); // 强制刷新
                        } catch (refreshError) {
                            console.error('刷新设备列表失败:', refreshError);
                        }
                    }

                    // 显示导入结果
                    let message = `导入完成！\n\n`;
                    message += `总计: ${result.data.total} 台设备\n`;
                    message += `成功: ${result.data.success ? result.data.success.length : 0} 台\n`;

                    if (result.data.failed && result.data.failed.length > 0) {
                        message += `失败: ${result.data.failed.length} 台\n\n失败详情:\n`;
                        result.data.failed.forEach(item => {
                            message += `- 第${item.row}行: ${item.reason}\n`;
                        });
                    }

                    alert(message);
                } else {
                    // 没有导入数据或完全失败
                    throw new Error(result.message || '导入失败');
                }
            } catch (error) {
                console.error('导入设备数据失败:', error);
                alert('导入设备数据失败: ' + error.message);
            } finally {
                loading.value = false;
                // 清空文件输入
                event.target.value = '';
            }
        };

        // 初始化数据
        const initData = async () => {
            try {
                // 初始化时直接调用各个数据获取函数，避免重复调用
                await fetchFilterOptions(); // 获取筛选选项
                await fetchEquipmentList(true, false); // skipLoading=true, forceRefresh=false
                await fetchFactoryList(true, false); // skipLoading=true, forceRefresh=false
                await fetchAllFilteredEquipment(); // 获取图表数据

                // 初始化完成后，手动刷新图表一次
                nextTick(() => {
                    refreshCharts();
                    // 标记初始化完成，之后允许自动刷新
                    isInitializing.value = false;
                    pageInitialized.value = true;
                });
            } catch (error) {
                console.error('初始化失败:', error);
                isInitializing.value = false;
                pageInitialized.value = true;
            }
        };

        // 页面加载时初始化数据
        onMounted(() => {
            console.log('🚀 设备信息页面开始初始化...');

            // 首先重置所有模态框状态
            resetAllModals();

            // 强制重置 showViewModal
            showViewModal.value = false;

            // 清空当前设备数据
            currentEquipment.value = {};

            initData();
            setTimeout(initCharts, 100);
            window.addEventListener('resize', handleResize);

            // 延迟标记页面初始化完成
            setTimeout(() => {
                // 再次确保模态框状态正确
                resetAllModals();
                showViewModal.value = false;
                pageInitialized.value = true;
                console.log('✅ 设备信息页面初始化完成');
            }, 500);
        });

        // 页面卸载时清理图表
        onUnmounted(() => {
            destroyCharts();
            if (chartRefreshTimer) {
                clearTimeout(chartRefreshTimer);
            }
            if (filterChangeTimer) {
                clearTimeout(filterChangeTimer);
            }
        });

        // 添加厂区
        const addFactory = async () => {
            // 验证必填字段
            if (!newFactory.value.id || !newFactory.value.name) {
                alert('请填写厂区ID和厂区名称');
                return;
            }

            try {
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                const response = await fetch('/api/equipment/factories', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: newFactory.value.id,
                        name: newFactory.value.name,
                        description: newFactory.value.description || ''
                    })
                });

                if (!response.ok) {
                    throw new Error('添加厂区失败');
                }

                const result = await response.json();
                if (result.success) {
                    // 重置表单
                    newFactory.value = {
                        id: '',
                        name: '',
                        description: ''
                    };

                    // 隐藏添加表单
                    showAddForm.value = false;

                    // 强制刷新厂区列表以获取最新数据
                    await fetchFactoryList(false, true); // 强制刷新

                    // 显示成功消息
                    alert('厂区添加成功！');
                } else {
                    throw new Error(result.message || '添加厂区失败');
                }
            } catch (error) {
                console.error('添加厂区失败:', error);
                alert('添加厂区失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // 编辑厂区
        const editFactory = (factory) => {
            // 多重安全检查
            if (!pageInitialized.value || modalOperationInProgress.value || showEditFactoryModal.value) {
                return;
            }

            // 验证传入的厂区数据
            if (!factory || !factory.id) {
                console.warn('editFactory: 无效的厂区数据');
                return;
            }

            modalOperationInProgress.value = true;

            try {
                editingFactory.value = { ...factory };
                // 使用 nextTick 确保 DOM 更新完成
                Vue.nextTick(() => {
                    showEditFactoryModal.value = true;
                });
            } finally {
                // 延迟重置操作状态
                setTimeout(() => {
                    modalOperationInProgress.value = false;
                }, 300);
            }
        };

        // 更新厂区
        const updateFactory = async () => {
            // 验证必填字段
            if (!editingFactory.value.id || !editingFactory.value.name) {
                alert('请填写厂区ID和厂区名称');
                return;
            }

            try {
                loading.value = true;
                const token = sessionStorage.getItem('authToken');

                const response = await fetch(`/api/equipment/factories/${editingFactory.value.id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: editingFactory.value.name,
                        description: editingFactory.value.description || ''
                    })
                });

                if (!response.ok) {
                    const result = await response.json();
                    throw new Error(result.message || '更新厂区失败');
                }

                const result = await response.json();
                if (result.success) {
                    // 关闭模态框
                    showEditFactoryModal.value = false;

                    // 强制刷新厂区列表以获取最新数据
                    await fetchFactoryList(false, true); // 强制刷新

                    // 显示成功消息
                    alert('厂区更新成功！');
                } else {
                    throw new Error(result.message || '更新厂区失败');
                }
            } catch (error) {
                console.error('更新厂区失败:', error);
                alert('更新厂区失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // 删除厂区
        const deleteFactory = async (factory) => {
            if (factory.equipmentCount > 0) {
                alert(`无法删除厂区 ${factory.name}，该厂区下还有 ${factory.equipmentCount} 台设备`);
                return;
            }

            if (confirm(`确定要删除厂区 ${factory.name} 吗？`)) {
                try {
                    loading.value = true;
                    const token = sessionStorage.getItem('authToken');

                    const response = await fetch(`/api/equipment/factories/${factory.id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('删除厂区失败');
                    }

                    const result = await response.json();
                    if (result.success) {
                        // 强制刷新厂区列表以获取最新数据
                        await fetchFactoryList(false, true); // 强制刷新
                        alert('厂区删除成功！');
                    } else {
                        throw new Error(result.message || '删除厂区失败');
                    }
                } catch (error) {
                    console.error('删除厂区失败:', error);
                    alert('删除厂区失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            }
        };

        // 取消添加厂区
        const cancelAddFactory = () => {
            // 重置表单
            newFactory.value = {
                id: '',
                name: '',
                description: ''
            };
            // 隐藏添加表单
            showAddForm.value = false;
        };

        // 打开厂区管理模态框
        const openFactoryModal = async () => {
            // 防止重复操作
            if (modalOperationInProgress.value || showAddFactoryModal.value) {
                return;
            }

            try {
                modalOperationInProgress.value = true;

                // 先强制刷新厂区列表数据，确保数据是最新的
                await fetchFactoryList(false, true); // 强制刷新

                // 使用 nextTick 确保 DOM 更新完成后再显示模态框
                await Vue.nextTick();

                // 重置添加表单状态
                showAddForm.value = false;

                // 最后显示模态框，避免闪烁
                showAddFactoryModal.value = true;

            } catch (error) {
                console.error('打开厂区管理模态框失败:', error);
                alert('打开厂区管理失败，请重试');
            } finally {
                // 延迟重置操作标志，防止过快的连续操作
                setTimeout(() => {
                    modalOperationInProgress.value = false;
                }, 300);
            }
        };

        // 关闭厂区管理模态框
        const closeFactoryModal = () => {
            // 防止重复操作
            if (modalOperationInProgress.value || !showAddFactoryModal.value) {
                return;
            }

            modalOperationInProgress.value = true;

            // 立即隐藏模态框
            showAddFactoryModal.value = false;
            showAddForm.value = false;

            // 重置新厂区表单数据
            newFactory.value = {
                id: '',
                name: '',
                description: ''
            };

            // 延迟重置操作标志
            setTimeout(() => {
                modalOperationInProgress.value = false;
            }, 300);
        };

        // 初始化图表
        const initCharts = () => {
            // 设备状态分布饼图
            const statusCtx = document.getElementById('statusChart');
            if (statusCtx) {
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['启用', '停用'],
                        datasets: [{
                            data: [statistics.value.runningEquipment, statistics.value.faultEquipment],
                            backgroundColor: ['#4ade80', '#ef4444'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // 厂区设备分布柱状图
            const distributionCtx = document.getElementById('distributionChart');
            if (distributionCtx) {
                new Chart(distributionCtx, {
                    type: 'bar',
                    data: {
                        labels: ['东莞厂区', '深圳厂区', '广州厂区'],
                        datasets: [{
                            data: [18, 5, 3],
                            backgroundColor: ['#3b82f6', '#06b6d4', '#8b5cf6'],
                            borderRadius: 4,
                            borderSkipped: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: '#f3f4f6'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        };

        // 侧边栏控制函数
        const toggleSidebar = () => {
            sidebarOpen.value = !sidebarOpen.value;
            // 保存状态到localStorage
            localStorage.setItem('sidebarOpen', sidebarOpen.value.toString());
        };

        const closeSidebar = () => {
            sidebarOpen.value = false;
            // 保存状态到localStorage
            localStorage.setItem('sidebarOpen', 'false');
        };

        // 监听窗口大小变化
        const handleResize = () => {
            isMobile.value = window.innerWidth < 768;
            if (!isMobile.value) {
                sidebarOpen.value = false;
            }
        };



        return {
            loading,
            refreshing,
            isExporting,
            modalOperationInProgress,
            pageInitialized,
            canShowModals,
            showAddModal,
            showAddFactoryModal,
            showAddForm,
            showViewModal,
            showEditModal,
            sidebarOpen,
            isMobile,
            toggleSidebar,
            closeSidebar,
            searchKeyword,
            selectedArea,
            selectedStatus,
            selectedLocation,
            selectedResponsible,
            selectAll,
            selectedEquipment,
            newEquipment,
            currentEquipment,
            factoryList,
            factoryBaseList,
            factoryModalList,
            factoryModalListWithCount,
            newFactory,
            editingFactory,
            showEditFactoryModal,
            statistics,
            equipmentList,
            filteredEquipment,
            allFilteredEquipment,
            filterOptions,
            // 排序相关
            sortField,
            sortOrder,
            handleSort,
            getSortIconClass,
            getSortIconPath,
            // 图表相关
            refreshCharts,
            getStatusClass,
            getStatusText,
            formatDate,
            toggleSelectAll,
            viewEquipment,
            editEquipment,
            updateEquipment,
            deleteEquipment,
            batchDeleteEquipment,
            addEquipment,
            addFactory,
            editFactory,
            updateFactory,
            deleteFactory,
            cancelAddFactory,
            openFactoryModal,
            closeFactoryModal,
            refreshData,
            exportEquipment,
            importEquipment,
            handleFileSelect,
            getFactoryId,
            // 分页相关
            currentPage,
            totalPages,
            itemsPerPage,
            totalEquipment,
            goToPage,
            handlePageSizeChange
        };
    },
    requiredPermissions: ['equipment_info'],
    onUserLoaded: async (user) => {
        logUserInfo('设备信息页面初始化完成', user);
    }
}).mount('#app');
