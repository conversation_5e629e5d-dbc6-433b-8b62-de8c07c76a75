/**
 * 通用工具函数库
 * 包含系统中常用的公共函数，避免重复代码
 */

import { getCurrentUser } from '../api/auth.js';

/**
 * 隐藏页面加载指示器并显示主应用
 * 统一的加载指示器隐藏函数
 */
export function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }

    // 显示主应用容器
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.style.display = 'flex';
    }
}

/**
 * 显示页面加载指示器并隐藏主应用
 * 统一的加载指示器显示函数
 */
export function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'flex';
    }

    // 隐藏主应用容器
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.style.display = 'none';
    }
}

/**
 * 通用认证检查函数
 * 检查用户认证状态并返回用户信息
 * @returns {Promise<Object>} 用户信息对象
 * @throws {Error} 认证失败时抛出错误
 */
export async function checkAuthentication() {
    try {
        const user = await getCurrentUser();
        return user;
    } catch (error) {
        console.error('认证失败:', error);
        // 认证失败，跳转到登录页
        window.location.href = '/login';
        throw error;
    }
}

/**
 * 通用页面初始化函数
 * 包含认证检查和加载指示器处理
 * @param {Function} initCallback - 初始化完成后的回调函数
 * @returns {Promise<Object>} 用户信息对象
 */
export async function initializePage(initCallback = null) {
    try {
        const user = await checkAuthentication();
        
        // 执行自定义初始化回调
        if (initCallback && typeof initCallback === 'function') {
            await initCallback(user);
        }
        
        return user;
    } finally {
        // 确保加载指示器被隐藏
        hideLoading();
    }
}

/**
 * 权限检查函数
 * 检查用户是否具有指定权限
 * @param {Object} user - 用户对象
 * @param {string|Array} requiredPermissions - 需要的权限
 * @param {string} redirectUrl - 权限不足时的重定向URL
 * @returns {boolean} 是否有权限
 */
export function checkPermissions(user, requiredPermissions, redirectUrl = '/') {
    if (!user) {
        console.warn('用户信息不存在');
        window.location.href = '/login';
        return false;
    }

    // 只有admin角色拥有所有权限
    const isSystemAdmin = user.role === 'admin';

    if (isSystemAdmin) {
        return true;
    }

    // 检查具体权限
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
    const userPermissions = user.permissions || [];
    
    const hasPermission = permissions.some(permission => userPermissions.includes(permission));
    
    if (!hasPermission) {
        console.warn('用户权限不足:', { required: permissions, user: userPermissions });
        if (window.showNotification) {
            window.showNotification('您没有访问此页面的权限', 'error');
        } else {
            alert('您没有访问此页面的权限');
        }
        setTimeout(() => {
            window.location.href = redirectUrl;
        }, 1000);
        return false;
    }
    
    return true;
}

/**
 * 角色检查函数
 * 检查用户是否具有指定角色
 * @param {Object} user - 用户对象
 * @param {string|Array} requiredRoles - 需要的角色
 * @param {string} redirectUrl - 角色不匹配时的重定向URL
 * @returns {boolean} 是否有角色
 */
export function checkRoles(user, requiredRoles, redirectUrl = '/dashboard') {
    if (!user) {
        console.warn('用户信息不存在');
        window.location.href = '/login';
        return false;
    }

    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    const userRole = user.role;

    const hasRole = roles.includes(userRole);

    if (!hasRole) {
        console.warn('用户角色不匹配:', { required: roles, user: userRole });
        if (window.showNotification) {
            window.showNotification('您没有访问此页面的权限', 'error');
        } else {
            alert('您没有访问此页面的权限');
        }
        setTimeout(() => {
            window.location.href = redirectUrl;
        }, 1000);
        return false;
    }

    return true;
}

/**
 * 格式化日期时间
 * 统一的日期时间格式化函数，确保正确显示本地时间
 * @param {string|Date} dateTime - 日期时间
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(dateTime, options = {}) {
    if (!dateTime) return '-';

    try {
        const date = new Date(dateTime);
        if (isNaN(date.getTime())) {
            console.warn('无效的日期时间:', dateTime);
            return '-';
        }

        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Shanghai' // 明确指定中国时区
        };

        const formatOptions = { ...defaultOptions, ...options };

        return date.toLocaleString('zh-CN', formatOptions);
    } catch (error) {
        console.error('时间格式化错误:', error, '原始值:', dateTime);
        return '-';
    }
}

/**
 * 格式化日期
 * 统一的日期格式化函数
 * @param {string|Date} date - 日期
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
    return formatDateTime(date, {
        hour: undefined,
        minute: undefined
    });
}

/**
 * 格式化时间
 * 统一的时间格式化函数
 * @param {string|Date} time - 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time) {
    if (!time) return '';
    
    const date = new Date(time);
    if (isNaN(date.getTime())) return '';
    
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 获取状态样式类
 * 通用的状态样式类获取函数
 * @param {string} status - 状态值
 * @param {Object} statusMap - 状态映射对象
 * @param {string} defaultClass - 默认样式类
 * @returns {string} 样式类名
 */
export function getStatusClass(status, statusMap = {}, defaultClass = 'bg-gray-100 text-gray-800') {
    const defaultStatusMap = {
        planned: 'bg-blue-100 text-blue-800',
        in_progress: 'bg-green-100 text-green-800',
        paused: 'bg-yellow-100 text-yellow-800',
        completed: 'bg-gray-100 text-gray-800',
        cancelled: 'bg-red-100 text-red-800',
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800'
    };
    
    const combinedMap = { ...defaultStatusMap, ...statusMap };
    return combinedMap[status] || defaultClass;
}

/**
 * 获取状态文本
 * 通用的状态文本获取函数
 * @param {string} status - 状态值
 * @param {Object} statusMap - 状态映射对象
 * @returns {string} 状态文本
 */
export function getStatusText(status, statusMap = {}) {
    const defaultStatusMap = {
        planned: '计划中',
        in_progress: '执行中',
        paused: '已暂停',
        completed: '已完成',
        cancelled: '已取消',
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝'
    };
    
    const combinedMap = { ...defaultStatusMap, ...statusMap };
    return combinedMap[status] || status;
}

/**
 * 获取优先级样式类
 * 通用的优先级样式类获取函数
 * @param {string} priority - 优先级值
 * @returns {string} 样式类名
 */
export function getPriorityClass(priority) {
    const priorityMap = {
        low: 'bg-gray-100 text-gray-600',
        medium: 'bg-blue-100 text-blue-600',
        high: 'bg-orange-100 text-orange-600',
        urgent: 'bg-red-100 text-red-600'
    };
    
    return priorityMap[priority] || 'bg-gray-100 text-gray-600';
}

/**
 * 获取优先级文本
 * 通用的优先级文本获取函数
 * @param {string} priority - 优先级值
 * @returns {string} 优先级文本
 */
export function getPriorityText(priority) {
    const priorityMap = {
        low: '低',
        medium: '中',
        high: '高',
        urgent: '紧急'
    };
    
    return priorityMap[priority] || priority;
}

/**
 * 高级防抖工具函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @param {Object} options - 配置选项
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay, options = {}) {
    const { immediate = false, maxWait = null, leading = false, trailing = true } = options;
    let timeoutId;
    let lastCallTime;
    let lastInvokeTime = 0;
    let result;

    function invokeFunc(time) {
        lastInvokeTime = time;
        result = func.apply(this, arguments);
        return result;
    }

    function shouldInvoke(time) {
        const timeSinceLastCall = time - lastCallTime;
        const timeSinceLastInvoke = time - lastInvokeTime;

        return (lastCallTime === undefined ||
                (timeSinceLastCall >= delay) ||
                (timeSinceLastCall < 0) ||
                (maxWait !== null && timeSinceLastInvoke >= maxWait));
    }

    function debounced(...args) {
        const time = Date.now();
        const isInvoking = shouldInvoke(time);

        lastCallTime = time;

        if (isInvoking) {
            if (timeoutId === undefined) {
                if (leading) {
                    return invokeFunc(time);
                }
            }
            if (maxWait !== null) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    if (trailing) {
                        invokeFunc(Date.now());
                    }
                    timeoutId = undefined;
                }, delay);
                return invokeFunc(time);
            }
        }

        if (timeoutId === undefined) {
            timeoutId = setTimeout(() => {
                if (trailing && lastCallTime !== undefined) {
                    invokeFunc(Date.now());
                }
                timeoutId = undefined;
            }, delay);
        }

        return result;
    }

    debounced.cancel = function() {
        if (timeoutId !== undefined) {
            clearTimeout(timeoutId);
            timeoutId = undefined;
        }
        lastCallTime = lastInvokeTime = undefined;
    };

    debounced.flush = function() {
        if (timeoutId !== undefined) {
            clearTimeout(timeoutId);
            const time = Date.now();
            timeoutId = undefined;
            return invokeFunc(time);
        }
        return result;
    };

    debounced.pending = function() {
        return timeoutId !== undefined;
    };

    return debounced;
}

/**
 * 高级节流工具函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @param {Object} options - 配置选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay, options = {}) {
    const { leading = true, trailing = true } = options;
    return debounce(func, delay, {
        leading,
        trailing,
        maxWait: delay
    });
}

/**
 * 性能监控工具
 */
export const PerformanceMonitor = {
    // 性能标记存储
    marks: new Map(),

    /**
     * 开始性能测量
     * @param {string} name - 测量名称
     */
    start(name) {
        this.marks.set(name, {
            startTime: performance.now(),
            startMemory: performance.memory ? performance.memory.usedJSHeapSize : 0
        });
    },

    /**
     * 结束性能测量
     * @param {string} name - 测量名称
     * @returns {Object} 性能数据
     */
    end(name) {
        const mark = this.marks.get(name);
        if (!mark) {
            console.warn(`性能标记 "${name}" 不存在`);
            return null;
        }

        const endTime = performance.now();
        const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

        const result = {
            name,
            duration: endTime - mark.startTime,
            memoryDelta: endMemory - mark.startMemory,
            timestamp: new Date().toISOString()
        };

        this.marks.delete(name);

        // 记录慢操作
        if (result.duration > 100) {
            console.warn(`慢操作检测: ${name} - ${result.duration.toFixed(2)}ms`);
        }

        return result;
    },

    /**
     * 测量函数执行性能
     * @param {Function} func - 要测量的函数
     * @param {string} name - 测量名称
     * @returns {Function} 包装后的函数
     */
    measure(func, name) {
        return async (...args) => {
            this.start(name);
            try {
                const result = await func.apply(this, args);
                return result;
            } finally {
                this.end(name);
            }
        };
    },

    /**
     * 获取所有性能数据
     * @returns {Array} 性能数据数组
     */
    getStats() {
        return Array.from(this.marks.entries()).map(([name, mark]) => ({
            name,
            running: true,
            elapsed: performance.now() - mark.startTime
        }));
    }
};

/**
 * 内存使用监控
 */
export const MemoryMonitor = {
    /**
     * 获取当前内存使用情况
     * @returns {Object} 内存使用数据
     */
    getUsage() {
        if (!performance.memory) {
            return { supported: false };
        }

        return {
            supported: true,
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit,
            usedMB: (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2),
            totalMB: (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2),
            limitMB: (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)
        };
    },

    /**
     * 检查内存使用是否过高
     * @param {number} threshold - 阈值（0-1之间）
     * @returns {boolean} 是否超过阈值
     */
    isHighUsage(threshold = 0.8) {
        const usage = this.getUsage();
        if (!usage.supported) return false;

        return (usage.used / usage.limit) > threshold;
    },

    /**
     * 建议垃圾回收
     */
    suggestGC() {
        if (this.isHighUsage(0.7)) {
            console.warn('内存使用较高，建议清理不必要的对象引用');

            // 尝试触发垃圾回收（仅在开发环境）
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }
        }
    }
};
