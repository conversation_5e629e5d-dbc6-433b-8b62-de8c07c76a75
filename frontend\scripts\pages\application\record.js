/**
 * 申请记录页面逻辑
 */

import { getCurrentUser } from '../../../scripts/api/auth.js';
import { getApplicationById } from '../../../scripts/api/application.js';
import Sidebar from '../../../components/common/Sidebar.js';
import ApplicationRecordList from '../../../components/application/ApplicationRecordList.js';
import ApplicationDetail from '../../../components/application/ApplicationDetail.js';

const { createApp, ref, onMounted } = Vue;

// 隐藏加载指示器
function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

const app = createApp({
    components: {
        Sidebar,
        ApplicationRecordList,
        ApplicationDetail
    },
    setup() {
        // 状态变量
        const currentUser = ref(null);
        const isAuthenticated = ref(false);
        const showDetailModal = ref(false);
        const currentDetail = ref(null);

        // 初始化
        onMounted(() => {
            checkAuth();
            // 确保加载指示器被隐藏
            hideLoading();
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                currentUser.value = await getCurrentUser();
                isAuthenticated.value = true;
            } catch (error) {
                console.error('认证失败:', error);
                // 认证失败，跳转到登录页
                window.location.href = '/login';
            }
        }

        // 查看申请详情
        async function viewDetail(id) {
            try {
                currentDetail.value = await getApplicationById(id);
                showDetailModal.value = true;
            } catch (error) {
                console.error('获取详情失败:', error);
                alert('获取详情失败: ' + (error.response?.data?.message || error.message));
            }
        }

        // 关闭详情模态框
        function closeDetail() {
            showDetailModal.value = false;
        }

        return {
            currentUser,
            isAuthenticated,
            showDetailModal,
            currentDetail,
            viewDetail,
            closeDetail
        };
    }
});

// 配置Vue以抑制特定警告
app.config.warnHandler = (msg, instance, trace) => {
    // 抑制关于script和style标签的警告
    if (msg.includes('Tags with side effect') || msg.includes('<script>') || msg.includes('<style>')) {
        return;
    }
    console.warn(msg, instance, trace);
};

// 挂载Vue应用
app.mount('#app');
