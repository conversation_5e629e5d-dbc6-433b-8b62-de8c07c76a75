/**
 * 设备管理控制器
 * 处理设备相关的业务逻辑
 */

const logger = require('../utils/logger');
const { databaseAdapter } = require('../database/databaseAdapter');
const HealthAssessmentService = require('../services/healthAssessmentService');
const FaultPredictor = require('../utils/faultPredictor');
const { v4: uuidv4 } = require('uuid');

class EquipmentController {
    constructor() {
        this.equipmentRepository = databaseAdapter.getEquipmentRepository();
        this.faultPredictor = new FaultPredictor();
        this.databaseAdapter = databaseAdapter;
    }

    // 延迟初始化健康度评估服务
    getHealthService() {
        if (!this._healthService) {
            this._healthService = new HealthAssessmentService();
        }
        return this._healthService;
    }

    /**
     * 获取设备列表
     */
    async getEquipmentList(req, res) {
        try {
            logger.debug('获取设备列表', { user: req.user.username });

            // 获取查询参数
            const {
                page = 1,
                limit = 10,
                search = '',
                area = '',
                status = '',
                location = '',
                responsible = ''
            } = req.query;

            // 如果没有分页参数，返回所有数据（保持向后兼容）
            if (!req.query.page && !req.query.limit) {
                const equipment = await this.equipmentRepository.findAll();
                const statistics = await this.equipmentRepository.getStatistics();

                // 为每个设备添加健康度信息
                const equipmentWithHealth = await this.addHealthScoreToEquipment(equipment);

                res.json({
                    success: true,
                    data: {
                        equipment: equipmentWithHealth,
                        statistics
                    },
                    message: '获取设备列表成功'
                });
                return;
            }

            // 分页查询
            const paginationResult = await this.equipmentRepository.findAllWithPagination({
                page: parseInt(page),
                limit: parseInt(limit),
                search,
                area,
                status,
                location,
                responsible
            });

            const statistics = await this.equipmentRepository.getStatistics();

            // 为分页结果中的设备添加健康度信息
            const equipmentWithHealth = await this.addHealthScoreToEquipment(paginationResult.data);

            res.json({
                success: true,
                data: {
                    equipment: equipmentWithHealth,
                    statistics,
                    pagination: {
                        total: paginationResult.pagination.total,
                        totalPages: paginationResult.pagination.pages,
                        currentPage: paginationResult.pagination.page,
                        limit: paginationResult.pagination.limit
                    }
                },
                message: '获取设备列表成功'
            });
        } catch (error) {
            logger.error('获取设备列表失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取设备列表失败: ' + error.message
            });
        }
    }

    /**
     * 为设备列表添加健康度信息
     * 从数据库中获取真实的健康度记录，包含四维度详细分数
     * 注意：不会自动计算健康度，只使用已有记录或估算值
     * 健康度计算应该通过以下方式触发：
     * 1. 用户手动点击"计算健康度"按钮
     * 2. 设备信息更新时自动触发
     * 3. 维修记录创建时自动触发
     * 4. 批量计算API调用
     */
    async addHealthScoreToEquipment(equipmentList) {
        try {
            // 创建健康度仓库实例
            const PostgresHealthRepository = require('../database/postgresHealthRepository');
            const healthRepository = new PostgresHealthRepository();

            // 为每个设备获取最新的健康度记录
            const equipmentWithHealth = await Promise.all(
                equipmentList.map(async (equipment) => {
                    try {
                        // 从数据库获取最新健康度记录
                        const latestHealth = await healthRepository.findLatestHealth(equipment.id);

                        let healthScore = 75; // 默认健康度
                        let healthMetrics = {
                            age: 75,
                            repair: 75,
                            fault: 75,
                            maintenance: 75
                        };
                        let lastAssessment = null;

                        if (latestHealth && latestHealth.health_score !== undefined &&
                            latestHealth.age_score !== null && latestHealth.repair_frequency_score !== null &&
                            latestHealth.fault_severity_score !== null && latestHealth.maintenance_score !== null) {
                            // 使用数据库中的真实健康度（只有当四维度分数都存在时）
                            healthScore = latestHealth.health_score;



                            healthMetrics = {
                                age: latestHealth.age_score,
                                repair: latestHealth.repair_frequency_score,
                                fault: latestHealth.fault_severity_score,
                                maintenance: latestHealth.maintenance_score
                            };
                            lastAssessment = latestHealth.assessment_date || latestHealth.created_at;
                        } else {
                            // 如果没有健康度记录，使用基于设备状态的估算值，避免自动计算
                            logger.debug('设备无健康度记录，使用估算值', { equipmentId: equipment.id });
                            healthScore = this.calculateFixedHealthScore(equipment);
                            healthMetrics = {
                                age: healthScore,
                                repair: healthScore,
                                fault: healthScore,
                                maintenance: healthScore
                            };
                            lastAssessment = null;
                        }

                        return {
                            ...equipment,
                            healthScore: Math.round(healthScore),
                            healthMetrics: {
                                age: Math.round(healthMetrics.age),
                                repair: Math.round(healthMetrics.repair),
                                fault: Math.round(healthMetrics.fault),
                                maintenance: Math.round(healthMetrics.maintenance)
                            },
                            lastAssessment: lastAssessment
                        };
                    } catch (error) {
                        logger.error('获取设备健康度失败:', { equipmentId: equipment.id, error: error.message });
                        // 如果获取失败，直接使用固定值，避免自动计算
                        const backupScore = this.calculateFixedHealthScore(equipment);
                        return {
                            ...equipment,
                            healthScore: Math.round(backupScore),
                            healthMetrics: {
                                age: Math.round(backupScore),
                                repair: Math.round(backupScore),
                                fault: Math.round(backupScore),
                                maintenance: Math.round(backupScore)
                            },
                            lastAssessment: null
                        };
                    }
                })
            );

            return equipmentWithHealth;
        } catch (error) {
            logger.error('添加健康度信息失败:', error);
            // 如果整体失败，返回原始设备列表，但添加默认健康度
            return equipmentList.map(equipment => ({
                ...equipment,
                healthScore: 75, // 默认健康度
                healthMetrics: {
                    age: 75,
                    repair: 75,
                    fault: 75,
                    maintenance: 75
                },
                lastAssessment: null
            }));
        }
    }

    /**
     * 获取设备详情
     */
    async getEquipmentById(req, res) {
        try {
            const { id } = req.params;
            logger.debug('获取设备详情', { equipmentId: id, user: req.user.username });

            const equipment = this.equipmentRepository.findById(id);

            if (!equipment) {
                return res.status(404).json({
                    success: false,
                    message: '设备不存在'
                });
            }

            res.json({
                success: true,
                data: equipment,
                message: '获取设备详情成功'
            });
        } catch (error) {
            logger.error('获取设备详情失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取设备详情失败: ' + error.message
            });
        }
    }

    /**
     * 创建设备
     */
    async createEquipment(req, res) {
        try {
            const equipmentData = req.body;
            logger.info('创建设备', { equipmentData, user: req.user.username });

            // 验证必填字段
            if (!equipmentData.code || !equipmentData.name || !equipmentData.area ||
                !equipmentData.location || !equipmentData.responsible || !equipmentData.manufacture_date) {
                logger.error('创建设备失败 - 缺少必填字段', {
                    receivedFields: Object.keys(equipmentData),
                    equipmentData,
                    user: req.user.username
                });
                return res.status(400).json({
                    success: false,
                    message: '缺少必填字段',
                    details: {
                        required: ['code', 'name', 'area', 'location', 'responsible', 'manufacture_date'],
                        received: Object.keys(equipmentData)
                    }
                });
            }

            // 检查设备编号是否已存在
            const existingEquipment = this.equipmentRepository.findByCode(equipmentData.code);
            if (existingEquipment) {
                return res.status(400).json({
                    success: false,
                    message: '设备编号已存在'
                });
            }

            // 生成设备ID
            const equipmentId = uuidv4();

            // 创建设备
            const newEquipment = this.equipmentRepository.create({
                id: equipmentId,
                ...equipmentData
            });

            res.json({
                success: true,
                data: newEquipment,
                message: '创建设备成功'
            });
        } catch (error) {
            logger.error('创建设备失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '创建设备失败: ' + error.message
            });
        }
    }

    /**
     * 更新设备信息
     */
    async updateEquipment(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            logger.info('更新设备信息', { equipmentId: id, updateData, user: req.user.username });

            // 检查设备是否存在
            const existingEquipment = this.equipmentRepository.findById(id);
            if (!existingEquipment) {
                return res.status(404).json({
                    success: false,
                    message: '设备不存在'
                });
            }

            // 如果更新设备编号，检查是否与其他设备冲突
            if (updateData.code && updateData.code !== existingEquipment.code) {
                const equipmentWithSameCode = this.equipmentRepository.findByCode(updateData.code);
                if (equipmentWithSameCode) {
                    return res.status(400).json({
                        success: false,
                        message: '设备编号已存在'
                    });
                }
            }

            // 更新设备
            const updatedEquipment = this.equipmentRepository.update(id, updateData);

            // 自动触发设备健康度重新计算（如果更新了影响健康度的字段）
            const healthRelevantFields = ['manufacture_date', 'status', 'area', 'last_maintenance_date', 'next_maintenance_date'];
            const hasHealthRelevantChanges = healthRelevantFields.some(field => updateData.hasOwnProperty(field));

            if (hasHealthRelevantChanges) {
                try {
                    const HealthAssessmentService = require('../services/healthAssessmentService');
                    const healthService = new HealthAssessmentService();
                    await healthService.calculateEquipmentHealth(id, 'system_auto');
                    logger.info('设备信息更新后自动更新健康度成功', { equipmentId: id });
                } catch (healthError) {
                    logger.error('设备信息更新后自动更新健康度失败', {
                        equipmentId: id,
                        error: healthError.message
                    });
                    // 健康度更新失败不影响设备信息更新的成功
                }
            }

            res.json({
                success: true,
                data: updatedEquipment,
                message: '更新设备信息成功'
            });
        } catch (error) {
            logger.error('更新设备信息失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '更新设备信息失败: ' + error.message
            });
        }
    }

    /**
     * 删除设备
     */
    async deleteEquipment(req, res) {
        try {
            const { id } = req.params;
            logger.info('删除设备', { equipmentId: id, user: req.user.username });

            // 检查设备是否存在
            const existingEquipment = this.equipmentRepository.findById(id);
            if (!existingEquipment) {
                return res.status(404).json({
                    success: false,
                    message: '设备不存在'
                });
            }

            // 检查设备是否有相关记录
            logger.debug('检查设备相关记录', { equipmentId: id });
            const relatedRecords = this.checkEquipmentRelatedRecords(id);
            logger.debug('相关记录检查结果', { equipmentId: id, hasRecords: relatedRecords.hasRecords });

            if (relatedRecords.hasRecords) {
                logger.warn('设备有相关记录无法删除', { equipmentId: id, details: relatedRecords.details });
                return res.status(400).json({
                    success: false,
                    message: `设备有相关记录无法删除: ${relatedRecords.details.join(', ')}`
                });
            }

            // 删除设备及其相关数据
            logger.debug('开始删除设备及相关数据', { equipmentId: id });
            const deleted = this.deleteEquipmentWithRelatedData(id);
            logger.debug('设备删除操作结果', { equipmentId: id, deleted });

            if (!deleted) {
                logger.error('设备删除操作失败', { equipmentId: id });
                return res.status(500).json({
                    success: false,
                    message: '删除设备失败'
                });
            }

            logger.info('设备删除成功', { equipmentId: id });
            res.json({
                success: true,
                message: '删除设备成功'
            });
        } catch (error) {
            logger.error('删除设备失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '删除设备失败: ' + error.message
            });
        }
    }

    /**
     * 批量删除设备
     */
    async batchDeleteEquipment(req, res) {
        try {
            const { equipmentIds } = req.body;
            logger.info('批量删除设备', { equipmentIds, user: req.user.username });

            // 验证输入
            if (!Array.isArray(equipmentIds) || equipmentIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '设备ID列表不能为空'
                });
            }

            const results = {
                success: [],
                failed: [],
                total: equipmentIds.length
            };

            // 逐个删除设备
            for (const equipmentId of equipmentIds) {
                try {
                    // 检查设备是否存在
                    const existingEquipment = this.equipmentRepository.findById(equipmentId);
                    if (!existingEquipment) {
                        results.failed.push({
                            id: equipmentId,
                            reason: '设备不存在'
                        });
                        continue;
                    }

                    // 检查设备是否有相关记录
                    const relatedRecords = this.checkEquipmentRelatedRecords(equipmentId);
                    if (relatedRecords.hasRecords) {
                        results.failed.push({
                            id: equipmentId,
                            reason: `设备有相关记录无法删除: ${relatedRecords.details.join(', ')}`
                        });
                        continue;
                    }

                    // 删除设备及其相关数据
                    const deleted = this.deleteEquipmentWithRelatedData(equipmentId);
                    if (deleted) {
                        results.success.push({
                            id: equipmentId,
                            name: existingEquipment.name
                        });
                    } else {
                        results.failed.push({
                            id: equipmentId,
                            reason: '删除操作失败'
                        });
                    }
                } catch (error) {
                    results.failed.push({
                        id: equipmentId,
                        reason: error.message
                    });
                }
            }

            // 返回结果
            const isAllSuccess = results.failed.length === 0;
            res.json({
                success: isAllSuccess,
                message: isAllSuccess
                    ? `成功删除 ${results.success.length} 台设备`
                    : `删除完成，成功 ${results.success.length} 台，失败 ${results.failed.length} 台`,
                data: results
            });

        } catch (error) {
            logger.error('批量删除设备失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '批量删除设备失败: ' + error.message
            });
        }
    }

    /**
     * 检查设备是否有相关记录
     * 只检查维修保养记录，健康度记录不阻止删除
     */
    checkEquipmentRelatedRecords(equipmentId) {
        const databaseManager = require('../database/database');
        const db = databaseManager.getConnection();
        const details = [];
        let hasRecords = false;

        try {
            // 检查设备维修记录
            const maintenanceCount = db.prepare('SELECT COUNT(*) as count FROM equipment_maintenance WHERE equipment_id = ?').get(equipmentId);
            if (maintenanceCount.count > 0) {
                details.push(`${maintenanceCount.count}条维修记录`);
                hasRecords = true;
            }

            // 健康度记录不阻止删除，删除设备时会自动清理
            // 检查设备产能记录不阻止删除，删除设备时会自动清理
            // 检查操作员技能记录不阻止删除，删除设备时会自动清理
            // 检查设备操作员关联记录不阻止删除，删除设备时会自动清理

            return { hasRecords, details };
        } catch (error) {
            console.error('检查设备相关记录失败:', error);
            return { hasRecords: true, details: ['检查相关记录时发生错误'] };
        }
    }

    /**
     * 删除设备及其相关数据
     * 包括健康度记录、产能记录、操作员关联等
     */
    deleteEquipmentWithRelatedData(equipmentId) {
        const databaseManager = require('../database/database');
        const db = databaseManager.getConnection();

        try {
            // 使用事务确保数据一致性
            const transaction = db.transaction(() => {
                // 删除设备健康度历史记录
                const deleteHealthHistoryStmt = db.prepare('DELETE FROM equipment_health_history WHERE equipment_id = ?');
                const healthHistoryResult = deleteHealthHistoryStmt.run(equipmentId);
                logger.debug('删除设备健康度历史记录', { equipmentId, deletedRows: healthHistoryResult.changes });

                // 删除设备健康度记录
                const deleteHealthStmt = db.prepare('DELETE FROM equipment_health WHERE equipment_id = ?');
                const healthResult = deleteHealthStmt.run(equipmentId);
                logger.debug('删除设备健康度记录', { equipmentId, deletedRows: healthResult.changes });

                // 删除设备产能记录
                const deleteCapabilityStmt = db.prepare('DELETE FROM equipment_capabilities WHERE equipment_id = ?');
                const capabilityResult = deleteCapabilityStmt.run(equipmentId);
                logger.debug('删除设备产能记录', { equipmentId, deletedRows: capabilityResult.changes });

                // 删除操作员技能记录
                const deleteSkillStmt = db.prepare('DELETE FROM operator_skills WHERE equipment_id = ?');
                const skillResult = deleteSkillStmt.run(equipmentId);
                logger.debug('删除操作员技能记录', { equipmentId, deletedRows: skillResult.changes });

                // 删除设备操作员关联记录
                const deleteOperatorStmt = db.prepare('DELETE FROM equipment_operators WHERE equipment_id = ?');
                const operatorResult = deleteOperatorStmt.run(equipmentId);
                logger.debug('删除设备操作员关联记录', { equipmentId, deletedRows: operatorResult.changes });

                // 最后删除设备主记录
                const deleted = this.equipmentRepository.delete(equipmentId);

                if (!deleted) {
                    throw new Error('删除设备主记录失败');
                }

                logger.info('设备及相关数据删除成功', {
                    equipmentId,
                    deletedRecords: {
                        healthHistory: healthHistoryResult.changes,
                        health: healthResult.changes,
                        capability: capabilityResult.changes,
                        skills: skillResult.changes,
                        operators: operatorResult.changes
                    }
                });

                return true;
            });

            return transaction();
        } catch (error) {
            logger.error('删除设备及相关数据失败', { equipmentId, error: error.message });
            throw error;
        }
    }

    /**
     * 获取厂区列表
     */
    async getFactoryList(req, res) {
        try {
            logger.debug('获取厂区列表', { user: req.user.username });

            const factories = await this.equipmentRepository.findAllFactories();
            const statistics = await this.equipmentRepository.getStatistics();

            // 为每个厂区添加设备数量
            const factoriesWithCount = factories.map(factory => {
                const areaStats = statistics.byArea.find(stat => stat.area === factory.name);
                return {
                    ...factory,
                    equipmentCount: areaStats ? areaStats.count : 0
                };
            });

            res.json({
                success: true,
                data: factoriesWithCount,
                message: '获取厂区列表成功'
            });
        } catch (error) {
            logger.error('获取厂区列表失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取厂区列表失败: ' + error.message
            });
        }
    }

    /**
     * 创建厂区
     */
    async createFactory(req, res) {
        try {
            const factoryData = req.body;
            logger.info('创建厂区', { factoryData, user: req.user.username });

            // 验证必填字段
            if (!factoryData.id || !factoryData.name) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必填字段'
                });
            }

            // 检查厂区ID是否已存在
            const existingFactoryById = this.equipmentRepository.findFactoryById(factoryData.id);
            if (existingFactoryById) {
                return res.status(400).json({
                    success: false,
                    message: '厂区ID已存在'
                });
            }

            // 检查厂区名称是否已存在
            const existingFactoryByName = this.equipmentRepository.findFactoryByName(factoryData.name);
            if (existingFactoryByName) {
                return res.status(400).json({
                    success: false,
                    message: '厂区名称已存在'
                });
            }

            // 创建厂区
            const newFactory = this.equipmentRepository.createFactory(factoryData);

            res.json({
                success: true,
                data: newFactory,
                message: '创建厂区成功'
            });
        } catch (error) {
            logger.error('创建厂区失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '创建厂区失败: ' + error.message
            });
        }
    }

    /**
     * 更新厂区信息
     */
    async updateFactory(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            logger.info('更新厂区信息', { factoryId: id, updateData, user: req.user.username });

            // 检查厂区是否存在
            const existingFactory = this.equipmentRepository.findFactoryById(id);
            if (!existingFactory) {
                return res.status(404).json({
                    success: false,
                    message: '厂区不存在'
                });
            }

            // 如果更新厂区名称，检查是否与其他厂区冲突
            if (updateData.name && updateData.name !== existingFactory.name) {
                const factoryWithSameName = this.equipmentRepository.findFactoryByName(updateData.name);
                if (factoryWithSameName) {
                    return res.status(400).json({
                        success: false,
                        message: '厂区名称已存在'
                    });
                }
            }

            // 更新厂区
            const updatedFactory = this.equipmentRepository.updateFactory(id, updateData);

            res.json({
                success: true,
                data: updatedFactory,
                message: '更新厂区信息成功'
            });
        } catch (error) {
            logger.error('更新厂区信息失败', { error: error.message, factoryId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '更新厂区信息失败: ' + error.message
            });
        }
    }

    /**
     * 删除厂区
     */
    async deleteFactory(req, res) {
        try {
            const { id } = req.params;
            logger.info('删除厂区', { factoryId: id, user: req.user.username });

            // 检查厂区是否存在
            const existingFactory = this.equipmentRepository.findFactoryById(id);
            if (!existingFactory) {
                return res.status(404).json({
                    success: false,
                    message: '厂区不存在'
                });
            }

            // 检查厂区下是否有设备
            const equipmentInFactory = this.equipmentRepository.findByArea(existingFactory.name);
            if (equipmentInFactory.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: `无法删除厂区，该厂区下还有 ${equipmentInFactory.length} 台设备`
                });
            }

            // 删除厂区
            const deleted = this.equipmentRepository.deleteFactory(id);

            if (!deleted) {
                return res.status(500).json({
                    success: false,
                    message: '删除厂区失败'
                });
            }

            res.json({
                success: true,
                message: '删除厂区成功'
            });
        } catch (error) {
            logger.error('删除厂区失败', { error: error.message, factoryId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '删除厂区失败: ' + error.message
            });
        }
    }

    /**
     * 获取设备维修记录
     */
    async getMaintenanceRecords(req, res) {
        try {
            const { id } = req.params;
            logger.debug('获取设备维修记录', { equipmentId: id, user: req.user.username });

            // 从数据库获取真实的维修保养记录
            const maintenanceRepository = this.databaseAdapter.getMaintenanceRepository();
            const records = await maintenanceRepository.findByEquipmentId(id);

            res.json({
                success: true,
                data: records,
                message: '获取维修记录成功'
            });
        } catch (error) {
            logger.error('获取维修记录失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取维修记录失败: ' + error.message
            });
        }
    }

    /**
     * 创建维修记录
     */
    async createMaintenanceRecord(req, res) {
        try {
            const { id } = req.params;
            const recordData = req.body;
            logger.info('创建维修记录', { equipmentId: id, recordData, user: req.user.username });

            // 这里应该实现实际的创建逻辑
            res.json({
                success: true,
                data: { id: 'MR' + Date.now(), equipmentId: id, ...recordData },
                message: '创建维修记录成功'
            });
        } catch (error) {
            logger.error('创建维修记录失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '创建维修记录失败: ' + error.message
            });
        }
    }

    /**
     * 更新维修记录
     */
    async updateMaintenanceRecord(req, res) {
        try {
            const { recordId } = req.params;
            const updateData = req.body;
            logger.info('更新维修记录', { recordId, updateData, user: req.user.username });

            // 这里应该实现实际的更新逻辑
            res.json({
                success: true,
                data: { id: recordId, ...updateData },
                message: '更新维修记录成功'
            });
        } catch (error) {
            logger.error('更新维修记录失败', { error: error.message, recordId: req.params.recordId, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '更新维修记录失败: ' + error.message
            });
        }
    }

    /**
     * 删除维修记录
     */
    async deleteMaintenanceRecord(req, res) {
        try {
            const { recordId } = req.params;
            logger.info('删除维修记录', { recordId, user: req.user.username });

            // 这里应该实现实际的删除逻辑
            res.json({
                success: true,
                message: '删除维修记录成功'
            });
        } catch (error) {
            logger.error('删除维修记录失败', { error: error.message, recordId: req.params.recordId, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '删除维修记录失败: ' + error.message
            });
        }
    }

    /**
     * 获取设备健康度评估
     * 使用真实的健康度计算服务获取详细评估信息
     */
    async getEquipmentHealth(req, res) {
        try {
            const { id } = req.params;
            logger.debug('获取设备健康度评估', { equipmentId: id, user: req.user.username });

            // 获取设备基本信息
            const equipment = await this.equipmentRepository.findById(id);
            if (!equipment) {
                return res.status(404).json({
                    success: false,
                    message: '设备不存在'
                });
            }

            // 尝试从数据库获取最新的健康度记录
            const PostgresHealthRepository = require('../database/postgresHealthRepository');
            const healthRepository = new PostgresHealthRepository();
            let latestHealth = await healthRepository.findLatestHealth(id);

            let healthData;

            // 始终使用健康度计算服务获取完整的详细信息
            try {


                const calculatedHealth = await this.getHealthService().calculateEquipmentHealth(id, req.user.username);



                // 获取维修记录统计
                const maintenanceRepository = this.databaseAdapter.getMaintenanceRepository();
                const maintenanceStats = await maintenanceRepository.getMaintenanceStatsByEquipment(id);

                healthData = {
                    equipmentId: id,
                    overallHealth: calculatedHealth.totalScore,
                    lastAssessment: calculatedHealth.assessmentDate,
                    metrics: {
                        age: calculatedHealth.details.age.score,
                        repairFrequency: calculatedHealth.details.repairFrequency.score,
                        faultSeverity: calculatedHealth.details.faultSeverity.score,
                        maintenance: calculatedHealth.details.maintenance.score
                    },
                    dimensions: {
                        age: calculatedHealth.details.age,
                        repairFrequency: calculatedHealth.details.repairFrequency,
                        faultSeverity: calculatedHealth.details.faultSeverity,
                        maintenance: calculatedHealth.details.maintenance
                    },
                    equipment: {
                        ...equipment,
                        // 添加前端期望的字段，如果不存在则使用默认值
                        model: equipment.model || equipment.specifications?.model || '未知型号',
                        manufacturer: equipment.manufacturer || equipment.specifications?.manufacturer || '未知制造商',
                        install_date: equipment.install_date || equipment.manufacture_date,
                        installDate: equipment.install_date || equipment.manufacture_date,
                        manufactureDate: equipment.manufacture_date
                    },
                    maintenanceStats: maintenanceStats || []
                };
            } catch (calcError) {
                logger.error('获取健康度详细信息失败，使用备用数据', { equipmentId: id, error: calcError.message });

                // 如果计算失败，尝试使用数据库中的记录
                if (latestHealth) {
                    healthData = {
                        equipmentId: id,
                        overallHealth: latestHealth.health_score,
                        lastAssessment: latestHealth.assessment_date || latestHealth.created_at,
                        metrics: {
                            age: latestHealth.age_score || 85,
                            repairFrequency: latestHealth.repair_frequency_score || 85,
                            faultSeverity: latestHealth.fault_severity_score || 85,
                            maintenance: latestHealth.maintenance_score || 85
                        },
                        equipment: {
                            ...equipment,
                            // 添加前端期望的字段，如果不存在则使用默认值
                            model: equipment.model || equipment.specifications?.model || '未知型号',
                            manufacturer: equipment.manufacturer || equipment.specifications?.manufacturer || '未知制造商',
                            install_date: equipment.install_date || equipment.manufacture_date,
                            installDate: equipment.install_date || equipment.manufacture_date,
                            manufactureDate: equipment.manufacture_date
                        },
                        maintenanceStats: []
                    };
                } else {
                    // 最后的备用方案
                    const backupScore = this.calculateFixedHealthScore(equipment);
                    healthData = {
                        equipmentId: id,
                        overallHealth: Math.round(backupScore),
                        lastAssessment: new Date().toISOString(),
                        metrics: {
                            age: 85,
                            repairFrequency: 85,
                            faultSeverity: 85,
                            maintenance: 85
                        },
                        equipment: {
                            ...equipment,
                            // 添加前端期望的字段，如果不存在则使用默认值
                            model: equipment.model || equipment.specifications?.model || '未知型号',
                            manufacturer: equipment.manufacturer || equipment.specifications?.manufacturer || '未知制造商',
                            install_date: equipment.install_date || equipment.manufacture_date,
                            installDate: equipment.install_date || equipment.manufacture_date,
                            manufactureDate: equipment.manufacture_date
                        },
                        maintenanceStats: []
                    };
                }
            }

            // 生成建议
            const recommendations = [];
            const healthScore = healthData.overallHealth;

            if (healthScore < 60) {
                recommendations.push('设备健康度较低，建议立即检修');
                recommendations.push('检查设备关键部件状态');
                recommendations.push('制定详细的维护计划');
            } else if (healthScore < 70) {
                recommendations.push('设备需要关注，建议加强维护');
                recommendations.push('定期检查设备运行状态');
            } else if (healthScore < 80) {
                recommendations.push('设备状态良好，保持定期维护');
            } else {
                recommendations.push('设备状态优秀，继续保持');
            }

            return res.json({
                success: true,
                data: {
                    ...healthData,
                    recommendations: recommendations
                },
                message: '获取设备健康度评估成功'
            });
        } catch (error) {
            logger.error('获取设备健康度评估失败', { error: error.message, equipmentId: req.params.id, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取设备健康度评估失败: ' + error.message
            });
        }
    }



    /**
     * 计算设备健康度
     * POST /api/equipment/:id/health/calculate
     */
    async calculateEquipmentHealth(req, res) {
        try {
            const { id } = req.params;
            const assessor = req.user.username;

            logger.info('开始计算设备健康度', {
                equipmentId: id,
                assessor,
                userRole: req.user.role
            });

            // 计算健康度
            const healthData = await this.getHealthService().calculateEquipmentHealth(id, assessor);

            res.json({
                success: true,
                data: healthData,
                message: '健康度计算成功'
            });

        } catch (error) {
            logger.error('计算设备健康度失败', {
                equipmentId: req.params.id,
                error: error.message,
                user: req.user.username
            });

            res.status(500).json({
                success: false,
                message: '计算设备健康度失败: ' + error.message,
                code: 'CALCULATION_ERROR'
            });
        }
    }

    /**
     * 获取设备健康度历史
     * GET /api/equipment/:id/health/history
     */
    async getEquipmentHealthHistory(req, res) {
        try {
            const { id } = req.params;
            const { limit = 30, startDate, endDate } = req.query;

            logger.debug('获取设备健康度历史', {
                equipmentId: id,
                limit,
                startDate,
                endDate,
                user: req.user.username
            });

            const healthService = this.getHealthService();

            const historyData = await healthService.getEquipmentHealthHistory(
                id,
                parseInt(limit)
            );

            // 如果指定了日期范围，进行过滤
            if (startDate || endDate) {
                const start = startDate ? new Date(startDate) : new Date('1900-01-01');
                const end = endDate ? new Date(endDate) : new Date();

                historyData.history = historyData.history.filter(record => {
                    const recordDate = new Date(record.assessment_date);
                    return recordDate >= start && recordDate <= end;
                });
            }

            res.json({
                success: true,
                data: historyData,
                message: '获取历史记录成功'
            });

        } catch (error) {
            logger.error('获取健康度历史失败', {
                equipmentId: req.params.id,
                error: error.message,
                user: req.user.username
            });

            res.status(500).json({
                success: false,
                message: '获取健康度历史失败: ' + error.message,
                code: 'HISTORY_FETCH_ERROR'
            });
        }
    }

    /**
     * 获取故障预测
     * GET /api/equipment/:id/health/prediction
     */
    async getEquipmentFaultPrediction(req, res) {
        try {
            const { id } = req.params;

            logger.debug('获取设备故障预测', {
                equipmentId: id,
                user: req.user.username
            });

            // 获取维修记录
            const maintenanceRecords = database.getConnection().prepare(`
                SELECT * FROM equipment_maintenance
                WHERE equipment_id = ?
                ORDER BY maintenance_date DESC
            `).all(id);

            if (maintenanceRecords.length === 0) {
                return res.json({
                    success: true,
                    data: {
                        equipmentId: id,
                        canPredict: false,
                        message: '暂无维修记录，无法进行故障预测',
                        dataQuality: 'insufficient'
                    },
                    message: '获取故障预测成功'
                });
            }

            // 进行故障预测
            const prediction = this.faultPredictor.predictNextFailure(maintenanceRecords);

            res.json({
                success: true,
                data: {
                    equipmentId: id,
                    ...prediction
                },
                message: '故障预测成功'
            });

        } catch (error) {
            logger.error('获取故障预测失败', {
                equipmentId: req.params.id,
                error: error.message,
                user: req.user.username
            });

            res.status(500).json({
                success: false,
                message: '获取故障预测失败: ' + error.message,
                code: 'PREDICTION_ERROR'
            });
        }
    }

    /**
     * 获取筛选选项
     */
    async getFilterOptions(req, res) {
        try {
            logger.debug('获取筛选选项', { user: req.user.username });

            const filterOptions = await this.equipmentRepository.getFilterOptions();

            res.json({
                success: true,
                data: filterOptions,
                message: '获取筛选选项成功'
            });
        } catch (error) {
            logger.error('获取筛选选项失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '获取筛选选项失败: ' + error.message
            });
        }
    }

    /**
     * 获取设备健康度统计
     */
    async getHealthStatistics(req, res) {
        try {
            logger.debug('获取设备健康度统计', { user: req.user.username });

            // 从查询参数获取筛选条件
            const filters = {
                area: req.query.area,
                level: req.query.level
            };

            // 使用健康度评估服务获取真实统计数据
            const statistics = await this.getHealthService().getHealthStatistics(filters);

            res.json({
                success: true,
                data: statistics,
                message: '获取设备健康度统计成功'
            });
        } catch (error) {
            logger.error('获取设备健康度统计失败', { error: error.message, user: req.user.username });

            // 如果获取真实数据失败，返回基础统计信息
            try {
                const equipmentResponse = await this.equipmentRepository.getEquipment({});
                const totalEquipment = equipmentResponse.total || 0;

                const fallbackData = {
                    overview: {
                        totalEquipment: totalEquipment,
                        averageHealth: 0,
                        lastUpdated: new Date().toISOString()
                    },
                    distribution: {
                        excellent: 0,
                        good: 0,
                        average: 0,
                        poor: 0,
                        dangerous: 0
                    },
                    warningEquipment: []
                };

                res.json({
                    success: true,
                    data: fallbackData,
                    message: '获取设备健康度统计成功（使用基础数据）'
                });
            } catch (fallbackError) {
                res.status(500).json({
                    success: false,
                    message: '获取设备健康度统计失败: ' + error.message
                });
            }
        }
    }

    /**
     * 导出设备数据为Excel
     */
    async exportEquipment(req, res) {
        try {
            logger.info('导出设备数据', { user: req.user.username });

            // 获取所有设备数据
            const equipmentList = await this.equipmentRepository.findAll();

            // 导入ExcelJS
            const ExcelJS = require('exceljs');

            // 创建工作簿
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('设备信息');

            // 设置列标题和宽度
            worksheet.columns = [
                { header: '设备编号', key: 'code', width: 15 },
                { header: '设备名称', key: 'name', width: 20 },
                { header: '厂区', key: 'area', width: 15 },
                { header: '设备位置', key: 'location', width: 15 },
                { header: '负责人', key: 'responsible', width: 12 },
                { header: '进厂日期', key: 'manufacture_date', width: 15 },
                { header: '状态', key: 'status', width: 10 },
                { header: '创建时间', key: 'created_at', width: 20 },
                { header: '更新时间', key: 'updated_at', width: 20 }
            ];

            // 设置标题行样式
            const headerRow = worksheet.getRow(1);
            headerRow.font = { bold: true };
            headerRow.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE0E0E0' }
            };

            // 添加数据行
            equipmentList.forEach(equipment => {
                worksheet.addRow({
                    code: equipment.code,
                    name: equipment.name,
                    area: equipment.area,
                    location: equipment.location,
                    responsible: equipment.responsible,
                    manufacture_date: equipment.manufacture_date,
                    status: equipment.status === 'active' ? '启用' : '停用',
                    created_at: this.formatDateTime(equipment.created_at),
                    updated_at: this.formatDateTime(equipment.updated_at)
                });
            });

            // 设置响应头
            const fileName = `设备信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

            // 写入响应
            await workbook.xlsx.write(res);
            res.end();

            logger.info('设备数据导出成功', { count: equipmentList.length, user: req.user.username });

        } catch (error) {
            logger.error('导出设备数据失败', { error: error.message, user: req.user.username });
            res.status(500).json({
                success: false,
                message: '导出设备数据失败: ' + error.message
            });
        }
    }

    /**
     * 导入设备数据从Excel
     */
    async importEquipment(req, res) {
        try {
            logger.info('导入设备数据', { user: req.user.username });

            // 检查是否有文件上传
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: '没有上传文件'
                });
            }

            // 导入ExcelJS
            const ExcelJS = require('exceljs');
            const fs = require('fs');
            const { v4: uuidv4 } = require('uuid');

            // 读取Excel文件
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(req.file.path);

            const worksheet = workbook.getWorksheet(1); // 获取第一个工作表
            if (!worksheet) {
                throw new Error('Excel文件中没有找到工作表');
            }

            const results = {
                success: [],
                failed: [],
                total: 0
            };

            // 跳过标题行，从第2行开始处理数据
            for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
                const row = worksheet.getRow(rowNumber);

                // 检查行是否为空
                if (row.hasValues) {
                    results.total++;

                    try {
                        // 读取单元格数据
                        const code = this.getCellValue(row.getCell(1));
                        const name = this.getCellValue(row.getCell(2));
                        const area = this.getCellValue(row.getCell(3));
                        const location = this.getCellValue(row.getCell(4));
                        const responsible = this.getCellValue(row.getCell(5));
                        const manufacture_date = this.getCellValue(row.getCell(6));
                        const statusText = this.getCellValue(row.getCell(7));

                        // 验证必填字段
                        if (!code || !name || !area || !location || !responsible || !manufacture_date) {
                            results.failed.push({
                                row: rowNumber,
                                reason: '缺少必填字段（设备编号、设备名称、厂区、设备位置、负责人、进厂日期）'
                            });
                            continue;
                        }

                        // 验证厂区是否存在，并获取正确的厂区名称
                        const validFactories = await this.equipmentRepository.findAllFactories();
                        let factoryName = area;

                        // 检查输入的是厂区ID还是厂区名称
                        const factoryByName = validFactories.find(factory => factory.name === area);
                        const factoryById = validFactories.find(factory => factory.id === area);

                        if (factoryByName) {
                            // 输入的是厂区名称，直接使用
                            factoryName = factoryByName.name;
                        } else if (factoryById) {
                            // 输入的是厂区ID，转换为厂区名称
                            factoryName = factoryById.name;
                        } else {
                            // 厂区不存在
                            results.failed.push({
                                row: rowNumber,
                                reason: `厂区 "${area}" 不存在，请先在厂区管理中添加该厂区`
                            });
                            continue;
                        }

                        // 转换状态
                        const status = statusText === '停用' ? 'inactive' : 'active';

                        // 检查设备编号是否已存在
                        const existingEquipment = await this.equipmentRepository.findByCode(code);

                        if (existingEquipment) {
                            // 更新现有设备
                            const updateData = {
                                name,
                                area: factoryName, // 使用验证后的厂区名称
                                location,
                                responsible,
                                manufacture_date,
                                status
                            };

                            const updated = await this.equipmentRepository.update(existingEquipment.id, updateData);
                            if (updated) {
                                results.success.push({
                                    row: rowNumber,
                                    action: '更新',
                                    code,
                                    name
                                });
                            } else {
                                results.failed.push({
                                    row: rowNumber,
                                    reason: '更新设备失败'
                                });
                            }
                        } else {
                            // 创建新设备
                            const equipmentData = {
                                id: uuidv4(),
                                code,
                                name,
                                area: factoryName, // 使用验证后的厂区名称
                                location,
                                responsible,
                                manufacture_date,
                                status,
                                specifications: {}
                            };

                            const created = await this.equipmentRepository.create(equipmentData);
                            if (created) {
                                results.success.push({
                                    row: rowNumber,
                                    action: '创建',
                                    code,
                                    name
                                });
                            } else {
                                results.failed.push({
                                    row: rowNumber,
                                    reason: '创建设备失败'
                                });
                            }
                        }

                    } catch (error) {
                        // 提供用户友好的错误信息
                        let friendlyMessage = '处理数据时发生错误';

                        if (error.message.includes('Cannot read properties of undefined')) {
                            friendlyMessage = '系统配置错误，请联系管理员';
                        } else if (error.message.includes('FOREIGN KEY constraint')) {
                            friendlyMessage = '数据关联错误，请检查厂区信息';
                        } else if (error.message.includes('UNIQUE constraint')) {
                            friendlyMessage = '设备编号已存在';
                        } else if (error.message.includes('NOT NULL constraint')) {
                            friendlyMessage = '缺少必填字段';
                        } else {
                            // 保留原始错误信息，但去掉技术细节
                            friendlyMessage = error.message.replace(/Cannot read properties of undefined.*/, '数据格式错误')
                                                         .replace(/Error:.*/, '数据处理失败');
                        }

                        results.failed.push({
                            row: rowNumber,
                            reason: friendlyMessage
                        });
                    }
                }
            }

            // 删除临时文件
            fs.unlinkSync(req.file.path);

            // 返回结果
            const isAllSuccess = results.failed.length === 0;
            res.json({
                success: isAllSuccess,
                message: isAllSuccess
                    ? `成功导入 ${results.success.length} 台设备`
                    : `导入完成，成功 ${results.success.length} 台，失败 ${results.failed.length} 台`,
                data: results
            });

            logger.info('设备数据导入完成', {
                total: results.total,
                success: results.success.length,
                failed: results.failed.length,
                user: req.user.username
            });

        } catch (error) {
            // 删除临时文件（如果存在）
            if (req.file && req.file.path) {
                try {
                    const fs = require('fs');
                    fs.unlinkSync(req.file.path);
                } catch (unlinkError) {
                    console.error('删除临时文件失败:', unlinkError);
                }
            }

            logger.error('导入设备数据失败', { error: error.message, user: req.user.username });

            // 提供用户友好的错误信息
            let friendlyMessage = '导入设备数据失败';

            if (error.message.includes('Excel文件中没有找到工作表')) {
                friendlyMessage = 'Excel文件格式错误，请检查文件是否包含有效的工作表';
            } else if (error.message.includes('没有上传文件')) {
                friendlyMessage = '请选择要导入的Excel文件';
            } else if (error.message.includes('Cannot read properties of undefined')) {
                friendlyMessage = '系统配置错误，请联系管理员';
            } else if (error.message.includes('ENOENT')) {
                friendlyMessage = '文件读取失败，请重新选择文件';
            } else {
                friendlyMessage = '导入过程中发生错误，请检查文件格式是否正确';
            }

            res.status(500).json({
                success: false,
                message: friendlyMessage
            });
        }
    }

    /**
     * 获取Excel单元格的值
     */
    getCellValue(cell) {
        if (!cell || cell.value === null || cell.value === undefined) {
            return '';
        }

        // 处理不同类型的单元格值
        if (typeof cell.value === 'object' && cell.value.text) {
            return cell.value.text.toString().trim();
        }

        return cell.value.toString().trim();
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
        if (!dateTime) return '';

        try {
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        } catch (error) {
            return dateTime;
        }
    }

    /**
     * 批量计算设备健康度
     * POST /api/equipment/health/batch-calculate
     */
    async batchCalculateHealth(req, res) {
        try {
            const { equipmentIds, options = {} } = req.body;
            const assessor = req.user.username;

            logger.info('开始批量计算设备健康度', {
                equipmentCount: equipmentIds ? equipmentIds.length : 0,
                assessor,
                userRole: req.user.role
            });

            // 验证输入
            if (!equipmentIds || !Array.isArray(equipmentIds) || equipmentIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '请提供有效的设备ID列表'
                });
            }

            const results = {
                successful: 0,
                failed: 0,
                total: equipmentIds.length,
                details: [],
                summary: {
                    averageScore: 0,
                    totalScore: 0
                }
            };

            // 批量计算健康度
            for (const equipmentId of equipmentIds) {
                try {
                    const healthData = await this.getHealthService().calculateEquipmentHealth(equipmentId, assessor);

                    results.successful++;
                    results.details.push({
                        equipmentId,
                        success: true,
                        totalScore: healthData.totalScore,
                        level: this.getHealthLevel(healthData.totalScore),
                        assessmentDate: healthData.assessmentDate,
                        details: healthData.details
                    });

                    results.summary.totalScore += healthData.totalScore;

                } catch (error) {
                    logger.error('计算设备健康度失败', {
                        equipmentId,
                        error: error.message,
                        assessor
                    });

                    results.failed++;
                    results.details.push({
                        equipmentId,
                        success: false,
                        error: error.message
                    });
                }
            }

            // 计算平均分
            if (results.successful > 0) {
                results.summary.averageScore = Math.round(results.summary.totalScore / results.successful);
            }

            const isAllSuccess = results.failed === 0;

            res.json({
                success: isAllSuccess,
                data: {
                    details: results.details, // 返回所有详情，包括成功和失败的
                    summary: {
                        total: results.total,
                        successful: results.successful,
                        failed: results.failed,
                        averageScore: results.summary.averageScore
                    }
                },
                message: isAllSuccess
                    ? `批量计算完成，成功计算 ${results.successful} 台设备的健康度`
                    : `批量计算完成，成功 ${results.successful} 台，失败 ${results.failed} 台`
            });

            logger.info('批量计算设备健康度完成', {
                total: results.total,
                successful: results.successful,
                failed: results.failed,
                averageScore: results.summary.averageScore,
                assessor
            });

        } catch (error) {
            logger.error('批量计算设备健康度失败', {
                error: error.message,
                user: req.user.username
            });

            res.status(500).json({
                success: false,
                message: '批量计算设备健康度失败: ' + error.message
            });
        }
    }

    /**
     * 根据分数获取健康度等级
     * @param {number} score - 健康度分数
     * @returns {string} 健康度等级
     */
    getHealthLevel(score) {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '一般';
        if (score >= 60) return '较差';
        return '危险';
    }

    /**
     * 计算固定的设备健康度
     * 基于设备真实数据，确保每次计算结果一致
     */
    calculateFixedHealthScore(equipment) {
        // 基础健康度：根据设备状态
        let healthScore = 85; // 默认健康度

        // 根据设备状态调整健康度（固定值）
        if (equipment.status === 'fault') {
            healthScore = 30;
        } else if (equipment.status === 'maintenance') {
            healthScore = 50;
        } else if (equipment.status === 'inactive') {
            healthScore = 40;
        } else if (equipment.manufacture_date) {
            // 根据设备年龄计算健康度（固定算法）
            const now = new Date();
            const manufactureDate = new Date(equipment.manufacture_date);
            const ageInYears = (now - manufactureDate) / (1000 * 60 * 60 * 24 * 365);

            // 设备年龄越大，健康度越低，但不低于60分
            healthScore = Math.max(60, 100 - Math.floor(ageInYears * 3));
        }

        // 基于设备ID生成固定的微调值（确保每次计算结果一致）
        const idHash = this.generateHashFromId(equipment.id);
        const adjustment = (idHash % 11) - 5; // -5到+5的固定调整

        return Math.max(30, Math.min(100, healthScore + adjustment));
    }

    /**
     * 基于设备ID生成固定的哈希值
     * 确保相同ID每次生成相同的数值
     */
    generateHashFromId(id) {
        let hash = 0;
        for (let i = 0; i < id.length; i++) {
            const char = id.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }
}

module.exports = new EquipmentController();
