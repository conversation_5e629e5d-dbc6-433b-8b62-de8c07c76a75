/**
 * PostgreSQL设备产能数据访问层
 * 替换SQLite的capacityRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const { EquipmentCapabilityModel, OperatorSkillModel, EquipmentOperatorModel } = require('../models/capacityModel');
const logger = require('../utils/logger');

class PostgresCapacityRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL设备产能数据访问层初始化完成');
        }
    }

    /**
     * 创建设备产能记录
     */
    async createCapability(capability) {
        try {
            const dbData = capability.toDatabase();

            const result = await this.query(`
                INSERT INTO equipment_capabilities (
                    id, equipment_id, product_id, process_id, capacity_per_hour,
                    efficiency_factor, setup_time, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            `, [
                dbData.id, dbData.equipment_id, dbData.product_id, dbData.process_id,
                dbData.capacity_per_hour, dbData.efficiency_factor, dbData.setup_time,
                dbData.created_at
            ]);

            logger.info('设备产能记录创建成功', { capabilityId: capability.id });
            return EquipmentCapabilityModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('设备产能记录创建失败', { error: error.message, capabilityId: capability.id });
            throw error;
        }
    }

    /**
     * 获取设备的产品产能
     */
    async getEquipmentCapability(equipmentId, productId) {
        try {
            const row = await this.findOne(`
                SELECT * FROM equipment_capabilities 
                WHERE equipment_id = $1 AND product_id = $2
            `, [equipmentId, productId]);
            
            return row ? EquipmentCapabilityModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('获取设备产能失败', { error: error.message, equipmentId, productId });
            throw error;
        }
    }

    /**
     * 获取设备的所有产能记录
     */
    async getEquipmentCapabilities(equipmentId) {
        try {
            const rows = await this.findMany(`
                SELECT ec.*, p.name as product_name, p.code as product_code
                FROM equipment_capabilities ec
                LEFT JOIN products p ON ec.product_id = p.id
                WHERE ec.equipment_id = $1
                ORDER BY ec.created_at DESC
            `, [equipmentId]);
            
            return rows.map(row => {
                const capability = EquipmentCapabilityModel.fromDatabase(row);
                capability.productName = row.product_name;
                capability.productCode = row.product_code;
                return capability;
            });
        } catch (error) {
            logger.error('获取设备产能列表失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 获取产品的所有设备产能
     */
    async getProductCapabilities(productId) {
        try {
            const rows = await this.findMany(`
                SELECT ec.*, e.name as equipment_name, e.code as equipment_code
                FROM equipment_capabilities ec
                LEFT JOIN equipment e ON ec.equipment_id = e.id
                WHERE ec.product_id = $1
                ORDER BY ec.capacity_per_hour DESC
            `, [productId]);
            
            return rows.map(row => {
                const capability = EquipmentCapabilityModel.fromDatabase(row);
                capability.equipmentName = row.equipment_name;
                capability.equipmentCode = row.equipment_code;
                return capability;
            });
        } catch (error) {
            logger.error('获取产品设备产能失败', { error: error.message, productId });
            throw error;
        }
    }

    /**
     * 更新设备产能
     */
    async updateCapability(id, capability) {
        try {
            const dbData = capability.toDatabase();

            const result = await this.query(`
                UPDATE equipment_capabilities SET
                    capacity_per_hour = $1, efficiency_factor = $2, setup_time = $3
                WHERE id = $4
                RETURNING *
            `, [
                dbData.capacity_per_hour, dbData.efficiency_factor, 
                dbData.setup_time, id
            ]);

            if (result.rows.length > 0) {
                logger.info('设备产能更新成功', { capabilityId: id });
                return EquipmentCapabilityModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('设备产能更新失败', { error: error.message, capabilityId: id });
            throw error;
        }
    }

    /**
     * 删除设备产能记录
     */
    async deleteCapability(id) {
        try {
            const result = await this.query('DELETE FROM equipment_capabilities WHERE id = $1', [id]);
            const success = result.rowCount > 0;
            
            if (success) {
                logger.info('设备产能记录删除成功', { capabilityId: id });
            }
            return success;
        } catch (error) {
            logger.error('设备产能记录删除失败', { error: error.message, capabilityId: id });
            throw error;
        }
    }

    // 操作员技能相关方法

    /**
     * 创建操作员技能记录
     */
    async createOperatorSkill(skill) {
        try {
            const dbData = skill.toDatabase();

            const result = await this.query(`
                INSERT INTO operator_skills (
                    id, operator_id, skill_type, skill_level, certification_date,
                    expiry_date, notes, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            `, [
                dbData.id, dbData.operator_id, dbData.skill_type, dbData.skill_level,
                dbData.certification_date, dbData.expiry_date, dbData.notes,
                dbData.created_at
            ]);

            logger.info('操作员技能记录创建成功', { skillId: skill.id });
            return OperatorSkillModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('操作员技能记录创建失败', { error: error.message, skillId: skill.id });
            throw error;
        }
    }

    /**
     * 获取操作员的技能列表
     */
    async getOperatorSkills(operatorId) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM operator_skills 
                WHERE operator_id = $1 
                ORDER BY created_at DESC
            `, [operatorId]);
            
            return rows.map(row => OperatorSkillModel.fromDatabase(row));
        } catch (error) {
            logger.error('获取操作员技能失败', { error: error.message, operatorId });
            throw error;
        }
    }

    /**
     * 获取具有特定技能的操作员
     */
    async getOperatorsBySkill(skillType, minLevel = 1) {
        try {
            const rows = await this.findMany(`
                SELECT os.*, u.username, u.name as operator_name
                FROM operator_skills os
                LEFT JOIN users u ON os.operator_id = u.id
                WHERE os.skill_type = $1 AND os.skill_level >= $2
                ORDER BY os.skill_level DESC
            `, [skillType, minLevel]);
            
            return rows.map(row => {
                const skill = OperatorSkillModel.fromDatabase(row);
                skill.operatorName = row.operator_name || row.username;
                return skill;
            });
        } catch (error) {
            logger.error('根据技能获取操作员失败', { error: error.message, skillType, minLevel });
            throw error;
        }
    }

    // 设备操作员关联相关方法

    /**
     * 创建设备操作员关联
     */
    async createEquipmentOperator(equipmentOperator) {
        try {
            const dbData = equipmentOperator.toDatabase();

            const result = await this.query(`
                INSERT INTO equipment_operators (
                    id, equipment_id, operator_id, proficiency_level, 
                    certification_date, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING *
            `, [
                dbData.id, dbData.equipment_id, dbData.operator_id,
                dbData.proficiency_level, dbData.certification_date,
                dbData.created_at
            ]);

            logger.info('设备操作员关联创建成功', { relationId: equipmentOperator.id });
            return EquipmentOperatorModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('设备操作员关联创建失败', { error: error.message, relationId: equipmentOperator.id });
            throw error;
        }
    }

    /**
     * 获取设备的操作员列表
     */
    async getEquipmentOperators(equipmentId) {
        try {
            const rows = await this.findMany(`
                SELECT eo.*, u.username, u.name as operator_name
                FROM equipment_operators eo
                LEFT JOIN users u ON eo.operator_id = u.id
                WHERE eo.equipment_id = $1
                ORDER BY eo.proficiency_level DESC
            `, [equipmentId]);
            
            return rows.map(row => {
                const relation = EquipmentOperatorModel.fromDatabase(row);
                relation.operatorName = row.operator_name || row.username;
                return relation;
            });
        } catch (error) {
            logger.error('获取设备操作员失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 获取操作员可操作的设备列表
     */
    async getOperatorEquipments(operatorId) {
        try {
            const rows = await this.findMany(`
                SELECT eo.*, e.name as equipment_name, e.code as equipment_code
                FROM equipment_operators eo
                LEFT JOIN equipment e ON eo.equipment_id = e.id
                WHERE eo.operator_id = $1
                ORDER BY eo.proficiency_level DESC
            `, [operatorId]);
            
            return rows.map(row => {
                const relation = EquipmentOperatorModel.fromDatabase(row);
                relation.equipmentName = row.equipment_name;
                relation.equipmentCode = row.equipment_code;
                return relation;
            });
        } catch (error) {
            logger.error('获取操作员设备失败', { error: error.message, operatorId });
            throw error;
        }
    }

    /**
     * 获取产能统计信息
     */
    async getCapacityStats() {
        try {
            const stats = await this.findOne(`
                SELECT
                    COUNT(DISTINCT equipment_id) as equipment_count,
                    COUNT(DISTINCT product_id) as product_count,
                    AVG(capacity_per_hour) as avg_capacity,
                    AVG(efficiency_factor) as avg_efficiency
                FROM equipment_capabilities
            `);

            return {
                equipmentCount: parseInt(stats.equipment_count) || 0,
                productCount: parseInt(stats.product_count) || 0,
                avgCapacity: parseFloat(stats.avg_capacity) || 0,
                avgEfficiency: parseFloat(stats.avg_efficiency) || 0
            };
        } catch (error) {
            logger.error('获取产能统计信息失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 删除设备的所有产能记录
     */
    async deleteCapabilitiesByEquipment(equipmentId) {
        try {
            const result = await this.query('DELETE FROM equipment_capabilities WHERE equipment_id = $1', [equipmentId]);
            logger.info('设备产能记录删除成功', { equipmentId, deletedCount: result.rowCount });
            return result.rowCount;
        } catch (error) {
            logger.error('删除设备产能记录失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 删除产品的所有产能记录
     */
    async deleteCapabilitiesByProduct(productId) {
        try {
            const result = await this.query('DELETE FROM equipment_capabilities WHERE product_id = $1', [productId]);
            logger.info('产品产能记录删除成功', { productId, deletedCount: result.rowCount });
            return result.rowCount;
        } catch (error) {
            logger.error('删除产品产能记录失败', { error: error.message, productId });
            throw error;
        }
    }
}

module.exports = PostgresCapacityRepository;
