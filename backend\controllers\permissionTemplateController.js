/**
 * 权限模板控制器
 * 处理权限模板相关的请求
 */

const permissionTemplateService = require('../services/permissionTemplateService');
const logger = require('../utils/logger');

/**
 * 获取所有权限模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getAllTemplates(req, res) {
    try {
        // 检查权限 - 只有admin角色才能访问权限模板
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限访问权限模板，只有系统管理员才能访问'
            });
        }

        const templates = await permissionTemplateService.getAllTemplates();

        res.json({
            success: true,
            templates
        });
    } catch (error) {
        logger.error('获取权限模板列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取权限模板列表失败: ' + error.message
        });
    }
}

/**
 * 根据ID获取权限模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getTemplateById(req, res) {
    try {
        // 检查权限 - 只有admin角色才能访问权限模板
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限访问权限模板，只有系统管理员才能访问'
            });
        }

        const { id } = req.params;
        const template = await permissionTemplateService.getTemplateById(id);

        if (!template) {
            return res.status(404).json({
                success: false,
                message: '权限模板不存在'
            });
        }

        res.json({
            success: true,
            template
        });
    } catch (error) {
        logger.error('获取权限模板失败:', error);
        res.status(500).json({
            success: false,
            message: '获取权限模板失败: ' + error.message
        });
    }
}

/**
 * 创建权限模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function createTemplate(req, res) {
    try {
        // 检查权限 - 只有admin角色才能创建权限模板
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限创建权限模板，只有系统管理员才能创建'
            });
        }

        const { name, description, permissions } = req.body;

        // 验证必填字段
        if (!name) {
            return res.status(400).json({
                success: false,
                message: '模板名称不能为空'
            });
        }

        if (!Array.isArray(permissions)) {
            return res.status(400).json({
                success: false,
                message: '权限必须是数组'
            });
        }

        // 检查模板名称是否已存在
        if (await permissionTemplateService.isTemplateNameExists(name)) {
            return res.status(400).json({
                success: false,
                message: '模板名称已存在'
            });
        }

        const template = await permissionTemplateService.createTemplate({
            name,
            description,
            permissions
        });

        if (!template) {
            return res.status(500).json({
                success: false,
                message: '创建权限模板失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('权限模板管理', '创建模板', {
            templateId: template.id,
            templateName: template.name,
            permissions: template.permissions
        }, req.user);

        res.status(201).json({
            success: true,
            message: '权限模板创建成功',
            template
        });
    } catch (error) {
        logger.error('创建权限模板失败:', error);
        res.status(500).json({
            success: false,
            message: '创建权限模板失败: ' + error.message
        });
    }
}

/**
 * 更新权限模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function updateTemplate(req, res) {
    try {
        // 检查权限 - 只有admin角色才能更新权限模板
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限更新权限模板，只有系统管理员才能更新'
            });
        }

        const { id } = req.params;
        const { name, description, permissions } = req.body;

        // 验证模板是否存在
        const existingTemplate = await permissionTemplateService.getTemplateById(id);
        if (!existingTemplate) {
            return res.status(404).json({
                success: false,
                message: '权限模板不存在'
            });
        }

        // 验证必填字段
        if (name !== undefined && !name) {
            return res.status(400).json({
                success: false,
                message: '模板名称不能为空'
            });
        }

        if (permissions !== undefined && !Array.isArray(permissions)) {
            return res.status(400).json({
                success: false,
                message: '权限必须是数组'
            });
        }

        // 检查模板名称是否已存在（排除当前模板）
        if (name && await permissionTemplateService.isTemplateNameExists(name, id)) {
            return res.status(400).json({
                success: false,
                message: '模板名称已存在'
            });
        }

        const updatedTemplate = await permissionTemplateService.updateTemplate(id, {
            name,
            description,
            permissions
        });

        if (!updatedTemplate) {
            return res.status(500).json({
                success: false,
                message: '更新权限模板失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('权限模板管理', '更新模板', {
            templateId: updatedTemplate.id,
            templateName: updatedTemplate.name,
            permissions: updatedTemplate.permissions
        }, req.user);

        res.json({
            success: true,
            message: '权限模板更新成功',
            template: updatedTemplate
        });
    } catch (error) {
        logger.error('更新权限模板失败:', error);
        res.status(500).json({
            success: false,
            message: '更新权限模板失败: ' + error.message
        });
    }
}

/**
 * 删除权限模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteTemplate(req, res) {
    try {
        // 检查权限 - 只有admin角色才能删除权限模板
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限删除权限模板，只有系统管理员才能删除'
            });
        }

        const { id } = req.params;

        // 验证模板是否存在
        const existingTemplate = await permissionTemplateService.getTemplateById(id);
        if (!existingTemplate) {
            return res.status(404).json({
                success: false,
                message: '权限模板不存在'
            });
        }

        // 检查是否是预设模板
        if (existingTemplate.isBuiltIn) {
            return res.status(400).json({
                success: false,
                message: '预设模板不能删除，只能编辑'
            });
        }

        const success = await permissionTemplateService.deleteTemplate(id);

        if (!success) {
            return res.status(500).json({
                success: false,
                message: '删除权限模板失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('权限模板管理', '删除模板', {
            templateId: id,
            templateName: existingTemplate.name
        }, req.user);

        res.json({
            success: true,
            message: '权限模板删除成功'
        });
    } catch (error) {
        logger.error('删除权限模板失败:', error);
        res.status(500).json({
            success: false,
            message: '删除权限模板失败: ' + error.message
        });
    }
}

module.exports = {
    getAllTemplates,
    getTemplateById,
    createTemplate,
    updateTemplate,
    deleteTemplate
};
