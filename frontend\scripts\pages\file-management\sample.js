/**
 * 样品寄送通知单页面
 * 处理样品寄送通知单的填写功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import SampleForm from '../../../components/file-management/SampleForm.js';

createStandardApp({
    components: {
        Sidebar,
        SampleForm
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_upload'],
    onUserLoaded: async (user) => {
        console.log('样品寄送通知单页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
