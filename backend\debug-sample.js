const { databaseAdapter } = require('./database/databaseAdapter');
const fs = require('fs');
const path = require('path');

async function debugSampleAttachments() {
    try {
        const sampleFormRepository = databaseAdapter.getSampleFormRepository();
        
        // 获取样品单详细信息
        const sampleForm = await sampleFormRepository.getSampleFormById('abfef608-4dd3-4e1a-b762-c51c41830025');
        
        console.log('=== 样品单基本信息 ===');
        console.log('ID:', sampleForm.id);
        console.log('编号:', sampleForm.sampleNumber);
        console.log('公司:', sampleForm.companyName);
        
        console.log('\n=== 附件信息 ===');
        console.log('附件数量:', sampleForm.attachments ? sampleForm.attachments.length : 0);
        
        if (sampleForm.attachments && sampleForm.attachments.length > 0) {
            sampleForm.attachments.forEach((attachment, index) => {
                console.log(`\n附件 ${index + 1}:`);
                console.log('  ID:', attachment.id);
                console.log('  文件类型:', attachment.file_type);
                console.log('  原始文件名:', attachment.original_filename);
                console.log('  MIME类型:', attachment.mime_type);
                console.log('  文件大小:', attachment.file_size);
                console.log('  存储路径:', attachment.file_path);
                
                // 检查文件是否存在
                let filePath = path.resolve(attachment.file_path);
                let fileExists = fs.existsSync(filePath);
                
                console.log('  原始路径存在:', fileExists);
                
                if (!fileExists) {
                    // 尝试修正路径
                    const backendIndex = attachment.file_path.indexOf('backend');
                    if (backendIndex !== -1) {
                        const relativePath = attachment.file_path.substring(backendIndex + 8);
                        const correctedPath = path.join(__dirname, '..', relativePath);
                        fileExists = fs.existsSync(correctedPath);
                        console.log('  修正路径:', correctedPath);
                        console.log('  修正路径存在:', fileExists);
                        
                        if (fileExists) {
                            const stats = fs.statSync(correctedPath);
                            console.log('  文件大小:', stats.size, 'bytes');
                        }
                    }
                }
            });
        } else {
            console.log('没有找到附件');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('调试失败:', error);
        process.exit(1);
    }
}

debugSampleAttachments();
