/**
 * PostgreSQL生产排程数据访问层
 * 替换SQLite的scheduleRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const { ScheduleModel } = require('../models/scheduleModel');
const logger = require('../utils/logger');

class PostgresScheduleRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL排程数据访问层初始化完成');
        }
    }

    /**
     * 创建排程
     */
    async create(schedule) {
        try {
            const dbData = schedule.toDatabase();

            const result = await this.query(`
                INSERT INTO schedules (
                    id, title, product_id, product_name, quantity,
                    start_time, end_time, status, priority,
                    assigned_equipment, assigned_personnel, required_materials,
                    progress, notes, created_by, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                RETURNING *
            `, [
                dbData.id, dbData.title, dbData.product_id, dbData.product_name,
                dbData.quantity, dbData.start_time, dbData.end_time,
                dbData.status, dbData.priority, dbData.assigned_equipment,
                dbData.assigned_personnel, dbData.required_materials,
                dbData.progress, dbData.notes, dbData.created_by,
                dbData.created_at, dbData.updated_at
            ]);

            logger.info('排程创建成功', { scheduleId: schedule.id });
            return ScheduleModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('排程创建失败', { error: error.message, scheduleId: schedule.id });
            throw error;
        }
    }

    /**
     * 根据ID获取排程
     */
    async findById(id) {
        try {
            const row = await this.findOne('SELECT * FROM schedules WHERE id = $1', [id]);
            return row ? ScheduleModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('获取排程失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 获取排程列表
     */
    async findAll(options = {}) {
        try {
            const { page = 1, limit = 20, status, priority, startDate, endDate } = options;
            const offset = (page - 1) * limit;

            let whereConditions = [];
            let params = [];
            let paramIndex = 1;

            if (status) {
                whereConditions.push(`status = $${paramIndex++}`);
                params.push(status);
            }

            if (priority) {
                whereConditions.push(`priority = $${paramIndex++}`);
                params.push(priority);
            }

            if (startDate) {
                whereConditions.push(`start_time >= $${paramIndex++}`);
                params.push(startDate);
            }

            if (endDate) {
                whereConditions.push(`end_time <= $${paramIndex++}`);
                params.push(endDate);
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            // 获取总数
            const countQuery = `SELECT COUNT(*) as total FROM schedules ${whereClause}`;
            const countResult = await this.findOne(countQuery, params);
            const total = parseInt(countResult.total) || 0;

            // 获取数据
            const dataQuery = `
                SELECT * FROM schedules 
                ${whereClause}
                ORDER BY created_at DESC 
                LIMIT $${paramIndex++} OFFSET $${paramIndex++}
            `;
            params.push(limit, offset);

            const rows = await this.findMany(dataQuery, params);
            const schedules = rows.map(row => ScheduleModel.fromDatabase(row));

            return {
                schedules,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            logger.error('获取排程列表失败', { error: error.message, options });
            throw error;
        }
    }

    /**
     * 更新排程
     */
    async update(id, schedule) {
        try {
            const dbData = schedule.toDatabase();

            const result = await this.query(`
                UPDATE schedules SET
                    title = $1, product_id = $2, product_name = $3, quantity = $4,
                    start_time = $5, end_time = $6, status = $7, priority = $8,
                    assigned_equipment = $9, assigned_personnel = $10, required_materials = $11,
                    progress = $12, notes = $13, updated_at = $14
                WHERE id = $15
                RETURNING *
            `, [
                dbData.title, dbData.product_id, dbData.product_name, dbData.quantity,
                dbData.start_time, dbData.end_time, dbData.status, dbData.priority,
                dbData.assigned_equipment, dbData.assigned_personnel, dbData.required_materials,
                dbData.progress, dbData.notes, dbData.updated_at, id
            ]);

            if (result.rows.length > 0) {
                logger.info('排程更新成功', { scheduleId: id });
                return ScheduleModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('排程更新失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 删除排程
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM schedules WHERE id = $1', [id]);
            const success = result.rowCount > 0;
            
            if (success) {
                logger.info('排程删除成功', { scheduleId: id });
            }
            return success;
        } catch (error) {
            logger.error('排程删除失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 根据状态获取排程
     */
    async findByStatus(status) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM schedules 
                WHERE status = $1 
                ORDER BY priority DESC, start_time ASC
            `, [status]);
            return rows.map(row => ScheduleModel.fromDatabase(row));
        } catch (error) {
            logger.error('根据状态获取排程失败', { error: error.message, status });
            throw error;
        }
    }

    /**
     * 根据日期范围获取排程
     */
    async findByDateRange(startDate, endDate) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM schedules 
                WHERE start_time >= $1 AND end_time <= $2
                ORDER BY start_time ASC
            `, [startDate, endDate]);
            return rows.map(row => ScheduleModel.fromDatabase(row));
        } catch (error) {
            logger.error('根据日期范围获取排程失败', { error: error.message, startDate, endDate });
            throw error;
        }
    }

    /**
     * 根据设备获取排程
     */
    async findByEquipment(equipmentId) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM schedules 
                WHERE assigned_equipment LIKE $1
                ORDER BY start_time ASC
            `, [`%${equipmentId}%`]);
            return rows.map(row => ScheduleModel.fromDatabase(row));
        } catch (error) {
            logger.error('根据设备获取排程失败', { error: error.message, equipmentId });
            throw error;
        }
    }

    /**
     * 更新排程状态
     */
    async updateStatus(id, status) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE schedules SET status = $1, updated_at = $2
                WHERE id = $3
                RETURNING *
            `, [status, now, id]);

            if (result.rows.length > 0) {
                logger.info('排程状态更新成功', { scheduleId: id, status });
                return ScheduleModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('排程状态更新失败', { error: error.message, scheduleId: id, status });
            throw error;
        }
    }

    /**
     * 更新排程进度
     */
    async updateProgress(id, progress) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE schedules SET progress = $1, updated_at = $2
                WHERE id = $3
                RETURNING *
            `, [progress, now, id]);

            if (result.rows.length > 0) {
                logger.info('排程进度更新成功', { scheduleId: id, progress });
                return ScheduleModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('排程进度更新失败', { error: error.message, scheduleId: id, progress });
            throw error;
        }
    }

    /**
     * 获取排程统计信息
     */
    async getScheduleStats() {
        try {
            const stats = await this.findOne(`
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    AVG(progress) as avg_progress
                FROM schedules
            `);
            
            return {
                totalCount: parseInt(stats.total_count) || 0,
                pendingCount: parseInt(stats.pending_count) || 0,
                inProgressCount: parseInt(stats.in_progress_count) || 0,
                completedCount: parseInt(stats.completed_count) || 0,
                avgProgress: parseFloat(stats.avg_progress) || 0
            };
        } catch (error) {
            logger.error('获取排程统计信息失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 批量更新排程状态
     */
    async batchUpdateStatus(ids, status) {
        try {
            const now = new Date().toISOString();
            const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
            
            const result = await this.query(`
                UPDATE schedules 
                SET status = $${ids.length + 1}, updated_at = $${ids.length + 2}
                WHERE id IN (${placeholders})
            `, [...ids, status, now]);

            logger.info('批量更新排程状态成功', { count: result.rowCount, status });
            return result.rowCount;
        } catch (error) {
            logger.error('批量更新排程状态失败', { error: error.message, ids, status });
            throw error;
        }
    }
}

module.exports = PostgresScheduleRepository;
