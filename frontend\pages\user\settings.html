<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/user/common.css">
    <link rel="stylesheet" href="/assets/css/user/settings.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 侧边导航栏 -->
        <sidebar
            :user="currentUser">
        </sidebar>

        <div class="flex-1 ml-72 p-4">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">个人设置</h2>

                <!-- 设置选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeTab = 'password'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'tab-active': activeTab === 'password'}">
                            修改密码
                        </button>
                        <button
                            @click="activeTab = 'signature'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'tab-active': activeTab === 'signature'}">
                            电子签名
                        </button>
                    </div>
                </div>

                <!-- 密码修改表单 -->
                <div v-if="activeTab === 'password'">
                    <password-change-form
                        :user="currentUser"
                        @updated="onUserUpdated">
                    </password-change-form>
                </div>

                <!-- 电子签名表单 -->
                <div v-if="activeTab === 'signature'">
                    <signature-upload-form
                        :user="currentUser"
                        @updated="onUserUpdated">
                    </signature-upload-form>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/user/settings.js"></script>
</body>
</html>
