/**
 * PostgreSQL维修保养记录数据访问层
 * 替换SQLite的maintenanceRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const { MaintenanceModel } = require('../models/maintenanceModel');
const logger = require('../utils/logger');

class PostgresMaintenanceRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL维修保养记录数据访问层初始化完成');
        }
    }

    /**
     * 获取所有维修记录
     */
    async findAll() {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ORDER BY m.created_at DESC
            `);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error('获取维修记录列表失败:', error);
            throw error;
        }
    }

    /**
     * 分页获取维修记录
     */
    async findAllWithPagination(options = {}) {
        try {
            const { page = 1, limit = 10, ...filters } = options;
            const offset = (parseInt(page) - 1) * parseInt(limit);

            // 构建WHERE条件
            let whereConditions = [];
            let params = [];
            let paramIndex = 1;

            if (filters.equipmentId) {
                whereConditions.push(`m.equipment_id = $${paramIndex}`);
                params.push(filters.equipmentId);
                paramIndex++;
            }

            if (filters.type) {
                whereConditions.push(`m.maintenance_type = $${paramIndex}`);
                params.push(filters.type);
                paramIndex++;
            }

            if (filters.status) {
                whereConditions.push(`m.status = $${paramIndex}`);
                params.push(filters.status);
                paramIndex++;
            }

            if (filters.startDate && filters.endDate) {
                whereConditions.push(`m.maintenance_date >= $${paramIndex} AND m.maintenance_date <= $${paramIndex + 1}`);
                params.push(filters.startDate, filters.endDate);
                paramIndex += 2;
            }

            if (filters.search) {
                whereConditions.push(`(m.description ILIKE $${paramIndex} OR m.technician ILIKE $${paramIndex} OR e.name ILIKE $${paramIndex})`);
                params.push(`%${filters.search}%`);
                paramIndex++;
            }

            if (filters.technician) {
                whereConditions.push(`m.technician = $${paramIndex}`);
                params.push(filters.technician);
                paramIndex++;
            }

            if (filters.area) {
                whereConditions.push(`e.area = $${paramIndex}`);
                params.push(filters.area);
                paramIndex++;
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

            // 获取总数
            const countQuery = `
                SELECT COUNT(*) as total
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ${whereClause}
            `;
            const countResult = await this.findOne(countQuery, params);
            const total = parseInt(countResult.total) || 0;

            // 获取分页数据
            const dataQuery = `
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                ${whereClause}
                ORDER BY m.created_at DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;
            params.push(limit, offset);

            const records = await this.findMany(dataQuery, params);

            return {
                data: records.map(record => this.enrichRecord(record)),
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: total,
                    totalPages: Math.ceil(total / parseInt(limit))
                }
            };
        } catch (error) {
            logger.error('分页获取维修记录失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找维修记录
     */
    async findById(id) {
        try {
            const record = await this.findOne(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.id = $1
            `, [id]);
            return record ? this.enrichRecord(record) : null;
        } catch (error) {
            logger.error(`根据ID查找维修记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 创建维修记录
     */
    async create(maintenanceData) {
        try {
            const now = new Date().toISOString();
            const id = maintenanceData.id || this.generateId();

            const result = await this.query(`
                INSERT INTO equipment_maintenance (
                    id, equipment_id, maintenance_type, severity_level, description, maintenance_date,
                    start_time, end_time, cost, technician, status, notes, result, reviewer, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                RETURNING *
            `, [
                id, maintenanceData.equipment_id, maintenanceData.type,
                maintenanceData.severity_level, maintenanceData.description,
                maintenanceData.maintenance_date, maintenanceData.start_time,
                maintenanceData.end_time, maintenanceData.cost || 0,
                maintenanceData.technician, maintenanceData.status || 'pending',
                maintenanceData.notes || '', maintenanceData.result || '',
                maintenanceData.reviewer || '', now, now
            ]);

            return this.enrichRecord(result.rows[0]);
        } catch (error) {
            logger.error('创建维修记录失败:', error);
            throw error;
        }
    }

    /**
     * 更新维修记录
     */
    async update(id, maintenanceData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE equipment_maintenance SET
                    equipment_id = $1, maintenance_type = $2, severity_level = $3, description = $4, maintenance_date = $5,
                    start_time = $6, end_time = $7, cost = $8, technician = $9, status = $10, notes = $11,
                    result = $12, reviewer = $13, updated_at = $14
                WHERE id = $15
                RETURNING *
            `, [
                maintenanceData.equipment_id, maintenanceData.type,
                maintenanceData.severity_level, maintenanceData.description,
                maintenanceData.maintenance_date, maintenanceData.start_time,
                maintenanceData.end_time, maintenanceData.cost || 0,
                maintenanceData.technician, maintenanceData.status,
                maintenanceData.notes || '', maintenanceData.result || '',
                maintenanceData.reviewer || '', now, id
            ]);

            return result.rows.length > 0 ? this.enrichRecord(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新维修记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除维修记录
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM equipment_maintenance WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除维修记录失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据设备ID获取维修记录
     */
    async findByEquipmentId(equipmentId) {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.equipment_id = $1
                ORDER BY m.created_at DESC
            `, [equipmentId]);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error(`根据设备ID获取维修记录失败 (${equipmentId}):`, error);
            throw error;
        }
    }

    /**
     * 根据状态获取维修记录
     */
    async findByStatus(status) {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.status = $1
                ORDER BY m.created_at DESC
            `, [status]);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error(`根据状态获取维修记录失败 (${status}):`, error);
            throw error;
        }
    }

    /**
     * 根据类型获取维修记录
     */
    async findByType(type) {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.maintenance_type = $1
                ORDER BY m.created_at DESC
            `, [type]);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error(`根据类型获取维修记录失败 (${type}):`, error);
            throw error;
        }
    }

    /**
     * 根据日期范围获取维修记录
     */
    async findByDateRange(startDate, endDate) {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE m.maintenance_date >= $1 AND m.maintenance_date <= $2
                ORDER BY m.maintenance_date DESC
            `, [startDate, endDate]);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error(`根据日期范围获取维修记录失败:`, error);
            throw error;
        }
    }

    /**
     * 根据日期获取维修记录
     */
    async findByDate(date) {
        try {
            const records = await this.findMany(`
                SELECT m.*, e.code as equipment_code, e.name as equipment_name, e.area as equipment_area
                FROM equipment_maintenance m
                LEFT JOIN equipment e ON m.equipment_id = e.id
                WHERE DATE(m.maintenance_date) = $1
                ORDER BY m.created_at DESC
            `, [date]);
            return records.map(record => this.enrichRecord(record));
        } catch (error) {
            logger.error(`根据日期获取维修记录失败 (${date}):`, error);
            throw error;
        }
    }

    /**
     * 获取维修统计信息
     */
    async getStatistics() {
        try {
            const stats = await this.findOne(`
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN maintenance_type = 'maintenance' THEN 1 END) as maintenance_count,
                    COUNT(CASE WHEN maintenance_type = 'repair' THEN 1 END) as repair_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    AVG(cost) as avg_cost,
                    SUM(cost) as total_cost
                FROM equipment_maintenance
            `);
            
            return {
                total: parseInt(stats.total) || 0,
                maintenanceCount: parseInt(stats.maintenance_count) || 0,
                repairCount: parseInt(stats.repair_count) || 0,
                pendingCount: parseInt(stats.pending_count) || 0,
                inProgressCount: parseInt(stats.in_progress_count) || 0,
                completedCount: parseInt(stats.completed_count) || 0,
                avgCost: parseFloat(stats.avg_cost) || 0,
                totalCost: parseFloat(stats.total_cost) || 0
            };
        } catch (error) {
            logger.error('获取维修统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备维修统计
     */
    async getEquipmentMaintenanceStats(equipmentId) {
        try {
            const stats = await this.findOne(`
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN maintenance_type = 'maintenance' THEN 1 END) as maintenance_count,
                    COUNT(CASE WHEN maintenance_type = 'repair' THEN 1 END) as repair_count,
                    SUM(cost) as total_cost,
                    MAX(maintenance_date) as last_maintenance
                FROM equipment_maintenance
                WHERE equipment_id = $1
            `, [equipmentId]);
            
            return {
                total: parseInt(stats.total) || 0,
                maintenanceCount: parseInt(stats.maintenance_count) || 0,
                repairCount: parseInt(stats.repair_count) || 0,
                totalCost: parseFloat(stats.total_cost) || 0,
                lastMaintenance: stats.last_maintenance
            };
        } catch (error) {
            logger.error(`获取设备维修统计失败 (${equipmentId}):`, error);
            throw error;
        }
    }

    /**
     * 更新维修状态
     */
    async updateStatus(id, status) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                UPDATE equipment_maintenance 
                SET status = $1, updated_at = $2
                WHERE id = $3
                RETURNING *
            `, [status, now, id]);

            return result.rows.length > 0 ? this.enrichRecord(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新维修状态失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 批量更新维修状态
     */
    async batchUpdateStatus(ids, status) {
        try {
            const now = new Date().toISOString();
            const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
            
            const result = await this.query(`
                UPDATE equipment_maintenance 
                SET status = $${ids.length + 1}, updated_at = $${ids.length + 2}
                WHERE id IN (${placeholders})
            `, [...ids, status, now]);

            return result.rowCount;
        } catch (error) {
            logger.error('批量更新维修状态失败:', error);
            throw error;
        }
    }

    /**
     * 获取设备维修记录统计
     */
    async getMaintenanceStatsByEquipment(equipmentId) {
        try {
            const stats = await this.findMany(`
                SELECT
                    maintenance_type as type,
                    severity_level,
                    COUNT(*) as count,
                    MAX(created_at) as latest_date
                FROM equipment_maintenance
                WHERE equipment_id = $1
                GROUP BY maintenance_type, severity_level
                ORDER BY maintenance_type, severity_level
            `, [equipmentId]);

            return stats.map(stat => ({
                type: stat.type,
                severity_level: stat.severity_level,
                count: parseInt(stat.count),
                latestDate: stat.latest_date
            }));
        } catch (error) {
            logger.error('获取设备维修统计失败:', error);
            throw error;
        }
    }

    /**
     * 丰富记录信息
     */
    enrichRecord(record) {
        if (!record) return null;

        return {
            ...record,
            type: record.maintenance_type, // 映射字段名：数据库字段 -> 前端字段
            cost: parseFloat(record.cost) || 0,
            equipment: {
                code: record.equipment_code,
                name: record.equipment_name,
                area: record.equipment_area
            }
        };
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
}

module.exports = PostgresMaintenanceRepository;
