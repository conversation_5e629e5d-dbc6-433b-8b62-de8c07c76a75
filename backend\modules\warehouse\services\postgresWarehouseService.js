/**
 * PostgreSQL仓库管理服务
 * 专用于PostgreSQL数据库的仓库管理服务
 */

require('dotenv').config();
const logger = require('../../../utils/logger');

class PostgresWarehouseService {
    constructor() {
        this.initializeServices();
    }

    /**
     * 初始化服务
     */
    initializeServices() {
        try {
            // PostgreSQL服务
            const PostgresWarehouseRepository = require('../repositories/postgresWarehouseRepository');
            this.repository = new PostgresWarehouseRepository();
            logger.info('PostgreSQL仓库管理服务初始化完成');
        } catch (error) {
            logger.error('仓库管理服务初始化失败:', error);
            throw error;
        }
    }

    // ==================== 物料管理方法 ====================

    /**
     * 获取物料列表
     */
    async getMaterials(filters = {}) {
        try {
            return await this.repository.getAllMaterials(filters);
        } catch (error) {
            logger.error('获取物料列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取物料
     */
    async getMaterialById(id) {
        try {
            return await this.repository.getMaterialById(id);
        } catch (error) {
            logger.error(`根据ID获取物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 创建物料
     */
    async createMaterial(materialData, operatorId) {
        try {
            // 生成ID
            materialData.id = this.repository.generateId();
            return await this.repository.createMaterial(materialData);
        } catch (error) {
            logger.error('创建物料失败:', error);
            throw error;
        }
    }

    /**
     * 更新物料
     */
    async updateMaterial(id, materialData, operatorId) {
        try {
            return await this.repository.updateMaterial(id, materialData);
        } catch (error) {
            logger.error(`更新物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除物料
     */
    async deleteMaterial(id, operatorId) {
        try {
            return await this.repository.deleteMaterial(id);
        } catch (error) {
            logger.error(`删除物料失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 物料入库
     */
    async materialInbound(inboundData, operatorId) {
        try {
            // PostgreSQL事务处理
            return await this.repository.transaction(async (client) => {
                // 创建入库事务记录
                const transactionData = {
                    id: this.repository.generateId(),
                    transaction_number: this.generateTransactionNumber('IN'),
                    transaction_type: 'inbound',
                    item_type: 'material',
                    item_id: inboundData.material_id,
                    quantity: inboundData.quantity,
                    batch_number: inboundData.batch_number,
                    qrcode: inboundData.qrcode,
                    operator_id: operatorId,
                    notes: inboundData.notes
                };

                const transaction = await this.repository.createTransaction(transactionData);

                // 更新物料库存
                await this.repository.updateMaterialStock(
                    inboundData.material_id,
                    inboundData.quantity,
                    'add'
                );

                return transaction;
            });
        } catch (error) {
            logger.error('物料入库失败:', error);
            throw error;
        }
    }

    /**
     * 物料出库
     */
    async materialOutbound(outboundData, operatorId) {
        try {
            // 检查库存
            const material = await this.repository.getMaterialById(outboundData.material_id);
            if (!material) {
                throw new Error('物料不存在');
            }

            if (material.currentStock < outboundData.quantity) {
                throw new Error('库存不足');
            }

            // PostgreSQL事务处理
            return await this.repository.transaction(async (client) => {
                // 创建出库事务记录
                const transactionData = {
                    id: this.repository.generateId(),
                    transaction_number: this.generateTransactionNumber('OUT'),
                    transaction_type: 'outbound',
                    item_type: 'material',
                    item_id: outboundData.material_id,
                    quantity: outboundData.quantity,
                    qrcode: outboundData.qrcode,
                    operator_id: operatorId,
                    notes: outboundData.notes
                };

                const transaction = await this.repository.createTransaction(transactionData);

                // 更新物料库存
                await this.repository.updateMaterialStock(
                    outboundData.material_id,
                    outboundData.quantity,
                    'subtract'
                );

                return transaction;
            });
        } catch (error) {
            logger.error('物料出库失败:', error);
            throw error;
        }
    }

    // ==================== 成品管理方法 ====================

    /**
     * 获取成品列表
     */
    async getProducts(filters = {}) {
        try {
            return await this.repository.getAllProducts(filters);
        } catch (error) {
            logger.error('获取成品列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取成品
     */
    async getProductById(id) {
        try {
            return await this.repository.getProductById(id);
        } catch (error) {
            logger.error(`根据ID获取成品失败 (${id}):`, error);
            throw error;
        }
    }

    // ==================== 库存事务管理方法 ====================

    /**
     * 获取库存事务列表
     */
    async getTransactions(filters = {}) {
        try {
            return await this.repository.getTransactions(filters);
        } catch (error) {
            logger.error('获取库存事务列表失败:', error);
            throw error;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 生成事务编号
     */
    generateTransactionNumber(type) {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${type}${timestamp}${random}`;
    }

    /**
     * 获取数据库类型
     */
    getDatabaseType() {
        return 'postgresql';
    }

    /**
     * 测试连接
     */
    async testConnection() {
        try {
            return await this.repository.pool.testConnection();
        } catch (error) {
            logger.error('仓库管理服务连接测试失败:', error);
            return false;
        }
    }
}

// 创建全局仓库管理服务实例
const postgresWarehouseService = new PostgresWarehouseService();

module.exports = {
    PostgresWarehouseService,
    postgresWarehouseService
};
