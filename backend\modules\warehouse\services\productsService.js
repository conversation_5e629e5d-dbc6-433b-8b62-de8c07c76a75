/**
 * 成品管理服务层
 * 处理成品相关的业务逻辑
 */

const { postgresConnectionPool } = require('../../../utils/postgresConnectionPool');
const logger = require('../../../utils/logger');

class ProductsService {
    constructor() {
        this.db = postgresConnectionPool;
    }

    /**
     * 获取成品列表
     */
    async getProducts(filters = {}) {
        try {
            let query = `
                SELECT p.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE 1=1
            `;
            
            const params = [];
            let paramIndex = 1;

            if (filters.product_type) {
                query += ` AND p.product_type = $${paramIndex++}`;
                params.push(filters.product_type);
            }

            if (filters.status) {
                query += ` AND p.status = $${paramIndex++}`;
                params.push(filters.status);
            }

            if (filters.search) {
                query += ` AND (p.product_name ILIKE $${paramIndex++} OR p.product_code ILIKE $${paramIndex++})`;
                params.push(`%${filters.search}%`, `%${filters.search}%`);
            }

            query += ' GROUP BY p.id ORDER BY p.created_at DESC';

            if (filters.limit) {
                query += ` LIMIT $${paramIndex++}`;
                params.push(parseInt(filters.limit));
            }

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('获取成品列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取成品详情
     */
    async getProductById(id) {
        try {
            const result = await this.db.query(`
                SELECT p.*,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE p.id = $1
                GROUP BY p.id
            `, [id]);

            if (result.rows.length === 0) {
                throw new Error('成品不存在');
            }

            return result.rows[0];
        } catch (error) {
            logger.error('获取成品详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建成品
     */
    async createProduct(productData, operatorId) {
        try {
            const { product_code, product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description } = productData;

            // 检查成品编码是否已存在
            const existingResult = await this.db.query('SELECT id FROM warehouse_finished_products WHERE product_code = $1', [product_code]);
            if (existingResult.rows.length > 0) {
                throw new Error('成品编码已存在');
            }

            const result = await this.db.query(`
                INSERT INTO warehouse_finished_products (
                    product_code, product_name, product_type, unit,
                    packaging_info, min_stock_level, max_stock_level,
                    description, status, created_by, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'active', $9, NOW(), NOW())
                RETURNING id
            `, [product_code, product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, operatorId]);

            logger.info(`成品创建成功: ${product_code}`, { operatorId });
            return { id: result.rows[0].id, product_code };
        } catch (error) {
            logger.error('创建成品失败:', error);
            throw error;
        }
    }

    /**
     * 更新成品
     */
    async updateProduct(id, productData, operatorId) {
        try {
            const { product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, status } = productData;

            const result = await this.db.query(`
                UPDATE warehouse_finished_products
                SET product_name = $1, product_type = $2, unit = $3, packaging_info = $4,
                    min_stock_level = $5, max_stock_level = $6, description = $7, status = $8,
                    updated_by = $9, updated_at = NOW()
                WHERE id = $10
            `, [product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, status, operatorId, id]);

            if (result.rowCount === 0) {
                throw new Error('成品不存在或更新失败');
            }

            logger.info(`成品更新成功: ID ${id}`, { operatorId });
            return { id, updated: true };
        } catch (error) {
            logger.error('更新成品失败:', error);
            throw error;
        }
    }

    /**
     * 删除成品
     */
    async deleteProduct(id, operatorId) {
        try {
            // 检查是否有库存事务记录
            const transactionsResult = await this.db.query('SELECT id FROM warehouse_inventory_transactions WHERE item_type = $1 AND item_id = $2 LIMIT 1', ['product', id]);
            if (transactionsResult.rows.length > 0) {
                throw new Error('该成品存在库存事务记录，无法删除');
            }

            const result = await this.db.query('DELETE FROM warehouse_finished_products WHERE id = $1', [id]);

            if (result.rowCount === 0) {
                throw new Error('成品不存在');
            }

            logger.info(`成品删除成功: ID ${id}`, { operatorId });
            return { id, deleted: true };
        } catch (error) {
            logger.error('删除成品失败:', error);
            throw error;
        }
    }

    /**
     * 成品入库
     */
    async productInbound(inboundData, operatorId) {
        try {
            const { product_id, quantity, batch_number, qrcode, notes } = inboundData;
            
            // 验证成品存在
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建入库事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'inbound',
                item_type: 'product',
                item_id: product_id,
                quantity,
                batch_number,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品入库成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品入库失败:', error);
            throw error;
        }
    }

    /**
     * 成品出库
     */
    async productOutbound(outboundData, operatorId) {
        try {
            const { product_id, quantity, qrcode, notes } = outboundData;
            
            // 验证成品存在和库存
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            if (product.current_stock < quantity) {
                throw new Error('库存不足');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建出库事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'outbound',
                item_type: 'product',
                item_id: product_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品出库成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品出库失败:', error);
            throw error;
        }
    }

    /**
     * 成品返仓
     */
    async productReturn(returnData, operatorId) {
        try {
            const { product_id, quantity, qrcode, notes } = returnData;
            
            // 验证成品存在
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建返仓事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'return',
                item_type: 'product',
                item_id: product_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品返仓成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品返仓失败:', error);
            throw error;
        }
    }

    /**
     * 获取成品库存报告
     */
    async getProductsInventory(filters = {}) {
        try {
            let query = `
                SELECT p.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) as total_inbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) as total_outbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as total_return,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock,
                       CASE 
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) <= p.min_stock_level 
                           THEN 'low_stock'
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) >= p.max_stock_level 
                           THEN 'high_stock'
                           ELSE 'normal'
                       END as stock_status
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE p.status = 'active'
            `;
            
            const params = [];

            if (filters.stock_status) {
                query += ' GROUP BY p.id HAVING stock_status = $1';
                params.push(filters.stock_status);
            } else {
                query += ' GROUP BY p.id';
            }
            
            query += ' ORDER BY p.product_name';

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('获取成品库存报告失败:', error);
            throw error;
        }
    }
}

module.exports = new ProductsService();
