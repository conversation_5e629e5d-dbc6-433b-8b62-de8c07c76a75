/**
 * 邮件队列控制器
 * 提供邮件队列状态监控和管理功能
 */

const emailQueueManager = require('../services/emailQueueManager');
const logger = require('../utils/logger');

/**
 * 获取邮件队列状态
 */
async function getQueueStatus(req, res) {
    try {
        const status = emailQueueManager.getQueueStatus();
        
        res.json({
            success: true,
            data: {
                ...status,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        logger.error('获取邮件队列状态失败:', error);
        res.status(500).json({
            success: false,
            message: '获取邮件队列状态失败'
        });
    }
}

/**
 * 重试失败的邮件任务
 */
async function retryFailedTasks(req, res) {
    try {
        emailQueueManager.retryFailedTasks();
        
        logger.info('手动重试失败的邮件任务', {
            userId: req.user.id,
            username: req.user.username
        });
        
        res.json({
            success: true,
            message: '已重试所有失败的邮件任务'
        });
    } catch (error) {
        logger.error('重试失败的邮件任务失败:', error);
        res.status(500).json({
            success: false,
            message: '重试失败的邮件任务失败'
        });
    }
}

/**
 * 清理失败的邮件任务
 */
async function cleanupFailedTasks(req, res) {
    try {
        emailQueueManager.cleanupFailedTasks();
        
        logger.info('手动清理失败的邮件任务', {
            userId: req.user.id,
            username: req.user.username
        });
        
        res.json({
            success: true,
            message: '已清理失败的邮件任务'
        });
    } catch (error) {
        logger.error('清理失败的邮件任务失败:', error);
        res.status(500).json({
            success: false,
            message: '清理失败的邮件任务失败'
        });
    }
}

module.exports = {
    getQueueStatus,
    retryFailedTasks,
    cleanupFailedTasks
};
