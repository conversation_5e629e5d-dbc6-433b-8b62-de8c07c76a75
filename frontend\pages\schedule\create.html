<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新建排程 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/schedule/common.css">
    <link rel="stylesheet" href="/assets/css/schedule/create.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">智能排程</h1>
                            <p class="text-gray-600 mt-1">基于AI算法的智能生产排程系统</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="switchToTraditional"
                                    class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                传统排程
                            </button>
                            <a href="/schedule/list"
                               class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                返回列表
                            </a>
                        </div>
                    </div>
                </header>

                <!-- 表单内容 -->
                <div class="p-6">
                    <div class="max-w-4xl mx-auto">
                        <!-- 智能排程模式 -->
                        <div v-if="!showTraditionalMode" class="space-y-6">
                            <!-- 订单信息输入 -->
                            <div class="bg-white shadow rounded-lg">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-900">订单信息</h3>
                                    <p class="text-sm text-gray-600 mt-1">输入订单信息，系统将自动生成多个优化排程方案</p>
                                </div>
                                <form @submit.prevent="generateSchedulePlans" class="p-6">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- 订单ID -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">订单ID *</label>
                                            <div class="flex">
                                                <input type="text" v-model="orderForm.id" required
                                                       class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                       placeholder="请输入订单ID">
                                                <button type="button" @click="generateOrderId"
                                                        class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 text-sm">
                                                    生成
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 产品ID -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">产品ID *</label>
                                            <div class="flex">
                                                <input type="text" v-model="orderForm.productId" required
                                                       class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                       placeholder="请输入产品ID">
                                                <button type="button" @click="generateProductId"
                                                        class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 text-sm">
                                                    选择
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 订单数量 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">订单数量 *</label>
                                            <input type="number" v-model.number="orderForm.quantity" min="1" required
                                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   placeholder="请输入订单数量">
                                        </div>

                                        <!-- 要求交期 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">要求交期 *</label>
                                            <input type="date" v-model="orderForm.requiredDate" required
                                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <!-- 优先级 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                            <select v-model="orderForm.priority"
                                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="low">低优先级</option>
                                                <option value="normal">普通</option>
                                                <option value="high">高优先级</option>
                                            </select>
                                        </div>

                                        <!-- 客户名称 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">客户名称</label>
                                            <input type="text" v-model="orderForm.customerName"
                                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   placeholder="请输入客户名称">
                                        </div>

                                        <!-- 特殊要求 -->
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">特殊要求</label>
                                            <textarea v-model="orderForm.specialRequirements" rows="2"
                                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                      placeholder="请输入特殊要求"></textarea>
                                        </div>
                                </div>

                                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                                        <div class="flex space-x-2">
                                            <button type="button" @click="fillExampleData"
                                                    class="px-3 py-2 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50">
                                                填充示例数据
                                            </button>
                                        </div>
                                        <div class="flex space-x-4">
                                            <button type="button" @click="resetOrderForm"
                                                    class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                                重置
                                            </button>
                                            <button type="submit" :disabled="submitting"
                                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                                                {{ submitting ? '生成中...' : '生成智能排程方案' }}
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- 智能排程方案显示区域 -->
                            <div v-if="schedulePlans.length > 0" class="bg-white shadow rounded-lg mt-6">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-900">智能排程方案</h3>
                                    <p class="text-sm text-gray-600 mt-1">系统为您生成了 {{ schedulePlans.length }} 个优化方案，请选择最适合的方案</p>
                                </div>

                                <div class="p-6">
                                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                                        <div v-for="plan in schedulePlans" :key="plan.id"
                                             :class="['plan-card bg-white border-2 rounded-lg p-6 cursor-pointer transition-all duration-200',
                                                     plan.selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:shadow-md']"
                                             @click="selectPlan(plan)">
                                            <div class="flex items-center justify-between mb-4">
                                                <h4 class="text-lg font-medium text-gray-900">{{ plan.name }}</h4>
                                                <span :class="['px-2 py-1 text-xs font-medium rounded-full',
                                                              getRiskClass(plan.riskAssessment?.level)]">
                                                    {{ getRiskText(plan.riskAssessment?.level) }}
                                                </span>
                                            </div>

                                            <div class="space-y-3">
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">预计交期:</span>
                                                    <span class="text-sm font-medium">{{ formatDate(plan.finalPrediction?.deliveryDate) }}</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">按时概率:</span>
                                                    <span class="text-sm font-medium">{{ Math.round((plan.finalPrediction?.onTimeProb || 0) * 100) }}%</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">效率评分:</span>
                                                    <span class="text-sm font-medium">{{ (plan.metrics?.efficiency || 0).toFixed(2) }}</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">成本估算:</span>
                                                    <span class="text-sm font-medium">¥{{ (plan.metrics?.cost || 0).toLocaleString() }}</span>
                                                </div>
                                            </div>

                                            <div class="mt-4 pt-4 border-t border-gray-200">
                                                <p class="text-xs text-gray-500">{{ plan.description }}</p>
                                            </div>

                                            <div v-if="plan.selected" class="mt-4 flex space-x-2">
                                                <button @click.stop="viewPlanDetails(plan)"
                                                        class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                                    查看详情
                                                </button>
                                                <button @click.stop="confirmPlan(plan)"
                                                        class="flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
                                                    确认方案
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 传统排程模式 -->
                        <div v-else class="bg-white shadow rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">传统排程</h3>
                                    <p class="text-sm text-gray-600 mt-1">手动创建排程计划</p>
                                </div>
                                <button @click="switchToIntelligent"
                                        class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50">
                                    切换到智能排程
                                </button>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-500 text-center py-8">传统排程功能开发中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入组件和脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/schedule/create.js"></script>
</body>
</html>
