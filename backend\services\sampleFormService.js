/**
 * 样品单服务层
 * 处理样品单相关的业务逻辑
 */

const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class SampleFormService {
    constructor(sampleFormRepository) {
        this.sampleFormRepository = sampleFormRepository;
    }

    /**
     * 创建样品单
     */
    async createSampleForm(sampleData, files, userId) {
        try {
            // 生成唯一ID和编号
            const id = uuidv4();
            const sampleNumber = await this.sampleFormRepository.generateSampleNumber();

            // 准备样品单数据
            const sampleFormData = {
                id,
                sampleNumber,
                generatedNumber: sampleData.generatedNumber || sampleNumber,
                date: sampleData.date || new Date().toISOString().split('T')[0],
                purpose: sampleData.purpose,
                companyName: sampleData.companyName,
                recipient: sampleData.recipient,
                application: sampleData.application,
                address: sampleData.address,
                productModel: sampleData.productModel,
                productDescription: sampleData.productDescription,
                outerLayer: sampleData.outerLayer,
                middleLayer: sampleData.middleLayer,
                innerLayer: sampleData.innerLayer,
                headband: sampleData.headband,
                noseBridge: sampleData.noseBridge,
                earLoop: sampleData.earLoop,
                valve: sampleData.valve,
                valveTopCover: sampleData.valveTopCover,
                valveBottomCover: sampleData.valveBottomCover,
                valveSheet: sampleData.valveSheet,
                quantity: parseInt(sampleData.quantity) || 0,
                printColor: sampleData.printColor,
                printSize: sampleData.printSize,
                headbandColor: sampleData.headbandColor,
                valveColor: sampleData.valveColor,
                valvePrint: sampleData.valvePrint,
                printMaterialColor: sampleData.printMaterialColor,
                printMaterialSize: sampleData.printMaterialSize,
                adjustmentBuckle: sampleData.adjustmentBuckle,
                internalTest: sampleData.internalTest,
                packagingMethod: sampleData.packagingMethod,
                packagingMaterials: this.parseJSONField(sampleData.packagingMaterials, []),
                colorBox: this.parseBooleanField(sampleData.colorBox),
                tape: this.parseBooleanField(sampleData.tape),
                tapeMaterial: sampleData.tapeMaterial,
                blisterPack: this.parseBooleanField(sampleData.blisterPack),
                other: this.parseBooleanField(sampleData.other),
                otherDescription: sampleData.otherDescription,
                testNotes: sampleData.testNotes,
                testQuantity: sampleData.testQuantity,
                testItems: this.parseJSONField(sampleData.testItems, []),
                testLevels: this.parseJSONField(sampleData.testLevels, {}),
                testSampleCounts: this.parseJSONField(sampleData.testSampleCounts, {}),
                otherTestItems: this.parseJSONField(sampleData.otherTestItems, {}),
                additionalNotes: sampleData.additionalNotes,
                recipients: this.parseJSONField(sampleData.recipients, []),
                ccRecipients: this.parseJSONField(sampleData.ccRecipients, []),
                status: 'submitted',
                createdBy: userId
            };

            // 创建样品单记录
            const sampleForm = await this.sampleFormRepository.createSampleForm(sampleFormData);

            // 处理文件上传
            if (files && Object.keys(files).length > 0) {
                await this.handleFileUploads(files, sampleForm.id, sampleData);
            }

            // 重新获取包含附件的完整样品单数据
            const completeSampleForm = await this.sampleFormRepository.getSampleFormById(sampleForm.id);

            logger.info(`样品单创建成功: ${sampleNumber}`);
            return completeSampleForm;

        } catch (error) {
            logger.error('创建样品单失败:', error);
            throw new Error('创建样品单失败: ' + error.message);
        }
    }

    /**
     * 处理文件上传（使用multer已上传的文件）
     */
    async handleFileUploads(files, sampleFormId, sampleData) {
        try {
            logger.info('开始处理样品单文件上传', {
                sampleFormId,
                fileTypes: Object.keys(files),
                fileCounts: Object.keys(files).reduce((acc, key) => {
                    acc[key] = Array.isArray(files[key]) ? files[key].length : (files[key] ? 1 : 0);
                    return acc;
                }, {})
            });

            // 创建最终目录，使用样品单编号而不是ID
            const companyName = this.sanitizeDirectoryName(sampleData.companyName || 'default');
            const sampleNumber = sampleData.generatedNumber || sampleFormId; // 使用样品单编号
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');

            const finalDir = path.join(
                __dirname, '../uploads/samples',
                companyName,
                year.toString(),
                month,
                day,
                sampleNumber  // 使用样品单编号作为文件夹名
            );

            // 确保最终目录存在
            const fsSync = require('fs');
            if (!fsSync.existsSync(finalDir)) {
                fsSync.mkdirSync(finalDir, { recursive: true });
                logger.info('创建样品单最终目录:', finalDir);
            }

            const fileTypes = ['maskPrintFiles', 'valvePrintFiles', 'colorBoxFiles', 'blisterPackFiles'];

            for (const fileType of fileTypes) {
                if (files[fileType]) {
                    const fileArray = Array.isArray(files[fileType]) ? files[fileType] : [files[fileType]];

                    for (const file of fileArray) {
                        logger.info('处理文件', {
                            fileType,
                            originalname: file.originalname,
                            filename: file.filename,
                            tempPath: file.path,
                            size: file.size
                        });

                        // 检查临时文件是否存在
                        if (!fsSync.existsSync(file.path)) {
                            logger.error('临时文件不存在:', file.path);
                            throw new Error(`临时文件不存在: ${file.path}`);
                        }

                        // 按照README.md规范处理文件冲突
                        let finalPath = path.join(finalDir, file.filename);
                        let finalFilename = file.filename;

                        // 如果文件已存在，按照README.md规范添加数字后缀
                        if (fsSync.existsSync(finalPath)) {
                            const ext = path.extname(file.filename);
                            const baseName = path.basename(file.filename, ext);
                            let counter = 1;

                            do {
                                finalFilename = `${baseName}_${counter}${ext}`;
                                finalPath = path.join(finalDir, finalFilename);
                                counter++;
                            } while (fsSync.existsSync(finalPath) && counter < 100); // 防止无限循环

                            logger.info('文件冲突处理（遵循README.md规范）', {
                                originalFilename: file.filename,
                                finalFilename,
                                conflictCounter: counter - 1
                            });
                        }

                        try {
                            fsSync.renameSync(file.path, finalPath);
                            logger.info('文件移动成功', {
                                from: file.path,
                                to: finalPath
                            });
                        } catch (moveError) {
                            logger.error('文件移动失败，尝试复制', {
                                error: moveError.message,
                                from: file.path,
                                to: finalPath
                            });

                            try {
                                fsSync.copyFileSync(file.path, finalPath);
                                fsSync.unlinkSync(file.path); // 删除临时文件
                                logger.info('文件复制成功', {
                                    from: file.path,
                                    to: finalPath
                                });
                            } catch (copyError) {
                                logger.error('文件复制也失败:', copyError);
                                throw new Error(`文件移动和复制都失败: ${copyError.message}`);
                            }
                        }

                        // 验证文件是否成功保存
                        if (!fsSync.existsSync(finalPath)) {
                            throw new Error(`文件保存失败，目标文件不存在: ${finalPath}`);
                        }

                        const fileId = uuidv4();

                        // 创建附件记录，使用处理冲突后的最终文件名和路径
                        await this.sampleFormRepository.createSampleFormAttachment({
                            id: fileId,
                            sampleFormId,
                            fileType,
                            originalFilename: file.originalname,
                            storedFilename: finalFilename, // 使用处理冲突后的最终文件名
                            filePath: finalPath, // 使用最终路径
                            fileSize: file.size,
                            mimeType: file.mimetype
                        });

                        logger.info('样品单附件记录创建成功（遵循README.md规范）', {
                            fileId,
                            sampleFormId,
                            fileType,
                            originalFilename: file.originalname,
                            storedFilename: finalFilename, // 记录最终文件名
                            filePath: finalPath,
                            fileExists: fsSync.existsSync(finalPath)
                        });
                    }
                }
            }

            logger.info('样品单文件上传处理完成', {
                sampleFormId,
                finalDir,
                finalDirExists: fsSync.existsSync(finalDir),
                fileCount: fsSync.readdirSync(finalDir).length
            });
        } catch (error) {
            logger.error('处理文件上传失败:', error);
            throw error;
        }
    }

    /**
     * 清理目录名称，严格遵循README.md规范
     * 规范：移除特殊字符<>:"/\|?*，空格替换为下划线
     * 示例：ABC 公司 (上海) → ABC_公司_上海_
     */
    sanitizeDirectoryName(name) {
        if (!name || name.trim() === '') return 'default';

        // 按照README.md规范处理公司名称
        return name
            .replace(/[<>:"/\\|?*]/g, '')   // 移除README.md中指定的特殊字符
            .replace(/\s+/g, '_')           // 空格替换为下划线
            .replace(/\(/g, '_')            // 左括号替换为下划线
            .replace(/\)/g, '_')            // 右括号替换为下划线
            .replace(/_{2,}/g, '_')         // 多个下划线合并为一个
            .replace(/^_|_$/g, '')          // 移除首尾下划线
            .substring(0, 50);              // 限制长度防止路径过长
    }

    /**
     * 获取所有样品单
     */
    async getAllSampleForms() {
        try {
            return await this.sampleFormRepository.getAllSampleForms();
        } catch (error) {
            logger.error('获取样品单列表失败:', error);
            throw new Error('获取样品单列表失败: ' + error.message);
        }
    }

    /**
     * 根据ID获取样品单
     */
    async getSampleFormById(id) {
        try {
            const sampleForm = await this.sampleFormRepository.getSampleFormById(id);
            if (!sampleForm) {
                throw new Error('样品单不存在');
            }
            return sampleForm;
        } catch (error) {
            logger.error('获取样品单详情失败:', error);
            throw new Error('获取样品单详情失败: ' + error.message);
        }
    }

    /**
     * 删除样品单
     */
    async deleteSampleForm(id, userId) {
        try {
            const sampleForm = await this.sampleFormRepository.getSampleFormById(id);
            if (!sampleForm) {
                throw new Error('样品单不存在');
            }

            const success = await this.sampleFormRepository.deleteSampleForm(id);
            if (!success) {
                throw new Error('删除样品单失败');
            }

            logger.info(`样品单删除成功: ${sampleForm.sampleNumber}, 操作人: ${userId}`);
            return { success: true, message: '样品单删除成功' };

        } catch (error) {
            logger.error('删除样品单失败:', error);
            throw new Error('删除样品单失败: ' + error.message);
        }
    }

    /**
     * 生成样品单编号
     */
    async generateSampleNumber() {
        try {
            return await this.sampleFormRepository.generateSampleNumber();
        } catch (error) {
            logger.error('生成样品单编号失败:', error);
            throw new Error('生成样品单编号失败: ' + error.message);
        }
    }

    /**
     * 解析JSON字段
     */
    parseJSONField(value, defaultValue = null) {
        if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            } catch (e) {
                return defaultValue;
            }
        }
        return value || defaultValue;
    }

    /**
     * 解析布尔字段
     */
    parseBooleanField(value) {
        if (typeof value === 'string') {
            return value === 'true' || value === '1';
        }
        return Boolean(value);
    }

    /**
     * 验证样品单数据
     */
    validateSampleFormData(data) {
        const requiredFields = ['companyName', 'recipient', 'productModel', 'quantity'];
        const errors = [];

        for (const field of requiredFields) {
            if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
                errors.push(`${field} 是必填字段`);
            }
        }

        if (data.quantity && (isNaN(data.quantity) || parseInt(data.quantity) <= 0)) {
            errors.push('数量必须是大于0的数字');
        }

        if (errors.length > 0) {
            throw new Error('数据验证失败: ' + errors.join(', '));
        }

        return true;
    }
}

module.exports = SampleFormService;
