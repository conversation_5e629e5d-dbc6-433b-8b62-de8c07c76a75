/**
 * 设备健康度评估服务
 * 实现四维度健康度计算算法
 */

const logger = require('../utils/logger');
const HealthCalculator = require('../utils/healthCalculator');
const FaultPredictor = require('../utils/faultPredictor');
const RecommendationEngine = require('../utils/recommendationEngine');
const PostgresHealthRepository = require('../database/postgresHealthRepository');
const PostgresEquipmentRepository = require('../database/postgresEquipmentRepository');
class HealthAssessmentService {
    constructor() {
        try {
            this.healthRepository = new PostgresHealthRepository();
            this.equipmentRepository = new PostgresEquipmentRepository();
            this.calculator = new HealthCalculator();
            this.predictor = new FaultPredictor();
            this.recommendationEngine = new RecommendationEngine();

            // 验证关键组件是否正确初始化
            if (!this.healthRepository) {
                throw new Error('PostgresHealthRepository 初始化失败');
            }
            if (typeof this.healthRepository.findHealthHistory !== 'function') {
                throw new Error('PostgresHealthRepository.findHealthHistory 方法不存在');
            }

            logger.debug('HealthAssessmentService 初始化完成', {
                healthRepository: !!this.healthRepository,
                equipmentRepository: !!this.equipmentRepository,
                calculator: !!this.calculator
            });
        } catch (error) {
            logger.error('HealthAssessmentService 初始化失败:', error);
            throw error;
        }
    }

    // 延迟初始化数据库适配器
    getDatabaseAdapter() {
        if (!this._databaseAdapter) {
            try {
                const { databaseAdapter } = require('../database/databaseAdapter');
                logger.debug('导入 databaseAdapter:', {
                    databaseAdapter: !!databaseAdapter,
                    type: typeof databaseAdapter,
                    hasGetMaintenanceRepository: !!(databaseAdapter && databaseAdapter.getMaintenanceRepository)
                });
                this._databaseAdapter = databaseAdapter;
            } catch (error) {
                logger.error('导入 databaseAdapter 失败:', error);
                throw error;
            }
        }
        return this._databaseAdapter;
    }



    /**
     * 计算设备健康度 - 使用标准化的四维度评估算法
     */
    async calculateEquipmentHealth(equipmentId, assessor) {
        try {
            logger.info('开始计算设备健康度', { equipmentId, assessor });

            // 获取设备基本信息
            const equipment = await this.equipmentRepository.findById(equipmentId);
            if (!equipment) {
                throw new Error('设备不存在');
            }

            // 获取真实的维修保养记录
            const maintenanceRecords = await this.getAllMaintenanceRecords(equipmentId);
            logger.debug('获取维修保养记录', { equipmentId, recordCount: maintenanceRecords.length });

            // 使用标准化的健康度计算器
            const healthCalculator = new (require('../utils/healthCalculator'))();

            // 计算所有维度评分
            const dimensions = healthCalculator.calculateAllDimensions(equipment, maintenanceRecords);

            // 计算总分
            const totalScore = healthCalculator.calculateTotalScore(dimensions);

            // 获取健康度等级和描述
            const healthLevel = healthCalculator.getHealthLevel(totalScore);
            const healthDescription = healthCalculator.getHealthLevelDescription(totalScore);

            // 提取各维度分数用于比较
            const ageScore = dimensions.age.score;
            const repairScore = dimensions.repairFrequency.score;
            const faultScore = dimensions.faultSeverity.score;
            const maintenanceScore = dimensions.maintenance.score;

            logger.debug('健康度计算结果', {
                equipmentId,
                totalScore,
                ageScore,
                repairScore,
                faultScore,
                maintenanceScore
            });

            // 检查是否已有最新的健康度记录（避免重复计算）
            const existingHealth = await this.healthRepository.findLatestHealth(equipmentId);

            // 检查是否需要更新记录（比较总分和各维度分数）
            let shouldUpdate = true;
            if (existingHealth &&
                existingHealth.health_score === totalScore &&
                existingHealth.age_score === ageScore &&
                existingHealth.repair_frequency_score === repairScore &&
                existingHealth.fault_severity_score === faultScore &&
                existingHealth.maintenance_score === maintenanceScore) {
                shouldUpdate = false;
                logger.debug('健康度分数未变化，使用现有记录', { equipmentId, totalScore });
            }

            // 生成维护建议
            const recommendations = this.recommendationEngine.generateRecommendations(dimensions, equipment, maintenanceRecords);

            // 计算故障预测
            const faultPrediction = this.predictor.predictNextFailure(maintenanceRecords);
            const failureProbability = faultPrediction.canPredict ? (100 - faultPrediction.prediction.confidence) / 100 : 0;

            // 计算下次维护日期
            const nextMaintenanceDate = this.calculateNextMaintenanceDate(equipment, dimensions, maintenanceRecords);

            // 使用当前时间作为评估时间
            const assessmentDate = new Date().toISOString();

            // 构建健康度数据
            const healthData = {
                equipment_id: equipmentId,
                health_score: totalScore,
                total_score: totalScore,
                health_level: healthLevel,
                age_score: ageScore,
                repair_frequency_score: repairScore,
                fault_severity_score: faultScore,
                maintenance_score: maintenanceScore,
                assessment_date: assessmentDate,
                assessor: assessor,
                calculation_details: JSON.stringify(dimensions),
                recommendations: JSON.stringify(recommendations),
                next_maintenance_date: nextMaintenanceDate,
                failure_probability: failureProbability,
                // 基于设备真实数据计算传感器数据
                temperature: this.calculateTemperature(equipment, totalScore),
                vibration: this.calculateVibration(equipment, totalScore),
                pressure: this.calculatePressure(equipment, totalScore),
                runtime_hours: this.calculateRuntimeHours(equipment),
                maintenance_status: equipment.status === 'maintenance' ? 'maintenance' : 'normal',
                last_maintenance: equipment.last_maintenance_date || null,
                next_maintenance: equipment.next_maintenance_date || null,
                alerts: totalScore < 60 ? ['低健康度警告'] : []
            };

            // 只有在需要更新时才保存到数据库
            if (shouldUpdate) {
                await this.healthRepository.createHealth(healthData);
                logger.debug('已保存新的健康度记录', { equipmentId, totalScore });
            }

            // 构建返回结果（包含详细的维度信息）
            const result = {
                equipmentId,
                totalScore: totalScore,
                healthLevel: healthCalculator.getHealthLevel(totalScore),
                healthDescription: healthCalculator.getHealthLevelDescription(totalScore),
                assessmentDate: assessmentDate,
                details: {
                    age: dimensions.age,
                    repairFrequency: dimensions.repairFrequency,
                    faultSeverity: dimensions.faultSeverity,
                    maintenance: dimensions.maintenance
                },
                temperature: healthData.temperature,
                vibration: healthData.vibration,
                pressure: healthData.pressure,
                runtime_hours: healthData.runtime_hours,
                maintenance_status: healthData.maintenance_status,
                alerts: healthData.alerts
            };

            logger.info('设备健康度计算完成', {
                equipmentId,
                totalScore: result.totalScore,
                healthLevel: result.healthLevel,
                details: result.details,
                assessor,
                updated: shouldUpdate
            });

            return result;
        } catch (error) {
            logger.error('设备健康度计算失败', {
                equipmentId,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }









    /**
     * 辅助方法：获取设备年龄（年）
     * 根据当前日期计算设备实际年龄
     */
    getEquipmentAgeInYears(equipment) {
        if (!equipment.manufacture_date) {
            return 5; // 默认5年
        }
        const manufactureDate = new Date(equipment.manufacture_date);
        // 使用当前日期计算设备实际年龄
        const currentDate = new Date();
        return (currentDate - manufactureDate) / (1000 * 60 * 60 * 24 * 365.25);
    }

    /**
     * 辅助方法：计算运行小时数
     * 基于设备年龄和ID生成固定的运行小时数
     */
    calculateRuntimeHours(equipment) {
        const ageInYears = this.getEquipmentAgeInYears(equipment);

        // 基础运行小时数：每年约2000小时
        let baseHours = ageInYears * 2000;

        // 基于设备ID生成固定的调整值
        const idHash = this.generateHashFromId(equipment.id);
        const adjustment = (idHash % 1001); // 0-1000小时的固定调整

        // 根据设备状态调整
        if (equipment.status === 'inactive') {
            baseHours *= 0.7; // 非活跃设备运行时间较少
        } else if (equipment.status === 'fault') {
            baseHours *= 1.2; // 故障设备可能过度使用
        }

        return Math.round(baseHours + adjustment);
    }

    /**
     * 获取真实的维修记录
     * 从数据库中查询设备的维修记录
     */
    async getRepairRecords(equipmentId) {
        try {
            // 从数据库查询真实的维修记录
            const maintenanceRepository = this.getDatabaseAdapter().getMaintenanceRepository();
            const allRecords = await maintenanceRepository.findByEquipmentId(equipmentId);

            // 筛选维修记录（排除保养记录）
            const repairRecords = allRecords.filter(record =>
                record.type === 'repair' || record.type === 'emergency'
            );

            return repairRecords.map(record => ({
                id: record.id,
                equipment_id: record.equipment_id,
                repair_date: record.maintenance_date,
                type: record.type,
                description: record.description || record.problem_description,
                severity_level: record.severity_level
            }));
        } catch (error) {
            logger.error('获取维修记录失败', { equipmentId, error: error.message });
            return [];
        }
    }

    /**
     * 获取真实的故障记录
     * 从数据库中查询设备的故障记录
     */
    async getFaultRecords(equipmentId) {
        try {
            // 从数据库查询真实的故障记录（维修记录中的故障）
            const maintenanceRepository = this.getDatabaseAdapter().getMaintenanceRepository();
            const allRecords = await maintenanceRepository.findByEquipmentId(equipmentId);

            // 筛选故障记录（维修类型的记录）
            const faultRecords = allRecords.filter(record =>
                record.type === 'repair' || record.type === 'emergency'
            );

            return faultRecords.map(record => ({
                id: record.id,
                equipment_id: record.equipment_id,
                fault_date: record.maintenance_date,
                severity_level: record.severity_level || 'moderate', // 默认一般严重程度
                description: record.problem_description || record.description
            }));
        } catch (error) {
            logger.error('获取故障记录失败', { equipmentId, error: error.message });
            return [];
        }
    }

    /**
     * 获取真实的保养记录
     * 从数据库中查询设备的保养记录
     */
    async getMaintenanceRecords(equipmentId) {
        try {
            // 从数据库查询真实的保养记录
            const maintenanceRepository = this.getDatabaseAdapter().getMaintenanceRepository();
            const allRecords = await maintenanceRepository.findByEquipmentId(equipmentId);

            // 筛选保养记录（排除维修记录）
            const maintenanceRecords = allRecords.filter(record =>
                record.type === 'maintenance' || record.type === 'preventive'
            );

            return maintenanceRecords.map(record => ({
                id: record.id,
                equipment_id: record.equipment_id,
                maintenance_date: record.maintenance_date,
                type: record.type,
                description: record.maintenance_content || record.description
            }));
        } catch (error) {
            logger.error('获取保养记录失败', { equipmentId, error: error.message });
            return [];
        }
    }

    /**
     * 获取所有维修保养记录（用于健康度计算）
     * 返回设备的所有维修和保养记录
     */
    async getAllMaintenanceRecords(equipmentId) {
        try {
            // 从数据库查询所有维修保养记录
            const maintenanceRepository = this.getDatabaseAdapter().getMaintenanceRepository();
            const allRecords = await maintenanceRepository.findByEquipmentId(equipmentId);

            return allRecords.map(record => ({
                id: record.id,
                equipment_id: record.equipment_id,
                maintenance_date: record.maintenance_date,
                type: record.maintenance_type || record.type, // 兼容新旧字段名
                description: record.maintenance_content || record.problem_description || record.description,
                severity_level: record.severity_level || 'moderate'
            }));
        } catch (error) {
            logger.error('获取所有维修保养记录失败', { equipmentId, error: error.message });
            return [];
        }
    }

    /**
     * 获取设备健康度历史
     */
    async getEquipmentHealthHistory(equipmentId, limit = 30) {
        try {
            if (!this.healthRepository) {
                throw new Error('healthRepository 未初始化');
            }

            if (typeof this.healthRepository.findHealthHistory !== 'function') {
                throw new Error('findHealthHistory 方法不存在');
            }

            const history = await this.healthRepository.findHealthHistory(equipmentId, limit);
            return history;
        } catch (error) {
            logger.error('获取设备健康度历史失败', {
                equipmentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取健康度统计
     */
    async getHealthStatistics(filters = {}) {
        try {
            const statistics = await this.healthRepository.getHealthStatistics(filters);
            return statistics;
        } catch (error) {
            logger.error('获取健康度统计失败:', error);
            throw error;
        }
    }

    /**
     * 保存健康度评估结果（已废弃，使用Repository方法）
     */
    async saveHealthAssessment(healthData, assessor) {
        const now = new Date().toISOString();
        const healthId = `HEALTH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const historyId = `HIST_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        try {
            // 开始事务
            const transaction = this.db.transaction(() => {
                // 插入当前健康度记录
                this.statements.insertHealth.run(
                    healthId,
                    healthData.equipmentId,
                    healthData.totalScore,
                    healthData.healthLevel,
                    healthData.dimensions.age.score,
                    healthData.dimensions.repairFrequency.score,
                    healthData.dimensions.faultSeverity.score,
                    healthData.dimensions.maintenance.score,
                    healthData.assessmentDate,
                    assessor,
                    JSON.stringify(healthData.dimensions),
                    JSON.stringify(healthData.recommendations),
                    healthData.prediction?.predictedDate || null,
                    healthData.prediction?.confidence || 0,
                    now,
                    now
                );

                // 插入历史记录
                this.statements.insertHealthHistory.run(
                    historyId,
                    healthData.equipmentId,
                    healthData.totalScore,
                    healthData.healthLevel,
                    healthData.dimensions.age.score,
                    healthData.dimensions.repairFrequency.score,
                    healthData.dimensions.faultSeverity.score,
                    healthData.dimensions.maintenance.score,
                    healthData.assessmentDate,
                    assessor,
                    JSON.stringify(healthData.dimensions),
                    now
                );
            });

            transaction();
        } catch (error) {
            logger.error('保存健康度评估失败', {
                equipmentId: healthData.equipmentId,
                error: error.message
            });
            throw error;
        }
    }



    /**
     * 计算健康度趋势
     */
    calculateTrends(history) {
        if (history.length < 2) {
            return null;
        }

        const current = history[0];
        const previous = history[1];

        return {
            totalScore: {
                current: current.total_score,
                previous: previous.total_score,
                change: current.total_score - previous.total_score,
                trend: current.total_score > previous.total_score ? 'increasing' : 
                       current.total_score < previous.total_score ? 'decreasing' : 'stable'
            }
        };
    }

    /**
     * 获取所有设备健康度统计
     */
    async getHealthStatistics(filters = {}) {
        try {
            const allHealth = this.statements.findAllEquipmentHealth.all();
            
            // 按等级分组统计
            const distribution = {
                excellent: 0,
                good: 0,
                average: 0,
                poor: 0,
                dangerous: 0
            };

            let totalScore = 0;
            const warningEquipment = [];

            allHealth.forEach(record => {
                totalScore += record.total_score;
                
                // 统计分布
                switch (record.health_level) {
                    case '优秀': distribution.excellent++; break;
                    case '良好': distribution.good++; break;
                    case '一般': distribution.average++; break;
                    case '较差': distribution.poor++; break;
                    case '危险': distribution.dangerous++; break;
                }

                // 收集需要关注的设备
                if (record.total_score < 70) {
                    warningEquipment.push({
                        equipmentId: record.equipment_id,
                        equipmentName: record.equipment_name,
                        equipmentCode: record.equipment_code,
                        area: record.area,
                        totalScore: record.total_score,
                        level: record.health_level,
                        urgentRecommendations: JSON.parse(record.recommendations || '[]')
                            .filter(r => r.priority === 'urgent' || r.priority === 'high').length
                    });
                }
            });

            return {
                overview: {
                    totalEquipment: allHealth.length,
                    averageHealth: allHealth.length > 0 ? Math.round(totalScore / allHealth.length) : 0,
                    lastUpdated: new Date().toISOString()
                },
                distribution,
                warningEquipment: warningEquipment.sort((a, b) => a.totalScore - b.totalScore)
            };
        } catch (error) {
            logger.error('获取健康度统计失败', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 批量计算设备健康度
     */
    async batchCalculateHealth(equipmentIds, assessor, options = {}) {
        const results = [];
        const errors = [];

        for (const equipmentId of equipmentIds) {
            try {
                const result = await this.calculateEquipmentHealth(equipmentId, assessor);
                results.push({
                    equipmentId,
                    totalScore: result.totalScore,
                    level: result.healthLevel,
                    calculatedAt: result.assessmentDate,
                    success: true
                });
            } catch (error) {
                errors.push({
                    equipmentId,
                    error: error.message,
                    success: false
                });
            }
        }

        return {
            results: [...results, ...errors],
            summary: {
                total: equipmentIds.length,
                successful: results.length,
                failed: errors.length,
                averageScore: results.length > 0 ?
                    Math.round(results.reduce((sum, r) => sum + r.totalScore, 0) / results.length) : 0
            }
        };
    }

    /**
     * 基于设备真实数据计算温度
     * 根据设备年龄、健康度和状态计算固定的温度值
     */
    calculateTemperature(equipment, healthScore) {
        // 基础温度：正常运行设备25-35°C
        let baseTemp = 30;

        // 根据设备年龄调整（老设备温度稍高）
        if (equipment.manufacture_date) {
            const age = this.getEquipmentAgeInYears(equipment);
            baseTemp += Math.min(age * 0.5, 10); // 最多增加10度
        }

        // 根据健康度调整
        if (healthScore < 60) {
            baseTemp += 15; // 健康度差的设备温度高
        } else if (healthScore < 80) {
            baseTemp += 8;
        }

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            baseTemp += 20;
        } else if (equipment.status === 'maintenance') {
            baseTemp -= 5; // 维护中温度较低
        }

        return Math.round(baseTemp * 10) / 10; // 保留一位小数
    }

    /**
     * 基于设备真实数据计算振动
     * 根据设备年龄、健康度和状态计算固定的振动值
     */
    calculateVibration(equipment, healthScore) {
        // 基础振动：正常设备1-3mm/s
        let baseVibration = 2;

        // 根据设备年龄调整
        if (equipment.manufacture_date) {
            const age = this.getEquipmentAgeInYears(equipment);
            baseVibration += Math.min(age * 0.2, 3); // 最多增加3mm/s
        }

        // 根据健康度调整
        if (healthScore < 60) {
            baseVibration += 4; // 健康度差振动大
        } else if (healthScore < 80) {
            baseVibration += 2;
        }

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            baseVibration += 5;
        } else if (equipment.status === 'maintenance') {
            baseVibration = 0.5; // 维护中振动很小
        }

        return Math.round(baseVibration * 10) / 10; // 保留一位小数
    }

    /**
     * 基于设备真实数据计算压力
     * 根据设备年龄、健康度和状态计算固定的压力值
     */
    calculatePressure(equipment, healthScore) {
        // 基础压力：正常设备5-8bar
        let basePressure = 6.5;

        // 根据健康度调整
        if (healthScore < 60) {
            basePressure -= 2; // 健康度差压力低
        } else if (healthScore < 80) {
            basePressure -= 1;
        } else if (healthScore >= 90) {
            basePressure += 1; // 健康度好压力稳定偏高
        }

        // 根据设备状态调整
        if (equipment.status === 'fault') {
            basePressure -= 3;
        } else if (equipment.status === 'maintenance') {
            basePressure = 0; // 维护中无压力
        }

        return Math.max(0, Math.round(basePressure * 10) / 10); // 保留一位小数，不能为负
    }

    /**
     * 基于设备ID生成固定的哈希值
     * 确保相同ID每次生成相同的数值
     */
    generateHashFromId(id) {
        let hash = 0;
        for (let i = 0; i < id.length; i++) {
            const char = id.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }

    /**
     * 计算下次维护日期
     */
    calculateNextMaintenanceDate(equipment, dimensions, maintenanceRecords) {
        try {
            // 基于健康度和维修历史计算下次维护日期
            const totalScore = dimensions.age.score * 0.2 +
                             dimensions.repairFrequency.score * 0.3 +
                             dimensions.faultSeverity.score * 0.3 +
                             dimensions.maintenance.score * 0.2;

            // 根据健康度确定维护间隔（天）
            let maintenanceInterval;
            if (totalScore >= 90) {
                maintenanceInterval = 180; // 6个月
            } else if (totalScore >= 80) {
                maintenanceInterval = 120; // 4个月
            } else if (totalScore >= 70) {
                maintenanceInterval = 90;  // 3个月
            } else if (totalScore >= 60) {
                maintenanceInterval = 60;  // 2个月
            } else {
                maintenanceInterval = 30;  // 1个月
            }

            // 找到最近的维护记录
            const sortedRecords = maintenanceRecords
                .filter(record => record.type === 'maintenance')
                .sort((a, b) => new Date(b.maintenance_date) - new Date(a.maintenance_date));

            let baseDate;
            if (sortedRecords.length > 0) {
                baseDate = new Date(sortedRecords[0].maintenance_date);
            } else {
                baseDate = new Date(); // 如果没有维护记录，从今天开始
            }

            // 计算下次维护日期
            const nextDate = new Date(baseDate);
            nextDate.setDate(nextDate.getDate() + maintenanceInterval);

            return nextDate.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
        } catch (error) {
            logger.error('计算下次维护日期失败', { error: error.message });
            // 默认返回30天后
            const defaultDate = new Date();
            defaultDate.setDate(defaultDate.getDate() + 30);
            return defaultDate.toISOString().split('T')[0];
        }
    }
}

module.exports = HealthAssessmentService;
