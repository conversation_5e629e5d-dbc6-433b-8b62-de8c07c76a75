<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源管理 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/schedule/common.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-50">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" v-cloak>
        <div class="flex">
            <!-- 侧边栏 -->
            <sidebar :user="currentUser"></sidebar>

            <!-- 主内容区 -->
            <div class="flex-1 ml-72">
                <!-- 头部 -->
                <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">资源管理</h1>
                            <p class="text-gray-600 mt-1">管理设备、人员和物料资源</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button @click="refreshData"
                                    class="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                    </div>
                </header>

                <!-- 资源概览 -->
                <div class="p-6">
                    <!-- 资源统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- 设备资源 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">设备总数</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ mockData.equipment.length }}</p>
                                    <p class="text-sm text-green-600">{{ getAvailableCount(mockData.equipment) }} 台可用</p>
                                </div>
                            </div>
                        </div>

                        <!-- 人员资源 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">人员总数</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ mockData.personnel.length }}</p>
                                    <p class="text-sm text-green-600">{{ getAvailableCount(mockData.personnel) }} 人可用</p>
                                </div>
                            </div>
                        </div>

                        <!-- 物料资源 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">物料种类</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ mockData.materials.length }}</p>
                                    <p class="text-sm text-green-600">{{ getSufficientCount(mockData.materials) }} 种充足</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资源详情 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 设备列表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">设备状态</h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div v-for="equipment in mockData.equipment" :key="equipment.id"
                                         class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ equipment.name }}</h4>
                                            <p class="text-sm text-gray-600">{{ equipment.type }}</p>
                                        </div>
                                        <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusClass(equipment.status)]">
                                            {{ getStatusText(equipment.status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 人员列表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">人员状态</h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div v-for="person in mockData.personnel" :key="person.id"
                                         class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ person.name }}</h4>
                                            <p class="text-sm text-gray-600">{{ person.department }}</p>
                                        </div>
                                        <span :class="['px-2 py-1 text-xs font-medium rounded-full', getStatusClass(person.status)]">
                                            {{ getStatusText(person.status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 物料列表 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900">物料库存</h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div v-for="material in mockData.materials" :key="material.id"
                                         class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ material.name }}</h4>
                                            <p class="text-sm text-gray-600">库存: {{ material.currentStock }} {{ material.unit }}</p>
                                        </div>
                                        <span :class="['px-2 py-1 text-xs font-medium rounded-full', getMaterialStatusClass(material)]">
                                            {{ getMaterialStatusText(material) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入组件和脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/schedule/resources.js"></script>
</body>
</html>
