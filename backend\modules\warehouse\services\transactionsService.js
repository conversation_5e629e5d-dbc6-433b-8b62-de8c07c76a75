/**
 * 库存事务服务层
 * 处理所有库存事务相关的业务逻辑
 */

const { postgresConnectionPool } = require('../../../utils/postgresConnectionPool');
const logger = require('../../../utils/logger');

class TransactionsService {
    constructor() {
        this.db = postgresConnectionPool;
    }

    /**
     * 创建库存事务
     */
    async createTransaction(transactionData) {
        try {
            const {
                transaction_type,
                item_type,
                item_id,
                quantity,
                batch_number,
                qrcode,
                notes,
                operator_id
            } = transactionData;

            // 生成事务ID
            const transaction_id = this.generateTransactionId(transaction_type);

            const result = await this.db.query(`
                INSERT INTO warehouse_inventory_transactions (
                    transaction_id, transaction_type, item_type, item_id,
                    quantity, batch_number, qrcode, notes, operator_id,
                    transaction_date, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
                RETURNING id
            `, [
                transaction_id,
                transaction_type,
                item_type,
                item_id,
                quantity,
                batch_number,
                qrcode,
                notes,
                operator_id
            ]);

            logger.info(`库存事务创建成功: ${transaction_id}`, {
                transaction_type,
                item_type,
                item_id,
                quantity,
                operator_id
            });

            return {
                id: result.rows[0].id,
                transaction_id,
                transaction_type,
                item_type,
                item_id,
                quantity
            };
        } catch (error) {
            logger.error('创建库存事务失败:', error);
            throw error;
        }
    }

    /**
     * 获取库存事务列表
     */
    async getTransactions(filters = {}) {
        try {
            let query = `
                SELECT t.*, 
                       u.username as operator_name,
                       CASE 
                           WHEN t.item_type = 'material' THEN m.material_name
                           WHEN t.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE 
                           WHEN t.item_type = 'material' THEN m.material_code
                           WHEN t.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_inventory_transactions t
                LEFT JOIN users u ON t.operator_id = u.id
                LEFT JOIN warehouse_materials m ON t.item_type = 'material' AND t.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON t.item_type = 'product' AND t.item_id = p.id
                WHERE 1=1
            `;

            const params = [];
            let paramIndex = 1;

            if (filters.transaction_type) {
                query += ` AND t.transaction_type = $${paramIndex++}`;
                params.push(filters.transaction_type);
            }

            if (filters.item_type) {
                query += ` AND t.item_type = $${paramIndex++}`;
                params.push(filters.item_type);
            }

            if (filters.item_id) {
                query += ` AND t.item_id = $${paramIndex++}`;
                params.push(filters.item_id);
            }

            if (filters.operator_id) {
                query += ` AND t.operator_id = $${paramIndex++}`;
                params.push(filters.operator_id);
            }

            if (filters.start_date) {
                query += ` AND t.transaction_date >= $${paramIndex++}`;
                params.push(filters.start_date);
            }

            if (filters.end_date) {
                query += ` AND t.transaction_date <= $${paramIndex++}`;
                params.push(filters.end_date);
            }

            if (filters.qrcode) {
                query += ` AND t.qrcode = $${paramIndex++}`;
                params.push(filters.qrcode);
            }

            query += ' ORDER BY t.transaction_date DESC, t.created_at DESC';

            if (filters.limit) {
                query += ` LIMIT $${params.length + 1}`;
                params.push(parseInt(filters.limit));
            }

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('获取库存事务列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取库存事务详情
     */
    async getTransactionById(id) {
        try {
            const result = await this.db.query(`
                SELECT t.*,
                       u.username as operator_name,
                       CASE
                           WHEN t.item_type = 'material' THEN m.material_name
                           WHEN t.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE
                           WHEN t.item_type = 'material' THEN m.material_code
                           WHEN t.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_inventory_transactions t
                LEFT JOIN users u ON t.operator_id = u.id
                LEFT JOIN warehouse_materials m ON t.item_type = 'material' AND t.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON t.item_type = 'product' AND t.item_id = p.id
                WHERE t.id = $1
            `, [id]);

            if (result.rows.length === 0) {
                throw new Error('库存事务不存在');
            }

            return result.rows[0];
        } catch (error) {
            logger.error('获取库存事务详情失败:', error);
            throw error;
        }
    }

    /**
     * 根据物品获取库存事务
     */
    async getTransactionsByItem(itemType, itemId, filters = {}) {
        try {
            let query = `
                SELECT t.*,
                       u.username as operator_name
                FROM warehouse_inventory_transactions t
                LEFT JOIN users u ON t.operator_id = u.id
                WHERE t.item_type = $1 AND t.item_id = $2
            `;

            const params = [itemType, itemId];
            let paramIndex = 3;

            if (filters.transaction_type) {
                query += ` AND t.transaction_type = $${paramIndex++}`;
                params.push(filters.transaction_type);
            }

            if (filters.start_date) {
                query += ` AND t.transaction_date >= $${paramIndex++}`;
                params.push(filters.start_date);
            }

            if (filters.end_date) {
                query += ` AND t.transaction_date <= $${paramIndex++}`;
                params.push(filters.end_date);
            }

            query += ' ORDER BY t.transaction_date DESC, t.created_at DESC';

            if (filters.limit) {
                query += ` LIMIT $${params.length + 1}`;
                params.push(parseInt(filters.limit));
            }

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('根据物品获取库存事务失败:', error);
            throw error;
        }
    }

    /**
     * 获取库存事务统计
     */
    async getTransactionStats(filters = {}) {
        try {
            let query = `
                SELECT 
                    transaction_type,
                    item_type,
                    COUNT(*) as transaction_count,
                    SUM(quantity) as total_quantity,
                    DATE(transaction_date) as transaction_date
                FROM warehouse_inventory_transactions
                WHERE 1=1
            `;

            const params = [];
            let paramIndex = 1;

            if (filters.start_date) {
                query += ` AND transaction_date >= $${paramIndex++}`;
                params.push(filters.start_date);
            }

            if (filters.end_date) {
                query += ` AND transaction_date <= $${paramIndex++}`;
                params.push(filters.end_date);
            }

            if (filters.item_type) {
                query += ` AND item_type = $${paramIndex++}`;
                params.push(filters.item_type);
            }

            query += ' GROUP BY transaction_type, item_type, DATE(transaction_date)';
            query += ' ORDER BY transaction_date DESC';

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('获取库存事务统计失败:', error);
            throw error;
        }
    }

    /**
     * 生成事务ID
     */
    generateTransactionId(transactionType) {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, ''); // HHMMSS
        const randomStr = Math.random().toString(36).substr(2, 4).toUpperCase(); // 4位随机字符

        const typePrefix = {
            'inbound': 'IN',
            'outbound': 'OUT',
            'return': 'RET'
        };

        return `${typePrefix[transactionType] || 'TXN'}-${dateStr}-${timeStr}-${randomStr}`;
    }

    /**
     * 获取物品的当前库存
     */
    async getCurrentStock(itemType, itemId) {
        try {
            const result = await this.db.query(`
                SELECT
                    COALESCE(SUM(CASE WHEN transaction_type = 'inbound' THEN quantity ELSE 0 END), 0) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'outbound' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'return' THEN quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_inventory_transactions
                WHERE item_type = $1 AND item_id = $2
            `, [itemType, itemId]);

            return result.rows.length > 0 ? result.rows[0].current_stock : 0;
        } catch (error) {
            logger.error('获取当前库存失败:', error);
            throw error;
        }
    }

    /**
     * 获取批次库存
     */
    async getBatchStock(itemType, itemId, batchNumber) {
        try {
            const result = await this.db.query(`
                SELECT
                    batch_number,
                    COALESCE(SUM(CASE WHEN transaction_type = 'inbound' THEN quantity ELSE 0 END), 0) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'outbound' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'return' THEN quantity ELSE 0 END), 0) as batch_stock
                FROM warehouse_inventory_transactions
                WHERE item_type = $1 AND item_id = $2 AND batch_number = $3
                GROUP BY batch_number
            `, [itemType, itemId, batchNumber]);

            return result.rows.length > 0 ? result.rows[0].batch_stock : 0;
        } catch (error) {
            logger.error('获取批次库存失败:', error);
            throw error;
        }
    }
}

module.exports = new TransactionsService();
