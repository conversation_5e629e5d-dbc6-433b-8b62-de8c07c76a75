/**
 * 测试样品单上传功能
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// 测试配置
const API_BASE_URL = 'http://localhost:3000/api/file-management';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjE0NzIxNzAzMzIiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNzIzMzY5NzI5LCJleHAiOjE3MjM0NTYxMjl9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // 请替换为有效的token

async function testSampleUpload() {
    try {
        console.log('🧪 开始测试样品单上传功能...');

        // 创建测试文件
        const testFilePath = path.join(__dirname, 'test-sample-file.txt');
        fs.writeFileSync(testFilePath, '这是一个测试样品单文件\n测试时间: ' + new Date().toISOString());

        // 创建FormData
        const formData = new FormData();

        // 添加样品单基本信息
        formData.append('companyName', '测试公司');
        formData.append('recipient', '测试收件人');
        formData.append('application', '13360670195');
        formData.append('address', '测试地址');
        formData.append('productModel', '测试产品型号');
        formData.append('quantity', '10');
        formData.append('date', '2025-08-11');
        formData.append('purpose', '测试目的');
        formData.append('valve', 'none');
        formData.append('valvePrint', 'none');
        formData.append('adjustmentBuckle', 'none');
        formData.append('internalTest', 'yes');
        formData.append('colorBox', 'true');
        formData.append('tape', 'false');
        formData.append('blisterPack', 'false');
        formData.append('other', 'false');
        formData.append('testItems', JSON.stringify(['NIOSH美规']));
        formData.append('testLevels', JSON.stringify({'NIOSH美规': 'N95'}));
        formData.append('testSampleCounts', JSON.stringify({'NIOSH美规': 5}));
        formData.append('otherTestItems', JSON.stringify({}));
        formData.append('recipients', JSON.stringify([{
            id: '5024662759',
            username: 'slp',
            email: '<EMAIL>',
            type: 'to'
        }]));
        formData.append('ccRecipients', JSON.stringify([]));

        // 添加测试文件
        formData.append('maskPrintFiles', fs.createReadStream(testFilePath), {
            filename: '测试口罩印字文件.txt',
            contentType: 'text/plain'
        });

        // 发送请求
        const response = await axios.post(`${API_BASE_URL}/sample`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${TEST_TOKEN}`
            },
            timeout: 30000
        });

        console.log('✅ 样品单上传成功!');
        console.log('📋 响应数据:', JSON.stringify(response.data, null, 2));

        // 检查文件是否真的保存了
        const sampleId = response.data.data.id;
        console.log('🔍 检查文件是否保存到磁盘...');
        
        // 查找上传目录
        const uploadsDir = path.join(__dirname, '../uploads/samples');
        if (fs.existsSync(uploadsDir)) {
            console.log('📁 samples目录存在');
            
            // 递归查找所有文件
            function findFiles(dir, files = []) {
                const items = fs.readdirSync(dir);
                for (const item of items) {
                    const fullPath = path.join(dir, item);
                    if (fs.statSync(fullPath).isDirectory()) {
                        findFiles(fullPath, files);
                    } else {
                        files.push(fullPath);
                    }
                }
                return files;
            }
            
            const allFiles = findFiles(uploadsDir);
            console.log('📄 找到的文件:', allFiles);
            
            if (allFiles.length > 0) {
                console.log('✅ 文件已成功保存到磁盘!');
            } else {
                console.log('❌ 没有找到保存的文件');
            }
        } else {
            console.log('❌ samples目录不存在');
        }

        // 清理测试文件
        fs.unlinkSync(testFilePath);
        console.log('🧹 测试文件已清理');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('📋 错误响应:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    testSampleUpload();
}

module.exports = testSampleUpload;
