/**
 * PostgreSQL基础Repository类
 * 提供通用的数据库操作方法，替换SQLite的预编译语句
 */

const { postgresConnectionPool } = require('../utils/postgresConnectionPool');
const logger = require('../utils/logger');

class BasePostgresRepository {
    constructor() {
        this.pool = postgresConnectionPool;
    }

    /**
     * 执行查询
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     * @returns {Promise} 查询结果
     */
    async query(text, params = []) {
        try {
            const result = await this.pool.query(text, params);
            return result;
        } catch (error) {
            logger.error('数据库查询失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 执行事务
     * @param {Function} callback 事务回调函数
     * @returns {Promise} 事务结果
     */
    async transaction(callback) {
        return await this.pool.transaction(callback);
    }

    /**
     * 查找单条记录
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     * @returns {Promise<Object|null>} 查询结果
     */
    async findOne(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rows.length > 0 ? result.rows[0] : null;
        } catch (error) {
            logger.error('查找单条记录失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 查找多条记录
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     * @returns {Promise<Array>} 查询结果
     */
    async findMany(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rows;
        } catch (error) {
            logger.error('查找多条记录失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 插入记录
     * @param {string} text SQL插入语句
     * @param {Array} params 插入参数
     * @returns {Promise<Object>} 插入结果
     */
    async insert(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rows[0] || { rowCount: result.rowCount };
        } catch (error) {
            logger.error('插入记录失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 更新记录
     * @param {string} text SQL更新语句
     * @param {Array} params 更新参数
     * @returns {Promise<number>} 影响的行数
     */
    async update(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rowCount;
        } catch (error) {
            logger.error('更新记录失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 删除记录
     * @param {string} text SQL删除语句
     * @param {Array} params 删除参数
     * @returns {Promise<number>} 影响的行数
     */
    async delete(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rowCount;
        } catch (error) {
            logger.error('删除记录失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 检查记录是否存在
     * @param {string} text SQL查询语句
     * @param {Array} params 查询参数
     * @returns {Promise<boolean>} 是否存在
     */
    async exists(text, params = []) {
        try {
            const result = await this.query(text, params);
            return result.rows.length > 0;
        } catch (error) {
            logger.error('检查记录存在失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 获取记录总数
     * @param {string} text SQL计数语句
     * @param {Array} params 查询参数
     * @returns {Promise<number>} 记录总数
     */
    async count(text, params = []) {
        try {
            const result = await this.query(text, params);
            return parseInt(result.rows[0].count) || 0;
        } catch (error) {
            logger.error('获取记录总数失败:', { text, params, error: error.message });
            throw error;
        }
    }

    /**
     * 批量插入
     * @param {string} table 表名
     * @param {Array} columns 列名数组
     * @param {Array} values 值数组
     * @returns {Promise<number>} 插入的行数
     */
    async batchInsert(table, columns, values) {
        if (!values || values.length === 0) {
            return 0;
        }

        try {
            const placeholders = values.map((_, index) => {
                const start = index * columns.length + 1;
                const end = start + columns.length - 1;
                const params = Array.from({ length: columns.length }, (_, i) => `$${start + i}`);
                return `(${params.join(', ')})`;
            }).join(', ');

            const text = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders}`;
            const params = values.flat();

            const result = await this.query(text, params);
            return result.rowCount;
        } catch (error) {
            logger.error('批量插入失败:', { table, columns, error: error.message });
            throw error;
        }
    }

    /**
     * 分页查询
     * @param {string} baseQuery 基础查询语句
     * @param {Array} baseParams 基础查询参数
     * @param {number} page 页码
     * @param {number} limit 每页数量
     * @param {string} orderBy 排序字段
     * @returns {Promise<Object>} 分页结果
     */
    async paginate(baseQuery, baseParams = [], page = 1, limit = 10, orderBy = '') {
        try {
            // 获取总数
            const countQuery = `SELECT COUNT(*) FROM (${baseQuery}) as count_query`;
            const countResult = await this.query(countQuery, baseParams);
            const total = parseInt(countResult.rows[0].count);

            // 获取分页数据
            const offset = (page - 1) * limit;
            const dataQuery = `${baseQuery} ${orderBy} LIMIT $${baseParams.length + 1} OFFSET $${baseParams.length + 2}`;
            const dataParams = [...baseParams, limit, offset];
            const dataResult = await this.query(dataQuery, dataParams);

            return {
                data: dataResult.rows,
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            logger.error('分页查询失败:', { baseQuery, baseParams, page, limit, error: error.message });
            throw error;
        }
    }

    /**
     * 生成UUID
     * @returns {Promise<string>} UUID
     */
    async generateUUID() {
        try {
            const result = await this.query('SELECT gen_random_uuid() as uuid');
            return result.rows[0].uuid;
        } catch (error) {
            // 如果没有uuid-ossp扩展，使用时间戳+随机数
            return Date.now().toString() + Math.random().toString(36).substr(2, 9);
        }
    }

    /**
     * 生成数字ID
     * @returns {string} 10位数字ID
     */
    generateId() {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return (timestamp + random).substr(-10);
    }

    /**
     * 转换日期格式
     * @param {Date|string} date 日期
     * @returns {string} ISO格式日期字符串
     */
    formatDate(date) {
        if (!date) return null;
        if (date instanceof Date) {
            return date.toISOString();
        }
        return new Date(date).toISOString();
    }

    /**
     * 解析JSON字段
     * @param {string|object} jsonData JSON字符串或对象
     * @param {*} defaultValue 默认值
     * @returns {*} 解析后的值
     */
    parseJSON(jsonData, defaultValue = null) {
        if (!jsonData) return defaultValue;

        // 如果已经是对象，直接返回（PostgreSQL JSONB字段）
        if (typeof jsonData === 'object') {
            return jsonData;
        }

        // 如果是字符串，尝试解析
        if (typeof jsonData === 'string') {
            try {
                return JSON.parse(jsonData);
            } catch (error) {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.warn('JSON解析失败:', { jsonData: jsonData.substring(0, 100), error: error.message });
                }
                return defaultValue;
            }
        }

        return defaultValue;
    }
}

module.exports = BasePostgresRepository;
