/**
 * 设备产能服务层
 * 处理设备产能相关的业务逻辑
 */

const { databaseAdapter } = require('../database/databaseAdapter');
const { EquipmentCapabilityModel, OperatorSkillModel, EquipmentOperatorModel } = require('../models/capacityModel');
const logger = require('../utils/logger');

class CapacityService {
    constructor() {
        this.capacityRepository = databaseAdapter.getCapacityRepository();
    }

    /**
     * 创建设备产能记录
     * @param {Object} capabilityData 设备产能数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createEquipmentCapability(capabilityData, userId) {
        try {
            // 生成产能记录ID
            const capabilityId = EquipmentCapabilityModel.generateId();
            
            // 创建产能模型
            const capability = new EquipmentCapabilityModel({
                ...capabilityData,
                id: capabilityId
            });

            // 验证数据
            const validation = capability.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 检查是否已存在相同的设备-产品组合
            const existingCapability = await this.capacityRepository.getEquipmentCapability(
                capability.equipmentId, 
                capability.productId
            );
            
            if (existingCapability) {
                return {
                    success: false,
                    message: '该设备的产品产能配置已存在'
                };
            }

            // 创建产能记录
            const createdCapability = await this.capacityRepository.createCapability(capability);

            logger.info('设备产能记录创建成功', { 
                capabilityId: createdCapability.id, 
                equipmentId: createdCapability.equipmentId,
                productId: createdCapability.productId,
                createdBy: userId 
            });

            return {
                success: true,
                message: '设备产能记录创建成功',
                data: createdCapability
            };
        } catch (error) {
            logger.error('设备产能记录创建失败', { error: error.message, userId });
            return {
                success: false,
                message: '设备产能记录创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取设备的产能配置
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} 产能配置结果
     */
    async getEquipmentCapabilities(equipmentId) {
        try {
            const capabilities = await this.capacityRepository.getEquipmentCapabilities(equipmentId);

            logger.info('获取设备产能配置成功', { 
                equipmentId, 
                capabilityCount: capabilities.length 
            });

            return {
                success: true,
                data: capabilities
            };
        } catch (error) {
            logger.error('获取设备产能配置失败', { error: error.message, equipmentId });
            return {
                success: false,
                message: '获取设备产能配置失败',
                error: error.message
            };
        }
    }

    /**
     * 获取产品的可用设备
     * @param {string} productId 产品ID
     * @returns {Promise<Object>} 可用设备结果
     */
    async getAvailableEquipmentForProduct(productId) {
        try {
            const equipment = await this.capacityRepository.getAvailableEquipmentForProduct(productId);

            logger.info('获取产品可用设备成功', { 
                productId, 
                equipmentCount: equipment.length 
            });

            return {
                success: true,
                data: equipment
            };
        } catch (error) {
            logger.error('获取产品可用设备失败', { error: error.message, productId });
            return {
                success: false,
                message: '获取产品可用设备失败',
                error: error.message
            };
        }
    }

    /**
     * 创建操作员技能记录
     * @param {Object} skillData 操作员技能数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createOperatorSkill(skillData, userId) {
        try {
            // 生成技能记录ID
            const skillId = OperatorSkillModel.generateId();
            
            // 创建技能模型
            const skill = new OperatorSkillModel({
                ...skillData,
                id: skillId
            });

            // 验证数据
            const validation = skill.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 检查是否已存在相同的操作员-设备组合
            const existingSkill = await this.capacityRepository.getOperatorSkill(
                skill.operatorId, 
                skill.equipmentId
            );
            
            if (existingSkill) {
                return {
                    success: false,
                    message: '该操作员的设备技能记录已存在'
                };
            }

            // 创建技能记录
            const createdSkill = await this.capacityRepository.createOperatorSkill(skill);

            logger.info('操作员技能记录创建成功', { 
                skillId: createdSkill.id, 
                operatorId: createdSkill.operatorId,
                equipmentId: createdSkill.equipmentId,
                createdBy: userId 
            });

            return {
                success: true,
                message: '操作员技能记录创建成功',
                data: createdSkill
            };
        } catch (error) {
            logger.error('操作员技能记录创建失败', { error: error.message, userId });
            return {
                success: false,
                message: '操作员技能记录创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取设备的操作员技能
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} 操作员技能结果
     */
    async getEquipmentOperatorSkills(equipmentId) {
        try {
            const skills = await this.capacityRepository.getEquipmentOperatorSkills(equipmentId);

            logger.info('获取设备操作员技能成功', { 
                equipmentId, 
                skillCount: skills.length 
            });

            return {
                success: true,
                data: skills
            };
        } catch (error) {
            logger.error('获取设备操作员技能失败', { error: error.message, equipmentId });
            return {
                success: false,
                message: '获取设备操作员技能失败',
                error: error.message
            };
        }
    }

    /**
     * 获取操作员的技能列表
     * @param {string} operatorId 操作员ID
     * @returns {Promise<Object>} 操作员技能结果
     */
    async getOperatorSkills(operatorId) {
        try {
            const skills = await this.capacityRepository.getOperatorSkills(operatorId);

            logger.info('获取操作员技能列表成功', { 
                operatorId, 
                skillCount: skills.length 
            });

            return {
                success: true,
                data: skills
            };
        } catch (error) {
            logger.error('获取操作员技能列表失败', { error: error.message, operatorId });
            return {
                success: false,
                message: '获取操作员技能列表失败',
                error: error.message
            };
        }
    }

    /**
     * 创建设备操作员关联
     * @param {Object} relationData 关联数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createEquipmentOperator(relationData, userId) {
        try {
            // 生成关联ID
            const relationId = EquipmentOperatorModel.generateId();
            
            // 创建关联模型
            const relation = new EquipmentOperatorModel({
                ...relationData,
                id: relationId
            });

            // 验证数据
            const validation = relation.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 创建关联记录
            const createdRelation = await this.capacityRepository.createEquipmentOperator(relation);

            logger.info('设备操作员关联创建成功', { 
                relationId: createdRelation.id, 
                equipmentId: createdRelation.equipmentId,
                operatorId: createdRelation.operatorId,
                createdBy: userId 
            });

            return {
                success: true,
                message: '设备操作员关联创建成功',
                data: createdRelation
            };
        } catch (error) {
            logger.error('设备操作员关联创建失败', { error: error.message, userId });
            return {
                success: false,
                message: '设备操作员关联创建失败',
                error: error.message
            };
        }
    }

    /**
     * 获取设备的操作员列表
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} 操作员列表结果
     */
    async getEquipmentOperators(equipmentId) {
        try {
            const operators = await this.capacityRepository.getEquipmentOperators(equipmentId);

            logger.info('获取设备操作员列表成功', { 
                equipmentId, 
                operatorCount: operators.length 
            });

            return {
                success: true,
                data: operators
            };
        } catch (error) {
            logger.error('获取设备操作员列表失败', { error: error.message, equipmentId });
            return {
                success: false,
                message: '获取设备操作员列表失败',
                error: error.message
            };
        }
    }

    /**
     * 更新设备产能
     * @param {string} capabilityId 产能记录ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 更新用户ID
     * @returns {Promise<Object>} 更新结果
     */
    async updateEquipmentCapability(capabilityId, updateData, userId) {
        try {
            const updatedCapability = await this.capacityRepository.updateCapability(capabilityId, updateData);

            if (!updatedCapability) {
                return {
                    success: false,
                    message: '设备产能记录不存在'
                };
            }

            logger.info('设备产能更新成功', { 
                capabilityId, 
                updatedBy: userId 
            });

            return {
                success: true,
                message: '设备产能更新成功',
                data: updatedCapability
            };
        } catch (error) {
            logger.error('设备产能更新失败', { error: error.message, capabilityId, userId });
            return {
                success: false,
                message: '设备产能更新失败',
                error: error.message
            };
        }
    }

    /**
     * 更新设备产能记录
     * @param {string} id 产能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateEquipmentCapability(id, updateData) {
        try {
            const updatedCapability = await this.capacityRepository.updateCapability(id, updateData);

            if (!updatedCapability) {
                return {
                    success: false,
                    message: '设备产能记录不存在'
                };
            }

            logger.info('设备产能记录更新成功', { capabilityId: id });

            return {
                success: true,
                data: updatedCapability,
                message: '设备产能记录更新成功'
            };
        } catch (error) {
            logger.error('更新设备产能记录失败', { error: error.message, capabilityId: id });
            return {
                success: false,
                message: '更新设备产能记录失败',
                error: error.message
            };
        }
    }

    /**
     * 删除设备产能记录
     * @param {string} id 产能记录ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteEquipmentCapability(id) {
        try {
            const deleted = await this.capacityRepository.deleteCapability(id);

            if (!deleted) {
                return {
                    success: false,
                    message: '设备产能记录不存在'
                };
            }

            logger.info('设备产能记录删除成功', { capabilityId: id });

            return {
                success: true,
                message: '设备产能记录删除成功'
            };
        } catch (error) {
            logger.error('删除设备产能记录失败', { error: error.message, capabilityId: id });
            return {
                success: false,
                message: '删除设备产能记录失败',
                error: error.message
            };
        }
    }

    /**
     * 获取设备的操作员技能
     * @param {string} equipmentId 设备ID
     * @returns {Promise<Object>} 操作员技能列表
     */
    async getEquipmentOperatorSkills(equipmentId) {
        try {
            const skills = await this.capacityRepository.getEquipmentOperatorSkills(equipmentId);

            logger.info('获取设备操作员技能成功', {
                equipmentId,
                skillCount: skills.length
            });

            return {
                success: true,
                data: skills
            };
        } catch (error) {
            logger.error('获取设备操作员技能失败', { error: error.message, equipmentId });
            return {
                success: false,
                message: '获取设备操作员技能失败',
                error: error.message
            };
        }
    }

    /**
     * 创建操作员技能记录
     * @param {Object} skillData 技能数据
     * @returns {Promise<Object>} 创建结果
     */
    async createOperatorSkill(skillData) {
        try {
            // 生成技能记录ID
            const skillId = OperatorSkillModel.generateId();

            // 创建技能模型
            const skill = new OperatorSkillModel({
                ...skillData,
                id: skillId
            });

            // 验证数据
            const validation = skill.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 创建技能记录
            const createdSkill = await this.capacityRepository.createOperatorSkill(skill);

            logger.info('操作员技能记录创建成功', {
                skillId: createdSkill.id,
                operatorId: createdSkill.operatorId,
                equipmentId: createdSkill.equipmentId
            });

            return {
                success: true,
                data: createdSkill,
                message: '操作员技能记录创建成功'
            };
        } catch (error) {
            logger.error('创建操作员技能记录失败', { error: error.message, skillData });
            return {
                success: false,
                message: '创建操作员技能记录失败',
                error: error.message
            };
        }
    }

    /**
     * 更新操作员技能记录
     * @param {string} id 技能记录ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateOperatorSkill(id, updateData) {
        try {
            const updatedSkill = await this.capacityRepository.updateOperatorSkill(id, updateData);

            if (!updatedSkill) {
                return {
                    success: false,
                    message: '操作员技能记录不存在'
                };
            }

            logger.info('操作员技能记录更新成功', { skillId: id });

            return {
                success: true,
                data: updatedSkill,
                message: '操作员技能记录更新成功'
            };
        } catch (error) {
            logger.error('更新操作员技能记录失败', { error: error.message, skillId: id });
            return {
                success: false,
                message: '更新操作员技能记录失败',
                error: error.message
            };
        }
    }

    /**
     * 删除操作员技能记录
     * @param {string} id 技能记录ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteOperatorSkill(id) {
        try {
            const deleted = await this.capacityRepository.deleteOperatorSkill(id);

            if (!deleted) {
                return {
                    success: false,
                    message: '操作员技能记录不存在'
                };
            }

            logger.info('操作员技能记录删除成功', { skillId: id });

            return {
                success: true,
                message: '操作员技能记录删除成功'
            };
        } catch (error) {
            logger.error('删除操作员技能记录失败', { error: error.message, skillId: id });
            return {
                success: false,
                message: '删除操作员技能记录失败',
                error: error.message
            };
        }
    }

    /**
     * 创建设备操作员关联
     * @param {Object} relationData 关联数据
     * @returns {Promise<Object>} 创建结果
     */
    async createEquipmentOperator(relationData) {
        try {
            // 生成关联记录ID
            const relationId = EquipmentOperatorModel.generateId();

            // 创建关联模型
            const relation = new EquipmentOperatorModel({
                ...relationData,
                id: relationId
            });

            // 验证数据
            const validation = relation.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 创建关联记录
            const createdRelation = await this.capacityRepository.createEquipmentOperator(relation);

            logger.info('设备操作员关联创建成功', {
                relationId: createdRelation.id,
                equipmentId: createdRelation.equipmentId,
                operatorId: createdRelation.operatorId
            });

            return {
                success: true,
                data: createdRelation,
                message: '设备操作员关联创建成功'
            };
        } catch (error) {
            logger.error('创建设备操作员关联失败', { error: error.message, relationData });
            return {
                success: false,
                message: '创建设备操作员关联失败',
                error: error.message
            };
        }
    }
}

module.exports = CapacityService;
