/**
 * PostgreSQL数据库迁移管理器
 * 负责执行数据库结构迁移
 */

const logger = require('../utils/logger');
const { Pool } = require('pg');
require('dotenv').config();

class MigrationManager {
    constructor() {
        // 创建PostgreSQL连接池
        this.pool = new Pool({
            host: process.env.POSTGRES_HOST || 'localhost',
            port: process.env.POSTGRES_PORT || 5432,
            user: process.env.POSTGRES_USER || 'postgres',
            password: process.env.POSTGRES_PASSWORD,
            database: process.env.POSTGRES_DATABASE || 'makrite_system',
            max: parseInt(process.env.POSTGRES_POOL_MAX) || 20,
            min: parseInt(process.env.POSTGRES_POOL_MIN) || 2,
            idleTimeoutMillis: parseInt(process.env.POSTGRES_POOL_IDLE_TIMEOUT) || 30000,
            connectionTimeoutMillis: parseInt(process.env.POSTGRES_POOL_CONNECTION_TIMEOUT) || 2000,
        });

        // 迁移脚本数组 - 当前为空，因为所有表结构已在initializeDatabase.js中创建
        // 未来如需增量迁移，可在此添加新的迁移脚本
        this.migrations = [];
    }

    /**
     * 初始化迁移记录表
     */
    async initMigrationTable() {
        try {
            await this.pool.query(`
                CREATE TABLE IF NOT EXISTS migrations (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(500) NOT NULL,
                    executed_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    success BOOLEAN NOT NULL DEFAULT true,
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
                )
            `);
            logger.debug('迁移记录表初始化完成');
        } catch (error) {
            logger.error('初始化迁移记录表失败:', error);
            throw error;
        }
    }

    /**
     * 检查迁移是否已执行
     */
    async isMigrationExecuted(migrationId) {
        try {
            const result = await this.pool.query('SELECT id FROM migrations WHERE id = $1 AND success = true', [migrationId]);
            return result.rows.length > 0;
        } catch (error) {
            logger.error(`检查迁移状态失败 (${migrationId}):`, error);
            return false;
        }
    }

    /**
     * 记录迁移执行结果
     */
    async recordMigration(migrationId, name, success = true) {
        try {
            const now = new Date();
            await this.pool.query(`
                INSERT INTO migrations (id, name, executed_at, success, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    executed_at = EXCLUDED.executed_at,
                    success = EXCLUDED.success,
                    updated_at = EXCLUDED.updated_at
            `, [migrationId, name, now, success, now, now]);
        } catch (error) {
            logger.error(`记录迁移结果失败 (${migrationId}):`, error);
            throw error;
        }
    }

    /**
     * 执行所有待执行的迁移
     */
    async runMigrations() {
        try {
            // 初始化迁移记录表
            await this.initMigrationTable();

            // 检查是否有迁移脚本
            if (this.migrations.length === 0) {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info('没有待执行的迁移脚本');
                }
                return;
            }

            // 只在详细日志模式下显示迁移检查信息
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('开始检查数据库迁移...');
            }

            let executedCount = 0;

            for (const migrationConfig of this.migrations) {
                const { id, name, migration: MigrationClass } = migrationConfig;

                if (await this.isMigrationExecuted(id)) {
                    logger.debug(`迁移 ${id} 已执行，跳过`);
                    continue;
                }

                try {
                    if (process.env.VERBOSE_LOGS === 'true') {
                        logger.info(`执行迁移: ${name} (${id})`);
                    }

                    const migration = new MigrationClass(this.pool);
                    await migration.migrate();

                    await this.recordMigration(id, name, true);
                    executedCount++;

                    if (process.env.VERBOSE_LOGS === 'true') {
                        logger.info(`迁移 ${id} 执行成功`);
                    }

                } catch (error) {
                    logger.error(`迁移 ${id} 执行失败:`, error);
                    await this.recordMigration(id, name, false);
                    throw error;
                }
            }

            if (executedCount > 0) {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info(`数据库迁移完成，共执行 ${executedCount} 个迁移`);
                }
            } else {
                // 只在详细日志模式下显示无需迁移信息
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info('数据库已是最新版本，无需迁移');
                }
            }
        } catch (error) {
            logger.error('数据库迁移执行失败:', error);
            throw error;
        }
    }

    /**
     * 获取迁移历史
     */
    async getMigrationHistory() {
        try {
            const result = await this.pool.query('SELECT * FROM migrations ORDER BY executed_at DESC');
            return result.rows;
        } catch (error) {
            logger.error('获取迁移历史失败:', error);
            return [];
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const client = await this.pool.connect();
            await client.query('SELECT NOW()');
            client.release();
            return true;
        } catch (error) {
            logger.error('数据库连接测试失败:', error);
            return false;
        }
    }

    /**
     * 关闭数据库连接池
     */
    async close() {
        try {
            await this.pool.end();
            logger.debug('迁移管理器数据库连接池已关闭');
        } catch (error) {
            logger.error('关闭数据库连接池失败:', error);
        }
    }
}

module.exports = MigrationManager;
