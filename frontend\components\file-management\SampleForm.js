/**
 * 样品寄送通知单表单组件
 * 用于填写样品寄送通知单信息
 */

import { getFileManagementUsers, submitSampleForm } from '../../scripts/api/file-management.js';
import eventManager, { EVENTS } from '../../scripts/utils/eventManager.js';

const { createApp, ref, reactive, computed, onMounted } = Vue;

export default {
    name: 'SampleForm',
    setup() {
        // 响应式数据
        const formData = reactive({
            // 收件人信息
            to: '',
            cc: '',

            // 基本信息
            date: '',
            number: '',
            purpose: '',

            // 公司信息
            companyName: '',
            recipient: '',
            application: '',
            address: '',

            // 产品规格信息
            productModel: '',
            productDescription: '',
            outerLayer: '',
            middleLayer: '',
            innerLayer: '',
            headband: '',
            noseBridge: '',
            earLoop: '',

            // 气阀信息
            valve: 'none', // none, with
            valveTopCover: '',
            valveBottomCover: '',
            valveSheet: '',

            // 客制化说明
            quantity: '',
            maskPrintFiles: [], // 改为文件数组
            printColor: '',
            printSize: '',
            headbandColor: '',
            valveColor: '',
            valvePrint: 'none', // none, need
            valvePrintFiles: [], // 改为文件数组
            printMaterialColor: '',
            printMaterialSize: '',

            // 样品内容
            adjustmentBuckle: 'none', // none, with
            internalTest: 'no', // yes, no

            // 包装信息
            packagingMethod: '',
            packagingMaterials: [], // 改为数组，支持多选
            colorBox: false, // 彩盒
            colorBoxFiles: [], // 彩盒图档文件数组
            tape: false, // 胶带
            tapeMaterial: '', // 胶带材质和尺寸
            blisterPack: false, // 泡壳
            blisterPackFiles: [], // 吊卡图档文件数组
            other: false, // 其他
            otherDescription: '', // 其他描述

            // 测试信息
            testNotes: '',
            testQuantity: '',
            testItems: [], // 选中的测试项目
            testLevels: {}, // 测试等级 {项目名: 等级值}
            otherTestItems: {}, // 其他项目的自定义内容 {项目名: 自定义内容}

            // 补充说明
            additionalNotes: ''
        });

        const loading = ref(false);
        const submitting = ref(false);
        const users = ref([]); // 用户列表
        const loadingUsers = ref(false); // 加载用户状态
        const selectedUsers = ref([]); // 选中的用户列表
        const showUserSelection = ref(false); // 是否显示用户选择列表
        const userSearchTerm = ref(''); // 用户搜索关键词
        const selectedCcUsers = ref([]); // 选中的抄送用户列表
        const showCcUserSelection = ref(false); // 是否显示抄送用户选择列表
        const ccUserSearchTerm = ref(''); // 抄送用户搜索关键词

        // 文件上传相关
        const maskPrintFiles = ref([]);
        const valvePrintFiles = ref([]);
        const colorBoxFiles = ref([]);
        const blisterPackFiles = ref([]);

        // 计算属性
        const formattedDate = computed(() => {
            if (!formData.date) return '';
            const date = new Date(formData.date);
            return date.toISOString().split('T')[0].replace(/-/g, '.');
        });

        const generatedNumber = ref('');

        // 获取下一个样品单编号
        async function fetchNextSampleNumber() {
            try {
                const response = await fetch('/api/file-management/sample/next-number', {
                    headers: {
                        'Authorization': `Bearer ${sessionStorage.getItem('authToken')}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        generatedNumber.value = data.data.sampleNumber;
                    }
                } else {
                    console.error('获取样品单编号失败');
                }
            } catch (error) {
                console.error('获取样品单编号失败:', error);
            }
        }

        // 筛选用户列表
        const filteredUsers = computed(() => {
            if (!userSearchTerm.value) {
                return users.value;
            }

            const searchTerm = userSearchTerm.value.toLowerCase();
            return users.value.filter(user =>
                (user.username && user.username.toLowerCase().includes(searchTerm)) ||
                (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                (user.email && user.email.toLowerCase().includes(searchTerm)) ||
                (user.department && user.department.toLowerCase().includes(searchTerm))
            );
        });

        // 筛选抄送用户列表
        const filteredCcUsers = computed(() => {
            if (!ccUserSearchTerm.value) {
                return users.value;
            }

            const searchTerm = ccUserSearchTerm.value.toLowerCase();
            return users.value.filter(user =>
                (user.username && user.username.toLowerCase().includes(searchTerm)) ||
                (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                (user.email && user.email.toLowerCase().includes(searchTerm)) ||
                (user.department && user.department.toLowerCase().includes(searchTerm))
            );
        });

        // 获取用户列表
        async function fetchUsers() {
            try {
                loadingUsers.value = true;
                const response = await getFileManagementUsers();

                if (response.success) {
                    // 只显示有邮箱的活跃用户
                    users.value = response.data.filter(user =>
                        user.active && user.email && user.email.trim() !== ''
                    );
                } else {
                    console.error('获取用户列表失败:', response.message);
                }
            } catch (error) {
                console.error('获取用户列表错误:', error);
            } finally {
                loadingUsers.value = false;
            }
        }

        // 切换用户选择显示
        function toggleUserSelectionDisplay() {
            if (showUserSelection.value) {
                showUserSelection.value = false;
            } else {
                if (users.value.length === 0) {
                    fetchUsers();
                }
                showUserSelection.value = true;
            }
        }

        // 切换用户选择
        function toggleUserSelection(user) {
            const index = selectedUsers.value.findIndex(u => u.id === user.id);
            if (index > -1) {
                selectedUsers.value.splice(index, 1);
            } else {
                selectedUsers.value.push(user);
            }
        }

        // 检查用户是否已选择
        const isUserSelected = (user) => {
            return selectedUsers.value.some(u => u.id === user.id);
        };

        // 切换抄送用户选择显示
        function toggleCcUserSelectionDisplay() {
            if (showCcUserSelection.value) {
                showCcUserSelection.value = false;
            } else {
                if (users.value.length === 0) {
                    fetchUsers();
                }
                showCcUserSelection.value = true;
            }
        }

        // 切换抄送用户选择
        function toggleCcUserSelection(user) {
            const index = selectedCcUsers.value.findIndex(u => u.id === user.id);
            if (index > -1) {
                selectedCcUsers.value.splice(index, 1);
            } else {
                selectedCcUsers.value.push(user);
            }
        }

        // 检查抄送用户是否已选择
        const isCcUserSelected = (user) => {
            return selectedCcUsers.value.some(u => u.id === user.id);
        };

        // 文件上传处理函数
        function handleMaskPrintFileSelect(event) {
            const files = Array.from(event.target.files);
            console.log('口罩印字文件选择:', {
                fileCount: files.length,
                files: files.map(f => ({
                    name: f.name,
                    size: f.size,
                    type: f.type
                }))
            });
            maskPrintFiles.value = [...maskPrintFiles.value, ...files];
            console.log('口罩印字文件总数:', maskPrintFiles.value.length);
        }

        function handleValvePrintFileSelect(event) {
            const files = Array.from(event.target.files);
            valvePrintFiles.value = [...valvePrintFiles.value, ...files];
        }

        function removeMaskPrintFile(index) {
            maskPrintFiles.value.splice(index, 1);
        }

        function removeValvePrintFile(index) {
            valvePrintFiles.value.splice(index, 1);
        }

        function handleColorBoxFileSelect(event) {
            const files = Array.from(event.target.files);
            colorBoxFiles.value = [...colorBoxFiles.value, ...files];
        }

        function handleBlisterPackFileSelect(event) {
            const files = Array.from(event.target.files);
            blisterPackFiles.value = [...blisterPackFiles.value, ...files];
        }

        function removeColorBoxFile(index) {
            colorBoxFiles.value.splice(index, 1);
        }

        function removeBlisterPackFile(index) {
            blisterPackFiles.value.splice(index, 1);
        }

        // 生命周期
        onMounted(() => {
            // 设置默认日期为今天
            const today = new Date();
            formData.date = today.toISOString().split('T')[0];

            // 获取用户列表
            fetchUsers();

            // 获取下一个样品单编号
            fetchNextSampleNumber();

            // 调试：检查组件是否正常加载
            console.log('样品单表单组件已加载');
            console.log('文件数组初始状态:', {
                maskPrintFiles: maskPrintFiles.value.length,
                valvePrintFiles: valvePrintFiles.value.length,
                colorBoxFiles: colorBoxFiles.value.length,
                blisterPackFiles: blisterPackFiles.value.length
            });
        });

        // 方法
        function handleInternalTestChange(value) {
            formData.internalTest = value;
            // 如果选择"否"，清空测试相关数据
            if (value === 'no') {
                formData.testItems = [];
                formData.testNotes = '';
                formData.testQuantity = '';
                formData.testLevels = {};
                formData.otherTestItems = {};
                testSampleCounts.value = {};
            }
        }

        // 定义测试项目层级关系
        const testHierarchy = {
            'NIOSH美规': ['一般穿透阻抗', '加载测试', '头带拉力', '气阀拉力', '密合度测试', '其他NIOSH'],
            'CE欧规': ['一般穿透阻抗CE', '加载测试CE', '全方位阻抗CE', '总泄漏率', '头带拉力CE', '气阀拉力CE', '白云石', '其他CE'],
            'AS/NZS澳规': ['一般穿透阻抗澳规', '全方位阻抗澳规', '头带拉力澳规', '气阀拉力澳规', '其他澳规'],
            '日规': ['粉尘和吸气上升值', '湿阻力', '使用时间', '吸气排气阻力测试', '泄漏率', '其他日规'],
            'GB国标': ['一般穿透阻抗GB', '加载测试GB', '头带拉力GB', '气阀拉力GB', '泄漏测试', '其他GB'],
            '医疗项目': ['血透测试', 'BFE', 'PFE', '压差', '阻燃测试', '其他医疗']
        };

        function handleTestItemChange(item, checked) {
            if (checked) {
                if (!formData.testItems.includes(item)) {
                    formData.testItems.push(item);
                }
            } else {
                const index = formData.testItems.indexOf(item);
                if (index > -1) {
                    formData.testItems.splice(index, 1);
                }

                // 如果取消勾选一级选项，同时取消勾选所有二级选项
                if (testHierarchy[item]) {
                    testHierarchy[item].forEach(subItem => {
                        const subIndex = formData.testItems.indexOf(subItem);
                        if (subIndex > -1) {
                            formData.testItems.splice(subIndex, 1);
                        }
                        // 清除测试等级和其他项目内容
                        delete formData.testLevels[subItem];
                        delete formData.otherTestItems[subItem];
                    });
                }

                // 清除当前项目的测试等级和其他项目内容
                delete formData.testLevels[item];
                delete formData.otherTestItems[item];
            }

            // 更新测试样品数量
            updateTestSampleCounts();
        }

        function handleTestLevelChange(item, level) {
            formData.testLevels[item] = level;
        }

        function handleOtherTestChange(item, content) {
            formData.otherTestItems[item] = content;
        }



        // 测试样品数量的响应式数据
        const testSampleCounts = ref({});

        // 获取二级选框对应的一级选框
        function getMainCategory(subItem) {
            for (const [mainCategory, subItems] of Object.entries(testHierarchy)) {
                if (subItems.includes(subItem)) {
                    return mainCategory;
                }
            }
            return '';
        }

        // 监听测试项目变化，自动更新数量
        function updateTestSampleCounts() {
            const newCounts = {};
            formData.testItems.forEach(item => {
                // 跳过一级选框，只计算二级选框的数量
                const isMainCategory = ['NIOSH美规', 'CE欧规', 'AS/NZS澳规', '日规', 'GB国标', '医疗项目'].includes(item);
                if (!isMainCategory) {
                    // 如果已经有设置的数量，保持不变；否则设置默认数量
                    if (testSampleCounts.value[item]) {
                        newCounts[item] = testSampleCounts.value[item];
                    } else {
                        // 根据不同测试项目设置默认数量
                        if (item.includes('穿透阻抗') || item.includes('加载测试')) {
                            newCounts[item] = 10; // 过滤效率测试需要10个
                        } else if (item.includes('拉力')) {
                            newCounts[item] = 5; // 拉力测试需要5个
                        } else if (item.includes('泄漏') || item.includes('密合度')) {
                            newCounts[item] = 3; // 泄漏测试需要3个
                        } else {
                            newCounts[item] = 5; // 其他测试默认5个
                        }
                    }
                }
            });
            testSampleCounts.value = newCounts;
        }

        // 按一级选框分组显示测试数量
        const groupedTestCounts = computed(() => {
            const grouped = {};
            Object.keys(testSampleCounts.value).forEach(item => {
                const mainCategory = getMainCategory(item);
                if (mainCategory) {
                    if (!grouped[mainCategory]) {
                        grouped[mainCategory] = [];
                    }
                    grouped[mainCategory].push({
                        name: item,
                        count: testSampleCounts.value[item]
                    });
                }
            });
            return grouped;
        });

        function updateTestSampleCount(item, count) {
            // 这个函数用于用户手动调整测试数量
            testSampleCounts.value[item] = parseInt(count) || 1;
        }

        function handlePackagingMaterialChange(material, checked) {
            if (checked) {
                if (!formData.packagingMaterials.includes(material)) {
                    formData.packagingMaterials.push(material);
                }
            } else {
                const index = formData.packagingMaterials.indexOf(material);
                if (index > -1) {
                    formData.packagingMaterials.splice(index, 1);
                }
            }
        }

        function validateForm() {
            const requiredFields = [
                'companyName', 'recipient', 'productModel', 'quantity'
            ];

            for (const field of requiredFields) {
                const value = formData[field];

                // 检查字段是否存在
                if (value === null || value === undefined) {
                    throw new Error(`请填写${getFieldLabel(field)}`);
                }

                // 对字符串类型进行trim检查
                if (typeof value === 'string') {
                    if (value.trim() === '') {
                        throw new Error(`请填写${getFieldLabel(field)}`);
                    }
                } else if (typeof value === 'number') {
                    // 数字类型检查是否为有效数字
                    if (isNaN(value) || value < 0) {
                        throw new Error(`请填写有效的${getFieldLabel(field)}`);
                    }
                } else if (Array.isArray(value)) {
                    // 数组类型检查是否为空
                    if (value.length === 0) {
                        throw new Error(`请选择${getFieldLabel(field)}`);
                    }
                } else {
                    // 其他类型检查是否为空值
                    if (!value) {
                        throw new Error(`请填写${getFieldLabel(field)}`);
                    }
                }
            }

            if (selectedUsers.value.length === 0) {
                throw new Error('请选择至少一个收件人');
            }
        }

        function getFieldLabel(field) {
            const labels = {
                'to': '收件人',
                'companyName': '公司名称',
                'recipient': '收件人',
                'productModel': '产品型号',
                'quantity': '数量'
            };
            return labels[field] || field;
        }

        async function submitForm() {
            try {
                submitting.value = true;

                // 表单验证
                validateForm();

                // 准备提交数据
                const submitData = {
                    // 基本信息
                    ...formData,

                    // 添加生成的编号
                    generatedNumber: generatedNumber.value,

                    // 收件人信息
                    recipients: selectedUsers.value.map(user => ({
                        id: user.id,
                        username: user.username || user.name,
                        email: user.email,
                        type: 'to'
                    })),

                    // 抄送人信息
                    ccRecipients: selectedCcUsers.value.map(user => ({
                        id: user.id,
                        username: user.username || user.name,
                        email: user.email,
                        type: 'cc'
                    })),

                    // 测试样品数量信息
                    testSampleCounts: testSampleCounts.value,

                    // 提交时间（使用本地时间）
                    submittedAt: new Date()
                };

                console.log('提交样品单数据:', submitData);
                console.log('文件上传状态:', {
                    maskPrintFiles: maskPrintFiles.value.length,
                    valvePrintFiles: valvePrintFiles.value.length,
                    colorBoxFiles: colorBoxFiles.value.length,
                    blisterPackFiles: blisterPackFiles.value.length
                });

                // 创建FormData以支持文件上传
                const formDataToSubmit = new FormData();

                // 添加基本数据
                Object.keys(submitData).forEach(key => {
                    if (key !== 'maskPrintFiles' && key !== 'valvePrintFiles' &&
                        key !== 'colorBoxFiles' && key !== 'blisterPackFiles') {
                        const value = submitData[key];

                        if (value === null || value === undefined) {
                            // 跳过空值
                            return;
                        }

                        if (typeof value === 'object') {
                            formDataToSubmit.append(key, JSON.stringify(value));
                        } else {
                            // 确保值被转换为字符串
                            formDataToSubmit.append(key, String(value));
                        }
                    }
                });

                // 调试：检查文件数组状态
                console.log('提交前文件状态检查:', {
                    maskPrintFiles: maskPrintFiles.value.length,
                    valvePrintFiles: valvePrintFiles.value.length,
                    colorBoxFiles: colorBoxFiles.value.length,
                    blisterPackFiles: blisterPackFiles.value.length
                });

                // 添加口罩印字文件
                maskPrintFiles.value.forEach((file, index) => {
                    console.log(`添加口罩印字文件 ${index + 1}:`, {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified
                    });
                    formDataToSubmit.append('maskPrintFiles', file);
                });

                // 添加气阀印字文件
                valvePrintFiles.value.forEach((file, index) => {
                    console.log(`添加气阀印字文件 ${index + 1}:`, {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified
                    });
                    formDataToSubmit.append('valvePrintFiles', file);
                });

                // 添加彩盒图档文件
                colorBoxFiles.value.forEach((file, index) => {
                    console.log(`添加彩盒图档文件 ${index + 1}:`, {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified
                    });
                    formDataToSubmit.append('colorBoxFiles', file);
                });

                // 添加泡壳图档文件
                blisterPackFiles.value.forEach((file, index) => {
                    console.log(`添加泡壳图档文件 ${index + 1}:`, {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified
                    });
                    formDataToSubmit.append('blisterPackFiles', file);
                });

                // 调试：检查FormData内容
                console.log('FormData构建完成，检查内容:');
                for (let [key, value] of formDataToSubmit.entries()) {
                    if (value instanceof File) {
                        console.log(`FormData文件: ${key} = ${value.name} (${value.size} bytes)`);
                    } else {
                        console.log(`FormData数据: ${key} = ${value}`);
                    }
                }

                // 调用后端API提交样品单
                const response = await submitSampleForm(formDataToSubmit);

                if (response.success) {
                    alert('样品单提交成功！邮件通知已发送给相关人员。');

                    // 触发文件列表刷新事件
                    eventManager.emit(EVENTS.SAMPLE_FORM_SUBMITTED, {
                        sampleForm: response.data,
                        timestamp: new Date()
                    });

                    // 延迟一下确保后端处理完成，然后触发刷新
                    setTimeout(() => {
                        eventManager.emit(EVENTS.FILE_LIST_REFRESH, {
                            reason: 'sample_form_submitted',
                            timestamp: new Date()
                        });
                    }, 500);

                    // 可以选择重置表单或跳转到列表页面
                    if (confirm('是否重置表单以填写新的样品单？')) {
                        resetForm();
                    } else {
                        // 跳转到文件列表页面，并添加刷新参数
                        window.location.href = '/file-management/list?refresh=true&action=submit&t=' + Date.now();
                    }
                } else {
                    throw new Error(response.message || '提交失败');
                }

            } catch (error) {
                console.error('提交失败:', error);
                alert('提交失败: ' + (error.response?.data?.message || error.message));
            } finally {
                submitting.value = false;
            }
        }

        function resetForm() {
            Object.keys(formData).forEach(key => {
                if (key === 'testItems' || key === 'packagingMaterials') {
                    formData[key] = [];
                } else if (key === 'testLevels' || key === 'otherTestItems') {
                    formData[key] = {};
                } else if (key === 'date') {
                    const today = new Date();
                    formData[key] = today.toISOString().split('T')[0];
                } else if (key === 'valve' || key === 'adjustmentBuckle') {
                    formData[key] = 'none';
                } else if (key === 'internalTest') {
                    formData[key] = 'no';
                } else if (key === 'valvePrint') {
                    formData[key] = 'none';
                } else if (key === 'colorBox' || key === 'tape' || key === 'blisterPack' || key === 'other') {
                    formData[key] = false;
                } else if (key === 'colorBoxFiles' || key === 'blisterPackFiles') {
                    formData[key] = [];
                } else if (key === 'tapeMaterial' || key === 'otherDescription') {
                    formData[key] = '';
                } else {
                    formData[key] = '';
                }
            });

            // 重置测试样品数量
            testSampleCounts.value = {};

            // 重置用户选择相关状态
            selectedUsers.value = [];
            showUserSelection.value = false;
            userSearchTerm.value = '';
            selectedCcUsers.value = [];
            showCcUserSelection.value = false;
            ccUserSearchTerm.value = '';

            // 清空文件
            maskPrintFiles.value = [];
            valvePrintFiles.value = [];
            colorBoxFiles.value = [];
            blisterPackFiles.value = [];
        }

        return {
            formData,
            loading,
            submitting,
            users,
            loadingUsers,
            selectedUsers,
            showUserSelection,
            userSearchTerm,
            selectedCcUsers,
            showCcUserSelection,
            ccUserSearchTerm,
            formattedDate,
            generatedNumber,
            testSampleCounts,
            groupedTestCounts,
            filteredUsers,
            filteredCcUsers,
            maskPrintFiles,
            valvePrintFiles,
            colorBoxFiles,
            blisterPackFiles,
            isUserSelected,
            isCcUserSelected,
            handleInternalTestChange,
            handleTestItemChange,
            handleTestLevelChange,
            handleOtherTestChange,
            updateTestSampleCount,
            handlePackagingMaterialChange,
            toggleUserSelectionDisplay,
            toggleUserSelection,
            toggleCcUserSelectionDisplay,
            toggleCcUserSelection,
            handleMaskPrintFileSelect,
            handleValvePrintFileSelect,
            removeMaskPrintFile,
            removeValvePrintFile,
            handleColorBoxFileSelect,
            handleBlisterPackFileSelect,
            removeColorBoxFile,
            removeBlisterPackFile,
            submitForm,
            resetForm,
            fetchNextSampleNumber
        };
    },
    template: `
        <div class="sample-form">
            <style>
                .fade-enter-active, .fade-leave-active {
                    transition: opacity 0.3s ease, transform 0.3s ease;
                }
                .fade-enter-from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                .fade-leave-to {
                    opacity: 0;
                    transform: translateY(-10px);
                }
            </style>
            <form @submit.prevent="submitForm" class="space-y-6">
                <!-- 基本信息区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 text-center">样品寄送通知单</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">日期</label>
                            <input type="date" v-model="formData.date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">编号</label>
                            <input type="text" :value="generatedNumber" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邮寄目的</label>
                            <input type="text" v-model="formData.purpose"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入邮寄目的">
                        </div>
                    </div>
                </div>

                <!-- 公司信息区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">公司信息</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">公司名称 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="formData.companyName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入公司名称">
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">收件人 <span class="text-red-500">*</span></label>
                                <input type="text" v-model="formData.recipient" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入收件人">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">电话</label>
                                <input type="tel" v-model="formData.application"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入联系电话">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">地址</label>
                            <textarea v-model="formData.address" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入地址"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 样品内容区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6 text-center">样品内容</h3>

                    <!-- 产品规格和客制化说明区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 产品规格区域（左侧） -->
                        <div class="bg-gray-50 rounded-lg border p-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">产品规格</h4>
                            <div class="space-y-4">
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">型号 <span class="text-red-500">*</span></label>
                                <input type="text" v-model="formData.productModel" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="<型号+产品描述>">
                                </div>
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">产品描述</label>
                                <textarea v-model="formData.productDescription" rows="2"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请输入产品描述"></textarea>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">外层</label>
                                    <input type="text" v-model="formData.outerLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">中层</label>
                                    <input type="text" v-model="formData.middleLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">内层</label>
                                    <input type="text" v-model="formData.innerLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">头带</label>
                                    <input type="text" v-model="formData.headband"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">鼻夹</label>
                                    <input type="text" v-model="formData.noseBridge"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">鼻垫</label>
                                    <input type="text" v-model="formData.earLoop"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                </div>

                                <!-- 气阀信息 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">气阀</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valve" value="none" class="mr-2">
                                            <span class="text-sm">无</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valve" value="with" class="mr-2">
                                            <span class="text-sm">有</span>
                                        </label>
                                    </div>
                                    <div v-if="formData.valve === 'with'" class="space-y-2 pl-4">
                                        <div>
                                            <input type="text" v-model="formData.valveTopCover" placeholder="气阀上下盖"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <input type="text" v-model="formData.valveSheet" placeholder="气阀片"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <!-- 调整扣和是否内测 -->
                                <div class="border-t pt-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">调整扣</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" v-model="formData.adjustmentBuckle" value="none" class="mr-2">
                                                <span class="text-sm">无</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" v-model="formData.adjustmentBuckle" value="with" class="mr-2">
                                                <span class="text-sm">有</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">是否内测</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" :value="'yes'" :checked="formData.internalTest === 'yes'"
                                                       @change="handleInternalTestChange('yes')" class="mr-2">
                                                <span class="text-sm">是</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" :value="'no'" :checked="formData.internalTest === 'no'"
                                                       @change="handleInternalTestChange('no')" class="mr-2">
                                                <span class="text-sm">否</span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">选择"是"将显示测试项目填写区域</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- 客制化说明区域（右侧） -->
                        <div class="bg-gray-50 rounded-lg border p-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">客制化说明</h4>
                            <div class="space-y-4">
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">数量 <span class="text-red-500">*</span></label>
                                <input type="number" v-model="formData.quantity" required min="1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入数量">
                                </div>

                                <!-- 口罩印字 -->
                                <div class="border-b pb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">口罩印字</label>
                                <div class="space-y-2">
                                    <!-- 文件上传区域 -->
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        <div class="text-center">
                                            <label class="cursor-pointer">
                                                <span class="text-blue-600 hover:text-blue-500">点击上传印字图稿文件</span>
                                                <input type="file" multiple @change="handleMaskPrintFileSelect"
                                                       class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                            </label>
                                        </div>
                                        <!-- 已选择的文件列表 -->
                                        <div v-if="maskPrintFiles.length > 0" class="mt-2 space-y-1">
                                            <div v-for="(file, index) in maskPrintFiles" :key="index"
                                                 class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                <span class="text-sm text-gray-700">{{ file.name }}</span>
                                                <button @click="removeMaskPrintFile(index)"
                                                        class="text-red-500 hover:text-red-700 text-sm">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="text" v-model="formData.printColor" placeholder="印字颜色"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <input type="text" v-model="formData.printSize" placeholder="印字尺寸"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                                </div>

                                <!-- 头带颜色 -->
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">头带颜色</label>
                                <input type="text" v-model="formData.headbandColor"
                                       placeholder="须为工程管理有认证之颜色"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">须为工程管理有认证之颜色</p>
                                </div>

                                <!-- 气阀颜色 -->
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">气阀颜色</label>
                                <input type="text" v-model="formData.valveColor"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                                <!-- 气阀印字 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">气阀印字</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valvePrint" value="none" class="mr-2">
                                            <span class="text-sm">无气阀印字</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valvePrint" value="need" class="mr-2">
                                            <span class="text-sm">需气阀印字</span>
                                        </label>
                                    </div>
                                    <div v-if="formData.valvePrint === 'need'" class="space-y-2 pl-4">
                                        <!-- 印字图稿文件上传 -->
                                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                            <div class="text-center">
                                                <label class="cursor-pointer">
                                                    <span class="text-blue-600 hover:text-blue-500">点击上传印字图稿文件</span>
                                                    <input type="file" multiple @change="handleValvePrintFileSelect"
                                                           class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                </label>
                                            </div>
                                            <!-- 已选择的文件列表 -->
                                            <div v-if="valvePrintFiles.length > 0" class="mt-2 space-y-1">
                                                <div v-for="(file, index) in valvePrintFiles" :key="index"
                                                     class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                    <span class="text-sm text-gray-700">{{ file.name }}</span>
                                                    <button @click="removeValvePrintFile(index)"
                                                            class="text-red-500 hover:text-red-700 text-sm">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-2 gap-2">
                                            <input type="text" v-model="formData.printMaterialColor" placeholder="印字颜色"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <input type="text" v-model="formData.printMaterialSize" placeholder="印字尺寸"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <!-- 包装信息 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">包装信息</label>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">包装方式</label>
                                        <input type="text" v-model="formData.packagingMethod"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入包装方式">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">包装材质&尺寸（可多选）</label>
                                        <div class="space-y-4">
                                            <!-- 彩盒 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.colorBox" class="mr-2">
                                                    <span class="text-sm">彩盒</span>
                                                </label>
                                                <div v-if="formData.colorBox" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">上传彩盒图档</label>
                                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-3">
                                                        <div class="text-center">
                                                            <label class="cursor-pointer">
                                                                <span class="text-blue-600 hover:text-blue-500 text-xs">点击上传彩盒图档文件</span>
                                                                <input type="file" multiple @change="handleColorBoxFileSelect"
                                                                       class="hidden" accept="image/*,.pdf,.ai,.eps">
                                                            </label>
                                                        </div>
                                                        <!-- 已选择的文件列表 -->
                                                        <div v-if="colorBoxFiles.length > 0" class="mt-2 space-y-1">
                                                            <div v-for="(file, index) in colorBoxFiles" :key="index"
                                                                 class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-xs text-gray-700">{{ file.name }}</span>
                                                                <button @click="removeColorBoxFile(index)"
                                                                        class="text-red-500 hover:text-red-700 text-xs">删除</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p class="text-xs text-gray-500 mt-1">支持图片、PDF、AI、EPS格式</p>
                                                </div>
                                            </div>

                                            <!-- 胶带 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.tape" class="mr-2">
                                                    <span class="text-sm">胶带</span>
                                                </label>
                                                <div v-if="formData.tape" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">胶带材质和尺寸</label>
                                                    <input type="text"
                                                           v-model="formData.tapeMaterial"
                                                           placeholder="请输入胶带材质和尺寸"
                                                           class="text-xs border border-gray-300 rounded px-2 py-1 w-full">
                                                </div>
                                            </div>

                                            <!-- 泡壳 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.blisterPack" class="mr-2">
                                                    <span class="text-sm">泡壳</span>
                                                </label>
                                                <div v-if="formData.blisterPack" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">上传吊卡图档</label>
                                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-3">
                                                        <div class="text-center">
                                                            <label class="cursor-pointer">
                                                                <span class="text-blue-600 hover:text-blue-500 text-xs">点击上传吊卡图档文件</span>
                                                                <input type="file" multiple @change="handleBlisterPackFileSelect"
                                                                       class="hidden" accept="image/*,.pdf,.ai,.eps">
                                                            </label>
                                                        </div>
                                                        <!-- 已选择的文件列表 -->
                                                        <div v-if="blisterPackFiles.length > 0" class="mt-2 space-y-1">
                                                            <div v-for="(file, index) in blisterPackFiles" :key="index"
                                                                 class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                                <span class="text-xs text-gray-700">{{ file.name }}</span>
                                                                <button @click="removeBlisterPackFile(index)"
                                                                        class="text-red-500 hover:text-red-700 text-xs">删除</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p class="text-xs text-gray-500 mt-1">支持图片、PDF、AI、EPS格式</p>
                                                </div>
                                            </div>

                                            <!-- 其他 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.other" class="mr-2">
                                                    <span class="text-sm">其他</span>
                                                </label>
                                                <div v-if="formData.other" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">文字描述</label>
                                                    <textarea v-model="formData.otherDescription"
                                                              rows="2"
                                                              placeholder="请输入其他包装要求的文字描述"
                                                              class="text-xs border border-gray-300 rounded px-2 py-1 w-full"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 测试项目及数量（可复选）区域 -->
                <transition name="fade" mode="out-in">
                    <div v-if="formData.internalTest === 'yes'" class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">测试项目及数量（可复选）</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">测试项目</label>
                            <div class="space-y-4">
                                <!-- NIOSH美规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('NIOSH美规')"
                                               @change="handleTestItemChange('NIOSH美规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">NIOSH美规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('NIOSH美规')" class="ml-6 mt-2 space-y-2 border-l-2 border-blue-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗')"
                                                       @change="handleTestItemChange('一般穿透阻抗', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗')"
                                                        @change="handleTestLevelChange('一般穿透阻抗', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="N95">N95</option>
                                                    <option value="N99">N99</option>
                                                    <option value="N100">N100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试')"
                                                       @change="handleTestItemChange('加载测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试')"
                                                        @change="handleTestLevelChange('加载测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="N95">N95</option>
                                                    <option value="N99">N99</option>
                                                    <option value="N100">N100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力')"
                                                       @change="handleTestItemChange('头带拉力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力')"
                                                        @change="handleTestLevelChange('头带拉力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力')"
                                                       @change="handleTestItemChange('气阀拉力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力')"
                                                        @change="handleTestLevelChange('气阀拉力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('密合度测试')"
                                                       @change="handleTestItemChange('密合度测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">密合度测试</span>
                                                <select v-if="formData.testItems.includes('密合度测试')"
                                                        @change="handleTestLevelChange('密合度测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="定性">定性</option>
                                                    <option value="定量">定量</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他NIOSH')"
                                                       @change="handleTestItemChange('其他NIOSH', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他NIOSH')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他NIOSH', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- CE欧规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('CE欧规')"
                                               @change="handleTestItemChange('CE欧规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">CE欧规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('CE欧规')" class="ml-6 mt-2 space-y-2 border-l-2 border-green-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗CE')"
                                                       @change="handleTestItemChange('一般穿透阻抗CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗CE')"
                                                        @change="handleTestLevelChange('一般穿透阻抗CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试CE')"
                                                       @change="handleTestItemChange('加载测试CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试CE')"
                                                        @change="handleTestLevelChange('加载测试CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('全方位阻抗CE')"
                                                       @change="handleTestItemChange('全方位阻抗CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">全方位阻抗</span>
                                                <select v-if="formData.testItems.includes('全方位阻抗CE')"
                                                        @change="handleTestLevelChange('全方位阻抗CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('总泄漏率')"
                                                       @change="handleTestItemChange('总泄漏率', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">总泄漏率</span>
                                                <select v-if="formData.testItems.includes('总泄漏率')"
                                                        @change="handleTestLevelChange('总泄漏率', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力CE')"
                                                       @change="handleTestItemChange('头带拉力CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力CE')"
                                                        @change="handleTestLevelChange('头带拉力CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力CE')"
                                                       @change="handleTestItemChange('气阀拉力CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力CE')"
                                                        @change="handleTestLevelChange('气阀拉力CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('白云石')"
                                                       @change="handleTestItemChange('白云石', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">白云石</span>
                                                <select v-if="formData.testItems.includes('白云石')"
                                                        @change="handleTestLevelChange('白云石', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他CE')"
                                                       @change="handleTestItemChange('其他CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他CE')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他CE', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- AS/NZS澳规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('AS/NZS澳规')"
                                               @change="handleTestItemChange('AS/NZS澳规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">AS/NZS澳规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('AS/NZS澳规')" class="ml-6 mt-2 space-y-2 border-l-2 border-yellow-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗澳规')"
                                                       @change="handleTestItemChange('一般穿透阻抗澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗澳规')"
                                                        @change="handleTestLevelChange('一般穿透阻抗澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="P1">P1</option>
                                                    <option value="P2">P2</option>
                                                    <option value="P3">P3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('全方位阻抗澳规')"
                                                       @change="handleTestItemChange('全方位阻抗澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">全方位阻抗</span>
                                                <select v-if="formData.testItems.includes('全方位阻抗澳规')"
                                                        @change="handleTestLevelChange('全方位阻抗澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="P1">P1</option>
                                                    <option value="P2">P2</option>
                                                    <option value="P3">P3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力澳规')"
                                                       @change="handleTestItemChange('头带拉力澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力澳规')"
                                                        @change="handleTestLevelChange('头带拉力澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力澳规')"
                                                       @change="handleTestItemChange('气阀拉力澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力澳规')"
                                                        @change="handleTestLevelChange('气阀拉力澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他澳规')"
                                                       @change="handleTestItemChange('其他澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他澳规')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他澳规', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- 日规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('日规')"
                                               @change="handleTestItemChange('日规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">日规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('日规')" class="ml-6 mt-2 space-y-2 border-l-2 border-red-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('粉尘和吸气上升值')"
                                                       @change="handleTestItemChange('粉尘和吸气上升值', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">粉尘和吸气上升值</span>
                                                <select v-if="formData.testItems.includes('粉尘和吸气上升值')"
                                                        @change="handleTestLevelChange('粉尘和吸气上升值', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="DS1">DS1</option>
                                                    <option value="DS2">DS2</option>
                                                    <option value="DS3">DS3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('湿阻力')"
                                                       @change="handleTestItemChange('湿阻力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">湿阻力</span>
                                                <select v-if="formData.testItems.includes('湿阻力')"
                                                        @change="handleTestLevelChange('湿阻力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('使用时间')"
                                                       @change="handleTestItemChange('使用时间', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">使用时间</span>
                                                <select v-if="formData.testItems.includes('使用时间')"
                                                        @change="handleTestLevelChange('使用时间', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="8小时">8小时</option>
                                                    <option value="12小时">12小时</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('吸气排气阻力测试')"
                                                       @change="handleTestItemChange('吸气排气阻力测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">吸气排气阻力测试</span>
                                                <select v-if="formData.testItems.includes('吸气排气阻力测试')"
                                                        @change="handleTestLevelChange('吸气排气阻力测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('泄漏率')"
                                                       @change="handleTestItemChange('泄漏率', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">泄漏率</span>
                                                <select v-if="formData.testItems.includes('泄漏率')"
                                                        @change="handleTestLevelChange('泄漏率', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="严格">严格</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他日规')"
                                                       @change="handleTestItemChange('其他日规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他日规')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他日规', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- GB国标 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('GB国标')"
                                               @change="handleTestItemChange('GB国标', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">GB国标</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('GB国标')" class="ml-6 mt-2 space-y-2 border-l-2 border-purple-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗GB')"
                                                       @change="handleTestItemChange('一般穿透阻抗GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗GB')"
                                                        @change="handleTestLevelChange('一般穿透阻抗GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="KN90">KN90</option>
                                                    <option value="KN95">KN95</option>
                                                    <option value="KN100">KN100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试GB')"
                                                       @change="handleTestItemChange('加载测试GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试GB')"
                                                        @change="handleTestLevelChange('加载测试GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="KN90">KN90</option>
                                                    <option value="KN95">KN95</option>
                                                    <option value="KN100">KN100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力GB')"
                                                       @change="handleTestItemChange('头带拉力GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力GB')"
                                                        @change="handleTestLevelChange('头带拉力GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力GB')"
                                                       @change="handleTestItemChange('气阀拉力GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力GB')"
                                                        @change="handleTestLevelChange('气阀拉力GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('泄漏测试')"
                                                       @change="handleTestItemChange('泄漏测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">泄漏测试</span>
                                                <select v-if="formData.testItems.includes('泄漏测试')"
                                                        @change="handleTestLevelChange('泄漏测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="严格">严格</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他GB')"
                                                       @change="handleTestItemChange('其他GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他GB')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他GB', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- 医疗项目 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('医疗项目')"
                                               @change="handleTestItemChange('医疗项目', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">医疗项目</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('医疗项目')" class="ml-6 mt-2 space-y-2 border-l-2 border-pink-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('血透测试')"
                                                       @change="handleTestItemChange('血透测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">血透测试</span>
                                                <select v-if="formData.testItems.includes('血透测试')"
                                                        @change="handleTestLevelChange('血透测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('BFE')"
                                                       @change="handleTestItemChange('BFE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">BFE</span>
                                                <select v-if="formData.testItems.includes('BFE')"
                                                        @change="handleTestLevelChange('BFE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≥95%">≥95%</option>
                                                    <option value="≥98%">≥98%</option>
                                                    <option value="≥99%">≥99%</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('PFE')"
                                                       @change="handleTestItemChange('PFE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">PFE</span>
                                                <select v-if="formData.testItems.includes('PFE')"
                                                        @change="handleTestLevelChange('PFE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≥95%">≥95%</option>
                                                    <option value="≥98%">≥98%</option>
                                                    <option value="≥99%">≥99%</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('压差')"
                                                       @change="handleTestItemChange('压差', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">压差</span>
                                                <select v-if="formData.testItems.includes('压差')"
                                                        @change="handleTestLevelChange('压差', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≤49Pa">≤49Pa</option>
                                                    <option value="≤60Pa">≤60Pa</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('阻燃测试')"
                                                       @change="handleTestItemChange('阻燃测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">阻燃测试</span>
                                                <select v-if="formData.testItems.includes('阻燃测试')"
                                                        @change="handleTestLevelChange('阻燃测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="Class 1">Class 1</option>
                                                    <option value="Class 2">Class 2</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他医疗')"
                                                       @change="handleTestItemChange('其他医疗', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他医疗')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他医疗', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">样品测试数量</label>
                            <div v-if="Object.keys(groupedTestCounts).length === 0" class="text-sm text-gray-500 italic">
                                请先选择测试项目
                            </div>
                            <div v-else class="space-y-4 max-h-80 overflow-y-auto">
                                <div v-for="(items, category) in groupedTestCounts" :key="category"
                                     class="border border-gray-200 rounded-lg p-3">
                                    <h4 class="text-sm font-medium text-gray-800 mb-2 border-b border-gray-100 pb-1">
                                        {{ category }}
                                    </h4>
                                    <div class="space-y-2">
                                        <div v-for="item in items" :key="item.name"
                                             class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-xs text-gray-700 flex-1">{{ item.name }}</span>
                                            <div class="flex items-center space-x-2">
                                                <input type="number"
                                                       :value="item.count"
                                                       @input="updateTestSampleCount(item.name, $event.target.value)"
                                                       min="1"
                                                       max="100"
                                                       class="w-14 text-xs border border-gray-300 rounded px-1 py-1 text-center">
                                                <span class="text-xs text-gray-500">个</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">数量会根据测试项目自动设置，您可以手动调整</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">测试备注</label>
                            <textarea v-model="formData.testNotes" rows="6"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入测试备注"></textarea>
                        </div>
                    </div>
                    </div>
                </transition>

                <!-- 补充说明区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <div class="flex items-center space-x-3 mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-800">补充说明</h3>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">其他需要说明的事项</label>
                        <textarea v-model="formData.additionalNotes" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="请输入其他需要说明的事项，如特殊要求、注意事项等"></textarea>
                        <p class="text-xs text-gray-500 mt-1">此内容将在邮件通知中显示，帮助收件人更好地了解样品相关信息</p>
                    </div>
                </div>

                <!-- 收件人和抄送人信息区域 -->
                <div class="email-notification-section">
                    <div class="flex items-center space-x-3 mb-6">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-800">邮件通知设置</h3>
                    </div>

                    <!-- 收件人和抄送人选择按钮 -->
                    <div class="recipient-grid mb-6">
                        <!-- 收件人区域 -->
                        <div class="recipient-card to-recipients">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-2">
                                    <h4 class="text-md font-semibold text-gray-800">收件人 <span class="text-red-500">*</span></h4>
                                    <span v-if="selectedUsers.length > 0" class="count-badge to-count">
                                        {{ selectedUsers.length }} 人
                                    </span>
                                </div>
                                <button
                                    type="button"
                                    @click="toggleUserSelectionDisplay"
                                    :disabled="loadingUsers"
                                    class="select-button primary"
                                >
                                    <span v-if="loadingUsers">加载中...</span>
                                    <span v-else>{{ showUserSelection ? '收起' : '选择' }}</span>
                                </button>
                            </div>
                            <p class="text-sm text-gray-600 mb-3">
                                选择要通知的用户，他们将收到样品单的邮件通知
                            </p>

                            <!-- 已选择的收件人显示 -->
                            <div v-if="selectedUsers.length > 0" class="space-y-2">
                                <div class="flex flex-wrap gap-2">
                                    <span v-for="(user, index) in selectedUsers" :key="user.id"
                                          class="user-tag to-user">
                                        {{ user.username || user.name }}
                                        <button type="button" @click="selectedUsers.splice(index, 1)"
                                                class="remove-btn">
                                            ×
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 抄送人区域 -->
                        <div class="recipient-card cc-recipients">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-2">
                                    <h4 class="text-md font-semibold text-gray-800">抄送人</h4>
                                    <span v-if="selectedCcUsers.length > 0" class="count-badge cc-count">
                                        {{ selectedCcUsers.length }} 人
                                    </span>
                                </div>
                                <button
                                    type="button"
                                    @click="toggleCcUserSelectionDisplay"
                                    :disabled="loadingUsers"
                                    class="select-button success"
                                >
                                    <span v-if="loadingUsers">加载中...</span>
                                    <span v-else>{{ showCcUserSelection ? '收起' : '选择' }}</span>
                                </button>
                            </div>
                            <p class="text-sm text-gray-600 mb-3">
                                选择要抄送的用户，他们将收到样品单的邮件抄送（可选）
                            </p>

                            <!-- 已选择的抄送人显示 -->
                            <div v-if="selectedCcUsers.length > 0" class="space-y-2">
                                <div class="flex flex-wrap gap-2">
                                    <span v-for="(user, index) in selectedCcUsers" :key="user.id"
                                          class="user-tag cc-user">
                                        {{ user.username || user.name }}
                                        <button type="button" @click="selectedCcUsers.splice(index, 1)"
                                                class="remove-btn">
                                            ×
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收件人选择列表 -->
                    <div v-if="showUserSelection" class="user-selection-panel to-panel mb-6">
                        <div class="user-selection-header to-header">
                            <h5 class="font-medium">选择收件人</h5>
                            <button @click="showUserSelection = false" class="close-btn">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="p-4">
                            <input
                                type="text"
                                v-model="userSearchTerm"
                                placeholder="搜索用户姓名、工号或部门..."
                                class="search-input to-search"
                            >
                        </div>
                        <div class="max-h-60 overflow-y-auto">
                            <div
                                v-for="user in filteredUsers"
                                :key="user.id"
                                @click="toggleUserSelection(user)"
                                class="user-item"
                                :class="{ 'selected to-selected': isUserSelected(user) }"
                            >
                                <div class="custom-checkbox to-checkbox" :class="{ 'checked': isUserSelected(user) }">
                                    <svg v-if="isUserSelected(user)" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="user-info">
                                    <div class="user-name">{{ user.username || user.name }}</div>
                                    <div class="user-email">{{ user.email }}</div>
                                    <div v-if="user.department" class="user-department">{{ user.department }}</div>
                                </div>
                            </div>
                            <div v-if="filteredUsers.length === 0" class="empty-state">
                                没有找到匹配的用户
                            </div>
                        </div>
                    </div>

                    <!-- 抄送人选择列表 -->
                    <div v-if="showCcUserSelection" class="user-selection-panel cc-panel">
                        <div class="user-selection-header cc-header">
                            <h5 class="font-medium">选择抄送人</h5>
                            <button @click="showCcUserSelection = false" class="close-btn">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="p-4">
                            <input
                                type="text"
                                v-model="ccUserSearchTerm"
                                placeholder="搜索用户姓名、工号或部门..."
                                class="search-input cc-search"
                            >
                        </div>
                        <div class="max-h-60 overflow-y-auto">
                            <div
                                v-for="user in filteredCcUsers"
                                :key="user.id"
                                @click="toggleCcUserSelection(user)"
                                class="user-item"
                                :class="{ 'selected cc-selected': isCcUserSelected(user) }"
                            >
                                <div class="custom-checkbox cc-checkbox" :class="{ 'checked': isCcUserSelected(user) }">
                                    <svg v-if="isCcUserSelected(user)" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="user-info">
                                    <div class="user-name">{{ user.username || user.name }}</div>
                                    <div class="user-email">{{ user.email }}</div>
                                    <div v-if="user.department" class="user-department">{{ user.department }}</div>
                                </div>
                            </div>
                            <div v-if="filteredCcUsers.length === 0" class="empty-state">
                                没有找到匹配的用户
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <div class="flex justify-end space-x-4">
                        <button type="button" @click="resetForm"
                                class="px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors">
                            重置表单
                        </button>
                        <button type="submit" :disabled="submitting"
                                class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <span v-if="submitting">提交中...</span>
                            <span v-else>提交样品单</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    `
};
