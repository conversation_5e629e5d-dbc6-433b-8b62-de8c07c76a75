/**
 * 邮件队列路由
 * 提供邮件队列监控和管理的API路由
 */

const express = require('express');
const router = express.Router();
const emailQueueController = require('../controllers/emailQueueController');
const { authenticateJWT, authorizeRoles } = require('../middlewares/auth');

// 获取邮件队列状态（仅管理员可用）
router.get('/status',
    authenticateJWT,
    authorizeRoles('admin'),
    emailQueueController.getQueueStatus
);

// 重试失败的邮件任务（仅管理员可用）
router.post('/retry-failed',
    authenticateJWT,
    authorizeRoles('admin'),
    emailQueueController.retryFailedTasks
);

// 清理失败的邮件任务（仅管理员可用）
router.post('/cleanup-failed',
    authenticateJWT,
    authorizeRoles('admin'),
    emailQueueController.cleanupFailedTasks
);

module.exports = router;
