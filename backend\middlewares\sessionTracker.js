/**
 * 会话跟踪中间件
 * 记录用户登录、会话活动和性能指标
 */

const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

class SessionTracker {
    constructor() {
        this.db = null;
        this.initDatabase();
    }

    /**
     * 初始化数据库连接
     */
    initDatabase() {
        try {
            const { postgresConnectionPool } = require('../utils/postgresConnectionPool');
            this.db = postgresConnectionPool; // 使用PostgreSQL连接池
        } catch (error) {
            logger.error('会话跟踪器数据库初始化失败:', error);
        }
    }

    /**
     * 记录用户登录
     */
    async recordLogin(username, userId, ipAddress, userAgent, success = true, failureReason = null) {
        if (!this.db) return;

        try {
            const sessionId = success ? uuidv4() : null;
            const loginTime = new Date().toISOString();

            // 记录登录日志
            await this.db.query(`
                INSERT INTO user_login_logs (
                    id, username, user_id, ip_address, user_agent,
                    login_time, login_status, session_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            `, [uuidv4(), username, userId, ipAddress, userAgent, loginTime, success ? 'success' : 'failed', sessionId]);

            // 如果登录成功，创建会话记录
            if (success && userId) {
                const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24小时后过期

                // 先清理该用户的其他活跃会话（防止同一用户多个会话）
                await this.db.query(`
                    UPDATE user_sessions
                    SET active = false, updated_at = $1
                    WHERE user_id = $2 AND active = true
                `, [loginTime, userId]);

                // 创建新的会话记录
                await this.db.query(`
                    INSERT INTO user_sessions (
                        id, user_id, username, session_token, ip_address, user_agent,
                        login_time, last_activity, expires_at, active
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                `, [sessionId, userId, username, sessionId, ipAddress, userAgent, loginTime, loginTime, expiresAt, true]);

                return sessionId;
            }
        } catch (error) {
            logger.error('记录用户登录失败:', error);
        }

        return null;
    }

    /**
     * 更新会话活动
     */
    async updateSessionActivity(sessionId, userId) {
        if (!this.db || !sessionId) return;

        try {
            const lastActivity = new Date().toISOString();
            await this.db.query(`
                UPDATE user_sessions
                SET last_activity = $1, updated_at = $2
                WHERE id = $3 AND user_id = $4 AND active = true
            `, [lastActivity, lastActivity, sessionId, userId]);
        } catch (error) {
            logger.error('更新会话活动失败:', error);
        }
    }

    /**
     * 记录用户登出
     */
    async recordLogout(sessionId, userId) {
        if (!this.db || !sessionId) return;

        try {
            const logoutTime = new Date().toISOString();

            // 更新登录日志的登出时间
            await this.db.query(`
                UPDATE user_login_logs
                SET logout_time = $1
                WHERE session_id = $2 AND user_id = $3
            `, [logoutTime, sessionId, userId]);

            // 标记会话为非活跃
            await this.db.query(`
                UPDATE user_sessions
                SET active = false, updated_at = $1
                WHERE id = $2 AND user_id = $3
            `, [logoutTime, sessionId, userId]);
        } catch (error) {
            logger.error('记录用户登出失败:', error);
        }
    }

    /**
     * 记录API性能
     */
    async recordPerformance(endpoint, method, responseTime, statusCode, userId = null, ipAddress = null, errorMessage = null) {
        if (!this.db) return;

        try {
            await this.db.query(`
                INSERT INTO performance_logs (
                    endpoint, method, response_time, status_code,
                    user_id, ip_address, error_message
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            `, [endpoint, method, responseTime, statusCode, userId, ipAddress, errorMessage]);
        } catch (error) {
            logger.error('记录API性能失败:', error);
        }
    }

    /**
     * 记录系统指标
     */
    async recordSystemMetric(metricType, metricValue, metricUnit = null, additionalData = {}) {
        if (!this.db) return;

        try {
            await this.db.query(`
                INSERT INTO system_metrics (
                    metric_type, metric_value, metric_unit, additional_data
                ) VALUES ($1, $2, $3, $4)
            `, [metricType, metricValue, metricUnit, JSON.stringify(additionalData)]);
        } catch (error) {
            logger.error('记录系统指标失败:', error);
        }
    }

    /**
     * 清理过期会话
     */
    async cleanupExpiredSessions() {
        if (!this.db) return;

        try {
            const now = new Date().toISOString();

            // 标记过期会话为非活跃
            const result = await this.db.query(`
                UPDATE user_sessions
                SET active = false, updated_at = $1
                WHERE expires_at < $2 AND active = true
            `, [now, now]);

            if (result.rowCount > 0) {
                logger.info(`清理了 ${result.rowCount} 个过期会话`);
            }
        } catch (error) {
            logger.error('清理过期会话失败:', error);
        }
    }

    /**
     * 清理旧的日志记录
     */
    async cleanupOldLogs(daysToKeep = 30) {
        if (!this.db) return;

        try {
            const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();

            // 清理旧的登录日志
            const loginLogsResult = await this.db.query(`
                DELETE FROM user_login_logs
                WHERE login_time < $1
            `, [cutoffDate]);

            // 清理旧的性能日志
            const performanceLogsResult = await this.db.query(`
                DELETE FROM performance_logs
                WHERE created_at < $1
            `, [cutoffDate]);

            // 清理旧的系统指标
            const metricsResult = await this.db.query(`
                DELETE FROM system_metrics
                WHERE recorded_at < $1
            `, [cutoffDate]);

            logger.info(`清理了 ${loginLogsResult.rowCount} 条登录日志, ${performanceLogsResult.rowCount} 条性能日志, ${metricsResult.rowCount} 条系统指标`);
        } catch (error) {
            logger.error('清理旧日志失败:', error);
        }
    }

    /**
     * 性能监控中间件
     */
    performanceMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            const originalSend = res.send;

            res.send = function(data) {
                const responseTime = Date.now() - startTime;
                const endpoint = req.route ? req.route.path : req.path;
                const method = req.method;
                const statusCode = res.statusCode;
                const userId = req.user ? req.user.id : null;
                const ipAddress = req.ip || req.connection.remoteAddress;
                const errorMessage = statusCode >= 400 ? data : null;

                // 异步记录性能数据
                setImmediate(() => {
                    sessionTracker.recordPerformance(
                        endpoint, method, responseTime, statusCode,
                        userId, ipAddress, errorMessage
                    );
                });

                originalSend.call(this, data);
            };

            next();
        };
    }

    /**
     * 会话活动更新中间件
     */
    sessionActivityMiddleware() {
        return (req, res, next) => {
            if (req.user && req.sessionId) {
                // 异步更新会话活动
                setImmediate(() => {
                    this.updateSessionActivity(req.sessionId, req.user.id);
                });
            }
            next();
        };
    }
}

// 创建全局实例
const sessionTracker = new SessionTracker();

// 定期清理任务
setInterval(() => {
    sessionTracker.cleanupExpiredSessions();
}, 60 * 60 * 1000); // 每小时清理一次过期会话

setInterval(() => {
    sessionTracker.cleanupOldLogs(30);
}, 24 * 60 * 60 * 1000); // 每天清理一次旧日志

module.exports = sessionTracker;
