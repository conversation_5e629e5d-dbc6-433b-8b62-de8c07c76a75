/**
 * 服务器入口文件
 * 初始化Express应用并启动服务器
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const config = require('./config');
const { initDirectories } = require('./utils/fileSystem');
const apiRoutes = require('./routes');
const logger = require('./utils/logger');
const { formatTimestamp } = require('./utils/logger');
const { requestLogger, errorLogger } = require('./middlewares/logger');
const { createCacheMiddleware, memoryCache } = require('./middlewares/cache');
const {
    createCompressionMiddleware,
    createStaticCacheMiddleware,
    createPreCompressionMiddleware,
    createSecurityHeadersMiddleware,
    createPerformanceMiddleware
} = require('./middlewares/compression');
const {
    createAPIRateLimitMiddleware,
    createLoginRateLimitMiddleware,
    createInputValidationMiddleware,
    createSQLInjectionProtectionMiddleware,
    createHelmetMiddleware,
    createRequestSizeLimitMiddleware,
    createSecurityLogMiddleware,
    createIPWhitelistMiddleware
} = require('./middlewares/security');
const { systemMonitor } = require('./utils/systemMonitor');
const { formatNetworkAddresses } = require('./utils/networkUtils'); // 引入网络工具函数
const userService = require('./services/userService');
const departmentService = require('./services/departmentService');
const sessionTracker = require('./middlewares/sessionTracker');
const DatabaseInitializer = require('./database/initializeDatabase');

// 数据库初始化函数
async function initializeDatabase() {
    try {
        const dbInitializer = new DatabaseInitializer();
        await dbInitializer.initializeAllTables();
        await dbInitializer.close();
        logger.info('数据库初始化完成');
    } catch (error) {
        logger.error('数据库初始化失败:', error);
        process.exit(1); // 如果数据库初始化失败，退出应用
    }
}

// 系统初始化函数
async function initializeSystem() {
    try {
        // 1. 初始化目录
        initDirectories();

        // 2. 初始化数据库表结构
        await initializeDatabase();

        // 3. 初始化系统用户（仅在首次启动时创建默认用户）
        await userService.initializeSystemUsers();

        // 4. 初始化默认部门数据
        await departmentService.initDefaultDepartments();

        logger.info('系统初始化完成');
    } catch (error) {
        logger.error('系统初始化失败:', error);
        process.exit(1);
    }
}

// 创建Express应用
const app = express();

// 性能监控中间件（最先执行）
app.use(createPerformanceMiddleware());

// IP白名单中间件（在所有其他中间件之前）
app.use(createIPWhitelistMiddleware(config.security.ipWhitelist));

// Helmet安全头中间件
app.use(createHelmetMiddleware());

// 安全头中间件
app.use(createSecurityHeadersMiddleware());

// 请求大小限制
app.use(createRequestSizeLimitMiddleware('10mb'));

// 输入验证和SQL注入防护
app.use(createInputValidationMiddleware());
app.use(createSQLInjectionProtectionMiddleware());

// 安全日志记录
app.use(createSecurityLogMiddleware());

// API速率限制
app.use('/api', createAPIRateLimitMiddleware());

// 登录速率限制
app.use('/api/auth/login', createLoginRateLimitMiddleware());

// 压缩中间件
app.use(createCompressionMiddleware());

// 预压缩中间件（用于静态文件）
app.use(createPreCompressionMiddleware());

// CORS中间件 - 支持本地和IP访问
app.use(cors({
    origin: function (origin, callback) {
        // 允许无origin的请求（如移动应用、Postman等）
        if (!origin) return callback(null, true);

        // 检查是否为开发环境（默认为开发环境）
        const isDevelopment = !process.env.NODE_ENV || process.env.NODE_ENV === 'development';

        // 在开发环境中，允许所有请求
        if (isDevelopment) {
            return callback(null, true);
        }

        // 生产环境允许的域名列表
        const allowedOrigins = [
            /^http:\/\/localhost:\d+$/,
            /^http:\/\/127\.0\.0\.1:\d+$/,
            /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
            /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
            /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
        ];

        const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));
        if (isAllowed) {
            return callback(null, true);
        }

        // 生产环境需要明确配置允许的域名
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 添加文件名编码处理中间件
app.use((req, res, next) => {
    // 处理multipart/form-data中的文件名编码问题
    if (req.files && Array.isArray(req.files)) {
        req.files.forEach(file => {
            if (file.originalname) {
                try {
                    // 尝试修复文件名编码
                    const decoded = decodeURIComponent(escape(file.originalname));
                    file.originalname = decoded;
                } catch (error) {
                    // 如果解码失败，保持原始文件名
                    console.warn('文件名解码失败:', error);
                }
            }
        });
    }
    next();
});

// 请求日志中间件
app.use(requestLogger);

// 性能监控中间件（全局）
app.use(sessionTracker.performanceMiddleware());

// API路由（带缓存）- 主要API路由，包含用户签名等
app.use('/api',
    createCacheMiddleware({
        ttl: 300000, // 5分钟缓存
        condition: (req) => req.method === 'GET' && !req.url.includes('/auth/') && !req.url.includes('/signature') && !req.url.includes('/equipment') && !req.url.includes('/factories'),
        keyGenerator: (req) => `api:${req.method}:${req.originalUrl}:${req.user?.id || 'anonymous'}`
    }),
    sessionTracker.sessionActivityMiddleware(), // 会话活动跟踪中间件移到API路由内部
    apiRoutes
);

// 设备产能管理路由
const capacityRoutes = require('./routes/capacity');
app.use('/api', capacityRoutes);

// 智能排程路由
const schedulingRoutes = require('./routes/scheduling');
app.use('/api/scheduling', schedulingRoutes);

// 性能监控路由
const performanceRoutes = require('./routes/performance');
app.use('/api/performance', performanceRoutes);

// 产能数据API路由
const capacityDataRoutes = require('./routes/capacity-data');
app.use('/api/capacity-data', capacityDataRoutes);

// 提供前端静态文件（带缓存控制）
app.use(express.static(path.join(__dirname, '../frontend'), {
    maxAge: '1d',
    etag: true,
    lastModified: true,
    setHeaders: (res, path) => {
        // 为不同类型的文件设置不同的缓存策略和MIME类型
        if (path.endsWith('.html')) {
            res.setHeader('Cache-Control', 'public, max-age=3600'); // 1小时
        } else if (path.match(/\.(js|mjs)$/)) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1年
            res.setHeader('Content-Type', 'application/javascript'); // 确保JavaScript文件的MIME类型
        } else if (path.endsWith('.css')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1年
            res.setHeader('Content-Type', 'text/css'); // 确保CSS文件的MIME类型
        } else if (path.match(/\.(png|jpg|jpeg|gif|svg|ico)$/)) {
            res.setHeader('Cache-Control', 'public, max-age=2592000'); // 30天
        }
    }
}));

// 提供上传的文件访问（带缓存控制）
app.use('/uploads', createStaticCacheMiddleware({ maxAge: 86400 }),
    express.static(path.join(__dirname, 'uploads')));

// 页面路由
app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/login.html'));
});

// 主页
app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/dashboard.html'));
});

// 系统功能展示页面（无需认证）
app.get('/system-overview', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/system-overview.html'));
});

// 申请相关页面
app.get('/new-application', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/application/new.html'));
});

app.get('/application-history', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/application/history.html'));
});

app.get('/application-record', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/application/record.html'));
});

app.get('/pending-approval', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/application/pending.html'));
});

app.get('/approved-applications', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/application/approved.html'));
});

// 用户相关页面
app.get('/user-settings', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/user/settings.html'));
});

// 系统管理相关页面
app.get('/system-management/user-management', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/system-management/user-management.html'));
});

app.get('/system-management/customer-management', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/system-management/customer-management.html'));
});

app.get('/system-management/settings', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/system-management/settings.html'));
});

app.get('/system-management/logs', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/system-management/logs.html'));
});

// 生产排程相关页面
app.get('/schedule/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/dashboard.html'));
});

app.get('/schedule/list', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/list.html'));
});

app.get('/schedule/create', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/create.html'));
});

app.get('/schedule/edit/:id', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/edit.html'));
});

app.get('/schedule/resources', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/resources.html'));
});

app.get('/schedule/reports', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/schedule/reports.html'));
});

// 产品管理相关页面
app.get('/product/management', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/product/management.html'));
});

app.get('/product/processes', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/product/processes.html'));
});

// 设备产能管理页面
app.get('/equipment/capacity', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/equipment/capacity.html'));
});

// 操作员技能管理页面
app.get('/operator/skills', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/operator/skills.html'));
});

// 智能排程页面
app.get('/scheduling/intelligent', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/scheduling/intelligent.html'));
});



// 算法调优页面
app.get('/admin/algorithm-tuning', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/admin/algorithm-tuning.html'));
});

// 设备管理相关页面
app.get('/equipment/info', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/equipment/info.html'));
});

app.get('/equipment/maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/equipment/maintenance.html'));
});

app.get('/equipment/health', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/equipment/health.html'));
});

// 质量管理相关页面
app.get('/quality-upload', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/quality/upload.html'));
});

app.get('/quality-list', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/quality/list.html'));
});

// 文件管理相关页面
app.get('/file-upload', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/upload.html'));
});

app.get('/file-upload/certification', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/certification.html'));
});

app.get('/file-upload/sample', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/sample.html'));
});

app.get('/file-upload/production-control', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/production-control.html'));
});

app.get('/file-list', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/list.html'));
});

app.get('/file-notifications', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/file-management/notifications.html'));
});

// 仓库管理相关页面
app.get('/warehouse/materials-products', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/warehouse/materials-products.html'));
});

app.get('/warehouse/inventory-monitor', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/warehouse/inventory-monitor.html'));
});

app.get('/warehouse/operations', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/warehouse/operations.html'));
});

app.get('/warehouse/reports', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/warehouse/reports.html'));
});

// 前端配置接口（无需认证）
app.get('/api/config', (req, res) => {
    res.json({
        success: true,
        data: {
            apiUrl: `${req.protocol}://${req.get('host')}/api`,
            serverPort: config.server.port,
            serverHost: config.server.host,
            environment: process.env.NODE_ENV || 'development'
        }
    });
});

// 性能监控和健康检查路由
app.get('/api/system/health', (req, res) => {
    const databaseManager = require('./database/database');
    const healthData = databaseManager.healthCheck();

    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        database: healthData,
        cache: memoryCache.getStats()
    });
});

app.get('/api/system/performance', (req, res) => {
    const databaseManager = require('./database/database');
    const performanceStats = databaseManager.getPerformanceStats();

    res.json({
        timestamp: new Date().toISOString(),
        ...performanceStats,
        process: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage()
        }
    });
});

app.get('/api/system/optimization', (req, res) => {
    const databaseManager = require('./database/database');
    const suggestions = databaseManager.getOptimizationSuggestions();
    const slowQueries = databaseManager.getSlowQueries(10);

    res.json({
        suggestions,
        slowQueries,
        cacheStats: memoryCache.getStats(),
        timestamp: new Date().toISOString()
    });
});

// 系统监控路由
app.get('/api/system/monitor', (req, res) => {
    const status = systemMonitor.getSystemStatus();
    res.json(status);
});

app.get('/api/system/monitor/detailed', (req, res) => {
    const stats = systemMonitor.getDetailedStats();
    res.json(stats);
});

// 记录请求到系统监控（集成到性能监控中间件中，避免重复）

// 根路径重定向到前端的index.html，让前端根据用户角色决定重定向目标
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/index.html'));
});

// 所有未匹配的路由返回index.html (支持前端路由)
// 但排除API路由和静态资源
app.get('*', (req, res, next) => {
    // 如果是API请求，不应该被这里处理
    if (req.originalUrl.startsWith('/api/')) {
        console.log('API请求被通配符路由拦截:', req.originalUrl);
        return next(); // 继续到下一个中间件
    }

    // 如果是静态资源请求，也不应该被这里处理
    if (req.originalUrl.startsWith('/uploads/') ||
        req.originalUrl.startsWith('/js/') ||
        req.originalUrl.startsWith('/css/') ||
        req.originalUrl.startsWith('/images/') ||
        req.originalUrl.startsWith('/scripts/') ||
        req.originalUrl.startsWith('/components/') ||
        req.originalUrl.startsWith('/assets/')) {
        return next();
    }

    res.sendFile(path.join(__dirname, '../frontend/pages/index.html'));
});

// 错误日志中间件
app.use(errorLogger);

// 错误处理中间件
app.use((err, req, res, next) => {
    logger.error('服务器错误', { error: err.message, stack: err.stack });
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// 启动服务器函数
async function startServer() {
    try {
        // 先完成系统初始化
        await initializeSystem();

        // 然后启动服务器
        const PORT = process.env.PORT || 5050;
        app.listen(PORT, () => {
            console.log(`${formatTimestamp()}✅ 服务器启动成功！`);
            console.log(`${formatTimestamp()}📊 数据库表初始化完成`);
            console.log(formatNetworkAddresses(PORT, config.security.ipWhitelist)); // 显示白名单允许的访问地址
        });
    } catch (error) {
        console.error(`${formatTimestamp()}❌ 服务器启动失败:`, error);
        process.exit(1);
    }
}

// 启动应用
startServer();
