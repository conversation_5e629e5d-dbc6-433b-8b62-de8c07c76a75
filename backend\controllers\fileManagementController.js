/**
 * 文件管理控制器
 * 处理文件管理相关的HTTP请求
 */

const fileManagementService = require('../services/fileManagementService');
const emailService = require('../services/emailService');
const emailQueueManager = require('../services/emailQueueManager');
const SampleFormService = require('../services/sampleFormService');
const { databaseAdapter } = require('../database/databaseAdapter');
const logger = require('../utils/logger');

// 初始化样品单服务
const sampleFormRepository = databaseAdapter.getSampleFormRepository();
const sampleFormService = new SampleFormService(sampleFormRepository);

/**
 * 获取所有客户（包括停用的，用于管理页面）
 */
async function getAllCustomers(req, res) {
    try {
        const customers = await fileManagementService.getAllCustomers();

        res.json({
            success: true,
            data: customers
        });
    } catch (error) {
        logger.error('获取客户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取客户列表失败'
        });
    }
}

/**
 * 获取活跃客户（用于文件上传选择）
 */
async function getActiveCustomers(req, res) {
    try {
        const customers = await fileManagementService.getActiveCustomers();

        res.json({
            success: true,
            data: customers
        });
    } catch (error) {
        logger.error('获取活跃客户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取活跃客户列表失败'
        });
    }
}

/**
 * 创建客户
 */
async function createCustomer(req, res) {
    try {
        const {
            customer_name,
            customer_code,
            contact_person,
            contact_email,
            contact_phone,
            address,
            description,
            active,
            create_user_account // 新增：是否为联系人创建用户账号
        } = req.body;

        // 验证必填字段
        if (!customer_name) {
            return res.status(400).json({
                success: false,
                message: '客户名称为必填项'
            });
        }

        // 如果选择创建用户账号，验证联系人信息
        if (create_user_account && (!contact_person || !contact_email)) {
            return res.status(400).json({
                success: false,
                message: '创建用户账号需要提供联系人姓名和邮箱'
            });
        }

        const customer = await fileManagementService.createCustomer({
            customer_name,
            customer_code,
            contact_person,
            contact_email,
            contact_phone,
            address,
            description,
            active,
            create_user_account
        }, req.user.id);

        res.status(201).json({
            success: true,
            message: customer.userCreated ? '客户创建成功，已为联系人创建用户账号' : '客户创建成功',
            data: customer.customer,
            userAccount: customer.userAccount || null
        });
    } catch (error) {
        logger.error('创建客户失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '创建客户失败'
        });
    }
}

/**
 * 更新客户信息
 */
async function updateCustomer(req, res) {
    try {
        const { id } = req.params;
        const customerData = req.body;

        const customer = await fileManagementService.updateCustomer(id, customerData, req.user.id);

        res.json({
            success: true,
            message: '客户更新成功',
            data: customer
        });
    } catch (error) {
        logger.error('更新客户失败:', error);
        res.status(400).json({
            success: false,
            message: error.message || '更新客户失败'
        });
    }
}

/**
 * 切换客户状态
 */
async function toggleCustomerStatus(req, res) {
    try {
        const { id } = req.params;

        const customer = await fileManagementService.toggleCustomerStatus(id, req.user.id);

        res.json({
            success: true,
            message: '客户状态更新成功',
            data: customer
        });
    } catch (error) {
        logger.error('切换客户状态失败:', error);
        res.status(400).json({
            success: false,
            message: error.message || '切换客户状态失败'
        });
    }
}

/**
 * 删除客户
 */
async function deleteCustomer(req, res) {
    try {
        const { id } = req.params;

        const result = await fileManagementService.deleteCustomer(id, req.user.id);

        res.json({
            success: true,
            message: '客户删除成功',
            data: result
        });
    } catch (error) {
        logger.error('删除客户失败:', error);
        res.status(400).json({
            success: false,
            message: error.message || '删除客户失败'
        });
    }
}

/**
 * 获取历史批次号
 */
async function getHistoricalBatches(req, res) {
    try {
        const { customerId, productModel } = req.query;

        if (!customerId || !productModel) {
            return res.status(400).json({
                success: false,
                message: '客户ID和产品型号为必填参数'
            });
        }

        const batches = await fileManagementService.getHistoricalBatches(customerId, productModel);

        res.json({
            success: true,
            data: batches
        });
    } catch (error) {
        logger.error('获取历史批次失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '获取历史批次失败'
        });
    }
}

/**
 * 获取客户的产品列表
 */
async function getCustomerProducts(req, res) {
    try {
        const { customerId } = req.params;
        const products = await fileManagementService.getCustomerProducts(customerId);

        res.json({
            success: true,
            data: products
        });
    } catch (error) {
        logger.error('获取客户产品列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取客户产品列表失败'
        });
    }
}

/**
 * 获取所有文件记录
 */
async function getAllFiles(req, res) {
    try {
        const files = await fileManagementService.getAllFiles();
        
        res.json({
            success: true,
            data: files
        });
    } catch (error) {
        logger.error('获取文件列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取文件列表失败'
        });
    }
}

/**
 * 根据ID获取文件记录详情
 */
async function getFileById(req, res) {
    try {
        const { id } = req.params;
        const file = await fileManagementService.getFileById(id);
        
        res.json({
            success: true,
            data: file
        });
    } catch (error) {
        logger.error('获取文件详情失败:', error);
        res.status(404).json({
            success: false,
            message: error.message || '获取文件详情失败'
        });
    }
}

/**
 * 预处理客户信息中间件
 * 在文件上传之前获取客户名称
 */
async function preprocessCustomerInfo(req, res, next) {
    try {
        const { customer_id } = req.body;

        if (customer_id) {
            // 获取客户信息
            const customer = await fileManagementService.getCustomerById(customer_id);
            if (customer) {
                // 将客户名称添加到请求体中，供multer使用
                req.body.customer_name = customer.customer_name;
                logger.info(`预处理客户信息: ${customer.customer_name}`, {
                    customerId: customer_id,
                    customerName: customer.customer_name
                });
            }
        }

        next();
    } catch (error) {
        logger.error('预处理客户信息失败:', error);
        // 即使获取客户信息失败，也继续处理，使用默认目录
        next();
    }
}

/**
 * 创建文件记录（支持文件上传）
 */
async function createFileRecord(req, res) {
    try {
        const {
            customer_id,
            product_model,
            batch_number,
            certification_content,
            title,
            description,
            change_description,
            notifyUsers // 要通知的用户ID列表
        } = req.body;

        // 验证必填字段
        if (!customer_id || !product_model || !batch_number || !title) {
            return res.status(400).json({
                success: false,
                message: '客户、产品型号、批次号和标题为必填项'
            });
        }

        // 解析通知用户列表
        let userIds = [];
        if (notifyUsers) {
            try {
                userIds = typeof notifyUsers === 'string' ? JSON.parse(notifyUsers) : notifyUsers;
            } catch (error) {
                logger.error('解析通知用户列表失败:', error);
            }
        }

        // 验证通知用户为必填项
        if (!userIds || userIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择至少一个用户进行邮件通知'
            });
        }

        // 创建文件记录
        const fileRecord = await fileManagementService.createFileRecord({
            customer_id,
            product_model,
            batch_number,
            certification_content,
            title,
            description,
            change_description
        }, req.files, req.user.id);

        // 创建通知记录
        if (userIds.length > 0) {
            await fileManagementService.createNotifications(
                fileRecord.id,
                userIds,
                fileRecord.is_first_version ? 'new_file' : 'file_update'
            );
        }

        // 将邮件发送任务添加到队列（完全异步，不阻塞响应）
        if (userIds.length > 0) {
            emailQueueManager.addToQueue('file_management', {
                fileRecord: fileRecord,
                uploader: req.user,
                userIds: userIds
            });

            logger.info('文件管理邮件通知已添加到发送队列', {
                fileId: fileRecord.id,
                fileNumber: fileRecord.file_number,
                recipientCount: userIds.length
            });
        }

        res.status(201).json({
            success: true,
            message: '文件记录创建成功',
            data: fileRecord
        });
    } catch (error) {
        logger.error('创建文件记录失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '创建文件记录失败'
        });
    }
}

/**
 * 删除文件记录
 */
async function deleteFileRecord(req, res) {
    try {
        const { id } = req.params;
        
        await fileManagementService.deleteFileRecord(id, req.user.id);
        
        res.json({
            success: true,
            message: '文件记录删除成功'
        });
    } catch (error) {
        logger.error('删除文件记录失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '删除文件记录失败'
        });
    }
}

/**
 * 获取用户的通知列表
 */
async function getUserNotifications(req, res) {
    try {
        const notifications = await fileManagementService.getUserNotifications(req.user.id);
        
        res.json({
            success: true,
            data: notifications
        });
    } catch (error) {
        logger.error('获取用户通知失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户通知失败'
        });
    }
}

/**
 * 确认通知
 */
async function confirmNotification(req, res) {
    try {
        const { id } = req.params;
        
        await fileManagementService.confirmNotification(id, req.user.id);
        
        res.json({
            success: true,
            message: '通知确认成功'
        });
    } catch (error) {
        logger.error('确认通知失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '确认通知失败'
        });
    }
}

/**
 * 搜索文件记录
 */
async function searchFiles(req, res) {
    try {
        const searchParams = req.query;
        const files = await fileManagementService.searchFiles(searchParams);
        
        res.json({
            success: true,
            data: files
        });
    } catch (error) {
        logger.error('搜索文件失败:', error);
        res.status(500).json({
            success: false,
            message: '搜索文件失败'
        });
    }
}

/**
 * 获取文件统计信息
 */
async function getFileStatistics(req, res) {
    try {
        const stats = await fileManagementService.getFileStatistics();
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        logger.error('获取文件统计信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取文件统计信息失败'
        });
    }
}

/**
 * 下载文件
 */
async function downloadFile(req, res) {
    try {
        const { fileId, attachmentId } = req.params;
        
        // 获取文件记录
        const file = await fileManagementService.getFileById(fileId);
        if (!file) {
            return res.status(404).json({
                success: false,
                message: '文件记录不存在'
            });
        }

        // 查找指定的附件
        const attachment = file.attachments.find(att => att.id === attachmentId);
        if (!attachment) {
            return res.status(404).json({
                success: false,
                message: '附件不存在'
            });
        }

        // 检查文件是否存在
        const fs = require('fs');
        if (!fs.existsSync(attachment.file_path)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 设置响应头，确保中文文件名正确显示
        const filename = attachment.original_filename;
        const encodedFilename = encodeURIComponent(filename);

        // 设置Content-Disposition头，支持中文文件名
        res.setHeader('Content-Disposition',
            `attachment; filename="${filename}"; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Type', attachment.mime_type);
        res.setHeader('Cache-Control', 'no-cache');

        // 发送文件
        res.sendFile(attachment.file_path);

        logger.info(`文件下载成功: ${attachment.original_filename}`, {
            fileId,
            attachmentId,
            userId: req.user.id
        });
    } catch (error) {
        logger.error('下载文件失败:', error);
        res.status(500).json({
            success: false,
            message: '下载文件失败'
        });
    }
}

/**
 * 获取下一个样品单编号
 */
async function getNextSampleNumber(req, res) {
    try {
        const { databaseAdapter } = require('../database/databaseAdapter');
        const sampleFormRepository = databaseAdapter.getSampleFormRepository();

        const sampleNumber = await sampleFormRepository.generateSampleNumber();

        res.json({
            success: true,
            data: {
                sampleNumber
            }
        });
    } catch (error) {
        logger.error('获取样品单编号失败:', error);
        res.status(500).json({
            success: false,
            message: '获取样品单编号失败'
        });
    }
}

/**
 * 提交样品单
 */
async function submitSampleForm(req, res) {
    try {
        const sampleData = req.body;
        const userId = req.user.id;
        const uploadedFiles = req.files || {};

        // 处理JSON字符串字段
        if (typeof sampleData.recipients === 'string') {
            sampleData.recipients = JSON.parse(sampleData.recipients);
        }
        if (typeof sampleData.ccRecipients === 'string') {
            sampleData.ccRecipients = JSON.parse(sampleData.ccRecipients);
        }
        if (typeof sampleData.testSampleCounts === 'string') {
            sampleData.testSampleCounts = JSON.parse(sampleData.testSampleCounts);
        }

        logger.info('收到样品单提交请求', {
            userId,
            sampleData: {
                ...sampleData,
                recipients: sampleData.recipients?.length || 0,
                ccRecipients: sampleData.ccRecipients?.length || 0
            },
            uploadedFiles: {
                maskPrintFiles: uploadedFiles.maskPrintFiles?.length || 0,
                valvePrintFiles: uploadedFiles.valvePrintFiles?.length || 0,
                colorBoxFiles: uploadedFiles.colorBoxFiles?.length || 0,
                blisterPackFiles: uploadedFiles.blisterPackFiles?.length || 0
            }
        });

        // 验证样品单数据
        sampleFormService.validateSampleFormData(sampleData);

        // 验证收件人
        if (!sampleData.recipients || sampleData.recipients.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择至少一个收件人'
            });
        }

        // 创建样品单
        const sampleForm = await sampleFormService.createSampleForm(
            sampleData,
            uploadedFiles,
            userId
        );

        // 立即返回成功响应
        res.json({
            success: true,
            message: '样品单提交成功',
            data: {
                id: sampleForm.id,
                sampleNumber: sampleForm.sampleNumber,
                generatedNumber: sampleForm.generatedNumber,
                submittedAt: sampleForm.createdAt
            }
        });

        // 将邮件发送任务添加到队列（完全异步，不阻塞响应）
        emailQueueManager.addToQueue('sample_form', {
            sampleRecord: sampleForm,
            recipients: sampleData.recipients,
            ccRecipients: sampleData.ccRecipients || [],
            submittedBy: req.user
        });

        logger.info('样品单邮件通知已添加到发送队列', {
            sampleId: sampleForm.id,
            sampleNumber: sampleForm.sampleNumber,
            recipientCount: sampleData.recipients.length,
            ccCount: sampleData.ccRecipients?.length || 0
        });

    } catch (error) {
        logger.error('提交样品单失败:', error);
        res.status(500).json({
            success: false,
            message: '提交样品单失败: ' + error.message
        });
    }
}

module.exports = {
    getAllCustomers,
    getActiveCustomers,
    createCustomer,
    updateCustomer,
    toggleCustomerStatus,
    deleteCustomer,
    getHistoricalBatches,
    getCustomerProducts,
    getAllFiles,
    getFileById,
    preprocessCustomerInfo,
    createFileRecord,
    deleteFileRecord,
    getUserNotifications,
    confirmNotification,
    searchFiles,
    getFileStatistics,
    downloadFile,
    submitSampleForm,
    getNextSampleNumber
};
