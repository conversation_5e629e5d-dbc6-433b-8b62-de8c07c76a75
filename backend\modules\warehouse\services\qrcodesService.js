/**
 * 二维码管理服务层
 * 处理二维码生成、验证和管理
 */

const { postgresConnectionPool } = require('../../../utils/postgresConnectionPool');
const logger = require('../../../utils/logger');
const crypto = require('crypto');

class QRCodesService {
    constructor() {
        this.db = postgresConnectionPool;
    }

    /**
     * 生成二维码
     */
    async generateQRCode(qrcodeData, operatorId) {
        try {
            const { item_type, item_id, batch_number, expiry_date, notes } = qrcodeData;

            // 验证物品存在
            await this.validateItem(item_type, item_id);

            // 生成唯一二维码
            const qrcode = this.generateUniqueQRCode(item_type, item_id, batch_number);

            // 检查二维码是否已存在
            const existingResult = await this.db.query('SELECT id FROM warehouse_qrcodes WHERE qrcode = $1', [qrcode]);
            if (existingResult.rows.length > 0) {
                throw new Error('二维码已存在，请重新生成');
            }

            const result = await this.db.query(`
                INSERT INTO warehouse_qrcodes (
                    qrcode, item_type, item_id, batch_number,
                    expiry_date, notes, status, created_by,
                    created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, 'active', $7, NOW(), NOW())
                RETURNING id
            `, [qrcode, item_type, item_id, batch_number, expiry_date, notes, operatorId]);

            logger.info(`二维码生成成功: ${qrcode}`, {
                item_type,
                item_id,
                batch_number,
                operatorId
            });

            return {
                id: result.rows[0].id,
                qrcode,
                item_type,
                item_id,
                batch_number,
                status: 'active'
            };
        } catch (error) {
            logger.error('生成二维码失败:', error);
            throw error;
        }
    }

    /**
     * 验证二维码
     */
    async validateQRCode(qrcode, expectedItemType = null, expectedItemId = null) {
        try {
            const result = await this.db.query(`
                SELECT q.*,
                       CASE
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE q.qrcode = $1
            `, [qrcode]);

            if (result.rows.length === 0) {
                throw new Error('二维码不存在');
            }

            const qrcodeInfo = result.rows[0];

            if (qrcodeInfo.status !== 'active') {
                throw new Error(`二维码状态异常: ${qrcodeInfo.status}`);
            }

            // 检查过期时间
            if (qrcodeInfo.expiry_date) {
                const expiryDate = new Date(qrcodeInfo.expiry_date);
                const now = new Date();
                if (now > expiryDate) {
                    throw new Error('二维码已过期');
                }
            }

            // 验证物品类型和ID（如果指定）
            if (expectedItemType && qrcodeInfo.item_type !== expectedItemType) {
                throw new Error(`二维码物品类型不匹配，期望: ${expectedItemType}, 实际: ${qrcodeInfo.item_type}`);
            }

            if (expectedItemId && qrcodeInfo.item_id !== expectedItemId) {
                throw new Error(`二维码物品ID不匹配，期望: ${expectedItemId}, 实际: ${qrcodeInfo.item_id}`);
            }

            return qrcodeInfo;
        } catch (error) {
            logger.error('验证二维码失败:', error);
            throw error;
        }
    }

    /**
     * 获取二维码信息
     */
    async getQRCodeInfo(qrcode) {
        try {
            const qrcodeResult = await this.db.query(`
                SELECT q.*,
                       u.username as created_by_name,
                       CASE
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN users u ON q.created_by = u.id
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE q.qrcode = $1
            `, [qrcode]);

            if (qrcodeResult.rows.length === 0) {
                throw new Error('二维码不存在');
            }

            const qrcodeInfo = qrcodeResult.rows[0];

            // 获取使用历史
            const usageResult = await this.db.query(`
                SELECT t.*, u.username as operator_name
                FROM warehouse_inventory_transactions t
                LEFT JOIN users u ON t.operator_id = u.id
                WHERE t.qrcode = $1
                ORDER BY t.transaction_date DESC
            `, [qrcode]);

            return {
                ...qrcodeInfo,
                usage_history: usageResult.rows
            };
        } catch (error) {
            logger.error('获取二维码信息失败:', error);
            throw error;
        }
    }

    /**
     * 使用二维码（标记为已使用）
     */
    async useQRCode(id, operatorId, notes = null) {
        try {
            const qrcodeResult = await this.db.query('SELECT * FROM warehouse_qrcodes WHERE id = $1', [id]);

            if (qrcodeResult.rows.length === 0) {
                throw new Error('二维码不存在');
            }

            const qrcodeInfo = qrcodeResult.rows[0];

            if (qrcodeInfo.status !== 'active') {
                throw new Error(`二维码状态异常: ${qrcodeInfo.status}`);
            }

            const result = await this.db.query(`
                UPDATE warehouse_qrcodes
                SET status = 'used', used_by = $1, used_at = NOW(),
                    notes = CASE WHEN $2 IS NOT NULL THEN $2 ELSE notes END,
                    updated_at = NOW()
                WHERE id = $3
            `, [operatorId, notes, id]);

            if (result.rowCount === 0) {
                throw new Error('更新二维码状态失败');
            }

            logger.info(`二维码使用成功: ${qrcodeInfo.qrcode}`, { operatorId });

            return {
                id,
                qrcode: qrcodeInfo.qrcode,
                status: 'used',
                used_by: operatorId,
                used_at: new Date().toISOString()
            };
        } catch (error) {
            logger.error('使用二维码失败:', error);
            throw error;
        }
    }

    /**
     * 批量生成二维码
     */
    async batchGenerateQRCodes(batchData, operatorId) {
        try {
            const { item_type, item_id, batch_number, quantity, expiry_date, notes } = batchData;

            // 验证物品存在
            await this.validateItem(item_type, item_id);

            const qrcodes = [];

            // PostgreSQL事务处理
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');

                for (let i = 1; i <= quantity; i++) {
                    const qrcode = this.generateUniqueQRCode(item_type, item_id, batch_number, i);

                    const result = await client.query(`
                        INSERT INTO warehouse_qrcodes (
                            qrcode, item_type, item_id, batch_number,
                            expiry_date, notes, status, created_by,
                            created_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, 'active', $7, NOW(), NOW())
                        RETURNING id
                    `, [qrcode, item_type, item_id, batch_number, expiry_date, notes, operatorId]);

                    qrcodes.push({
                        id: result.rows[0].id,
                        qrcode,
                        item_type,
                        item_id,
                        batch_number
                    });
                }

                await client.query('COMMIT');
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }

            logger.info(`批量生成二维码成功: ${quantity}个`, { 
                item_type, 
                item_id, 
                batch_number, 
                operatorId 
            });

            return qrcodes;
        } catch (error) {
            logger.error('批量生成二维码失败:', error);
            throw error;
        }
    }

    /**
     * 生成唯一二维码
     */
    generateUniqueQRCode(itemType, itemId, batchNumber = null, sequence = null) {
        const timestamp = Date.now().toString(36); // 时间戳转36进制
        const random = crypto.randomBytes(4).toString('hex').toUpperCase(); // 8位随机字符
        
        let qrcode = `${itemType.toUpperCase()}-${itemId}-${timestamp}-${random}`;
        
        if (batchNumber) {
            qrcode += `-B${batchNumber}`;
        }
        
        if (sequence) {
            qrcode += `-S${sequence.toString().padStart(3, '0')}`;
        }
        
        return qrcode;
    }

    /**
     * 验证物品是否存在
     */
    async validateItem(itemType, itemId) {
        try {
            let exists = false;

            if (itemType === 'material') {
                const materialResult = await this.db.query('SELECT id FROM warehouse_materials WHERE id = $1 AND status = $2', [itemId, 'active']);
                exists = materialResult.rows.length > 0;
            } else if (itemType === 'product') {
                const productResult = await this.db.query('SELECT id FROM warehouse_finished_products WHERE id = $1 AND status = $2', [itemId, 'active']);
                exists = productResult.rows.length > 0;
            }

            if (!exists) {
                throw new Error(`${itemType === 'material' ? '物料' : '成品'}不存在或状态异常`);
            }

            return true;
        } catch (error) {
            logger.error('验证物品失败:', error);
            throw error;
        }
    }

    /**
     * 获取二维码列表
     */
    async getQRCodes(filters = {}) {
        try {
            let query = `
                SELECT q.*, 
                       u.username as created_by_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN users u ON q.created_by = u.id
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE 1=1
            `;

            const params = [];
            let paramIndex = 1;

            if (filters.item_type) {
                query += ` AND q.item_type = $${paramIndex++}`;
                params.push(filters.item_type);
            }

            if (filters.item_id) {
                query += ` AND q.item_id = $${paramIndex++}`;
                params.push(filters.item_id);
            }

            if (filters.status) {
                query += ` AND q.status = $${paramIndex++}`;
                params.push(filters.status);
            }

            if (filters.batch_number) {
                query += ` AND q.batch_number = $${paramIndex++}`;
                params.push(filters.batch_number);
            }

            query += ' ORDER BY q.created_at DESC';

            if (filters.limit) {
                query += ` LIMIT $${params.length + 1}`;
                params.push(parseInt(filters.limit));
            }

            const result = await this.db.query(query, params);
            return result.rows;
        } catch (error) {
            logger.error('获取二维码列表失败:', error);
            throw error;
        }
    }
}

module.exports = new QRCodesService();
