/**
 * 文件上传主页
 * 提供文件上传类型选择功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref, onMounted } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);



        // 页面加载完成后的初始化
        onMounted(() => {
            // 简单的页面加载指示
            const progressIndicator = document.getElementById('progressIndicator');
            if (progressIndicator) {
                progressIndicator.classList.add('active');
                setTimeout(() => {
                    progressIndicator.classList.remove('active');
                }, 1000);
            }

            console.log('文件管理中心页面已加载');
        });

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_upload'],
    onUserLoaded: async (user) => {
        console.log('文件上传主页加载完成，当前用户:', user.username);
    }
}).mount('#app');
