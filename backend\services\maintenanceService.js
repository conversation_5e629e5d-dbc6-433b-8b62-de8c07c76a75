/**
 * 维修保养记录服务层
 * 处理维修保养记录相关的业务逻辑
 */

const { databaseAdapter } = require('../database/databaseAdapter');
const { MaintenanceModel } = require('../models/maintenanceModel');
const logger = require('../utils/logger');

class MaintenanceService {
    constructor() {
        this.maintenanceRepository = databaseAdapter.getMaintenanceRepository();
        this.equipmentRepository = databaseAdapter.getEquipmentRepository();
    }

    /**
     * 获取维修记录列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 查询结果
     */
    async getMaintenanceRecords(options = {}) {
        try {
            // 如果没有分页参数，返回所有数据
            if (!options.page && !options.limit) {
                const records = await this.maintenanceRepository.findAll();
                const statistics = await this.maintenanceRepository.getStatistics();

                return {
                    success: true,
                    data: {
                        records: records.map(record => MaintenanceModel.fromDatabase(record).toResponse()),
                        statistics
                    }
                };
            }

            // 分页查询
            const result = await this.maintenanceRepository.findAllWithPagination(options);

            return {
                success: true,
                data: {
                    records: result.data.map(record => MaintenanceModel.fromDatabase(record).toResponse()),
                    pagination: result.pagination,
                    statistics: await this.maintenanceRepository.getStatistics()
                }
            };
        } catch (error) {
            logger.error('获取维修记录列表失败', { error: error.message });
            return {
                success: false,
                message: '获取维修记录列表失败',
                error: error.message
            };
        }
    }

    /**
     * 根据ID获取维修记录详情
     * @param {string} id 记录ID
     * @returns {Promise<Object>} 查询结果
     */
    async getMaintenanceRecordById(id) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            const record = await this.maintenanceRepository.findById(id);
            
            if (!record) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(record).toResponse()
            };
        } catch (error) {
            logger.error('获取维修记录详情失败', { error: error.message, recordId: id });
            return {
                success: false,
                message: '获取维修记录详情失败',
                error: error.message
            };
        }
    }

    /**
     * 生成记录编号
     * @param {string} areaId 厂区ID
     * @param {string} date 日期 (YYYY-MM-DD)
     * @returns {Promise<string>} 记录编号
     */
    async generateRecordNumber(areaId, date) {
        // 格式: 厂区ID + 日期(YYYYMMDD) + 序号(001)
        const areaCode = areaId || 'XX';
        const dateCode = date ? date.replace(/-/g, '') : new Date().toISOString().split('T')[0].replace(/-/g, '');

        // 查询当天已有的记录数量
        const today = date || new Date().toISOString().split('T')[0];
        const existingRecords = await this.maintenanceRepository.findByDate(today);
        const sequence = String(existingRecords.length + 1).padStart(3, '0');

        return `${areaCode}${dateCode}${sequence}`;
    }

    /**
     * 创建维修记录
     * @param {Object} recordData 记录数据
     * @param {string} userId 创建用户ID
     * @returns {Promise<Object>} 创建结果
     */
    async createMaintenanceRecord(recordData, userId) {
        try {
            // 统一字段名称（支持equipmentId和equipment_id两种格式）
            const equipmentId = recordData.equipmentId || recordData.equipment_id;
            const maintenanceDate = recordData.maintenanceDate || recordData.maintenance_date;

            // 验证设备是否存在
            const equipment = await this.equipmentRepository.findById(equipmentId);
            if (!equipment) {
                return {
                    success: false,
                    message: '指定的设备不存在'
                };
            }

            // 根据设备的厂区获取厂区信息
            let factory = this.equipmentRepository.findFactoryByName(equipment.area);
            let areaId;

            if (factory) {
                areaId = factory.id;
            } else {
                // 如果factories表中没有对应的厂区，使用厂区名称前两个字符作为ID
                areaId = equipment.area ? equipment.area.substring(0, 2).toUpperCase() : 'XX';
            }

            // 生成记录编号
            const recordId = await this.generateRecordNumber(areaId, maintenanceDate);

            // 创建维修记录模型（统一字段名称）
            const maintenance = new MaintenanceModel({
                ...recordData,
                id: recordId,
                equipmentId: equipmentId,
                maintenanceDate: maintenanceDate
            });

            // 验证数据
            const validation = maintenance.validate();
            if (!validation.isValid) {
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 创建记录
            const createdRecord = await this.maintenanceRepository.create(maintenance);

            logger.info('维修记录创建成功', {
                recordId: createdRecord.id,
                equipmentId: createdRecord.equipment_id,
                type: createdRecord.type,
                createdBy: userId
            });

            // 自动触发设备健康度重新计算
            try {
                const HealthAssessmentService = require('./healthAssessmentService');
                const healthService = new HealthAssessmentService();
                await healthService.calculateEquipmentHealth(createdRecord.equipment_id, 'system_auto');
                logger.info('维修记录创建后自动更新健康度成功', { equipmentId: createdRecord.equipment_id });
            } catch (healthError) {
                logger.error('维修记录创建后自动更新健康度失败', {
                    equipmentId: createdRecord.equipment_id,
                    error: healthError.message
                });
                // 健康度更新失败不影响维修记录创建的成功
            }

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(createdRecord).toResponse(),
                message: '维修记录创建成功'
            };
        } catch (error) {
            logger.error('创建维修记录失败', { error: error.message, userId });
            return {
                success: false,
                message: '创建维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 更新维修记录
     * @param {string} id 记录ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 更新用户ID
     * @returns {Promise<Object>} 更新结果
     */
    async updateMaintenanceRecord(id, updateData, userId) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            // 检查记录是否存在
            const existingRecord = await this.maintenanceRepository.findById(id);
            if (!existingRecord) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            // 创建更新后的模型
            const maintenance = new MaintenanceModel({
                ...existingRecord,
                ...updateData,
                id: id,
                updatedAt: new Date().toISOString()
            });

            // 验证数据
            const validation = maintenance.validate();
            if (!validation.isValid) {
                logger.error('维修记录数据验证失败', {
                    recordId: id,
                    errors: validation.errors,
                    maintenanceData: maintenance
                });
                return {
                    success: false,
                    message: '数据验证失败',
                    errors: validation.errors
                };
            }

            // 如果更新了设备ID，验证设备是否存在
            if (updateData.equipmentId && updateData.equipmentId !== existingRecord.equipment_id) {
                const equipment = this.equipmentRepository.findById(maintenance.equipmentId);
                if (!equipment) {
                    return {
                        success: false,
                        message: '指定的设备不存在'
                    };
                }
            }

            // 更新记录
            const updatedRecord = await this.maintenanceRepository.update(id, maintenance);

            logger.info('维修记录更新成功', {
                recordId: id,
                updatedBy: userId
            });

            // 自动触发设备健康度重新计算
            try {
                const HealthAssessmentService = require('./healthAssessmentService');
                const healthService = new HealthAssessmentService();
                await healthService.calculateEquipmentHealth(updatedRecord.equipment_id, 'system_auto');
                logger.info('维修记录更新后自动更新健康度成功', { equipmentId: updatedRecord.equipment_id });
            } catch (healthError) {
                logger.error('维修记录更新后自动更新健康度失败', {
                    equipmentId: updatedRecord.equipment_id,
                    error: healthError.message
                });
                // 健康度更新失败不影响维修记录更新的成功
            }

            return {
                success: true,
                data: MaintenanceModel.fromDatabase(updatedRecord).toResponse(),
                message: '维修记录更新成功'
            };
        } catch (error) {
            logger.error('更新维修记录失败', { error: error.message, recordId: id, userId });
            return {
                success: false,
                message: '更新维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 删除维修记录
     * @param {string} id 记录ID
     * @param {string} userId 删除用户ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteMaintenanceRecord(id, userId) {
        try {
            if (!id) {
                return {
                    success: false,
                    message: '记录ID不能为空'
                };
            }

            // 检查记录是否存在
            const existingRecord = await this.maintenanceRepository.findById(id);
            if (!existingRecord) {
                return {
                    success: false,
                    message: '维修记录不存在'
                };
            }

            // 检查是否可以删除（例如：进行中的维修不能删除）
            if (existingRecord.status === 'in_progress') {
                return {
                    success: false,
                    message: '进行中的维修记录不能删除'
                };
            }

            // 删除记录
            const deleted = await this.maintenanceRepository.delete(id);
            
            if (!deleted) {
                return {
                    success: false,
                    message: '删除维修记录失败'
                };
            }

            logger.info('维修记录删除成功', {
                recordId: id,
                deletedBy: userId
            });

            // 自动触发设备健康度重新计算
            try {
                const HealthAssessmentService = require('./healthAssessmentService');
                const healthService = new HealthAssessmentService();
                await healthService.calculateEquipmentHealth(existingRecord.equipment_id, 'system_auto');
                logger.info('维修记录删除后自动更新健康度成功', { equipmentId: existingRecord.equipment_id });
            } catch (healthError) {
                logger.error('维修记录删除后自动更新健康度失败', {
                    equipmentId: existingRecord.equipment_id,
                    error: healthError.message
                });
                // 健康度更新失败不影响维修记录删除的成功
            }

            return {
                success: true,
                message: '维修记录删除成功'
            };
        } catch (error) {
            logger.error('删除维修记录失败', { error: error.message, recordId: id, userId });
            return {
                success: false,
                message: '删除维修记录失败',
                error: error.message
            };
        }
    }

    /**
     * 获取统计数据
     * @returns {Promise<Object>} 统计结果
     */
    async getStatistics() {
        try {
            const statistics = await this.maintenanceRepository.getStatistics();
            
            return {
                success: true,
                data: statistics
            };
        } catch (error) {
            logger.error('获取维修记录统计数据失败', { error: error.message });
            return {
                success: false,
                message: '获取统计数据失败',
                error: error.message
            };
        }
    }

    /**
     * 获取维修类型和状态选项
     * @returns {Object} 选项数据
     */
    getOptions() {
        return {
            types: MaintenanceModel.getTypeOptions(),
            statuses: MaintenanceModel.getStatusOptions()
        };
    }

    /**
     * 获取维修保养记录选项数据
     * @returns {Object} 选项数据
     */
    async getMaintenanceOptions() {
        try {
            // 获取厂区列表
            let factories = [];
            try {
                factories = await this.equipmentRepository.findAllFactories();
            } catch (error) {
                logger.warn('获取厂区列表失败，使用默认数据', { error: error.message });
                factories = [];
            }

            // 如果factories表没有数据，从equipment表的area字段获取
            if (!factories || factories.length === 0) {
                try {
                    const filterOptions = await this.equipmentRepository.getFilterOptions();
                    if (filterOptions && filterOptions.areas && Array.isArray(filterOptions.areas)) {
                        factories = filterOptions.areas.map(area => ({
                            id: area.area ? area.area.substring(0, 2).toUpperCase() : 'DEFAULT',
                            name: area.area || '默认厂区'
                        }));
                    }
                } catch (error) {
                    logger.warn('获取设备筛选选项失败，使用默认厂区', { error: error.message });
                    factories = [{ id: 'DEFAULT', name: '默认厂区' }];
                }
            }

            // 获取设备筛选选项（位置等）
            let filterOptions = { locations: [] };
            try {
                filterOptions = await this.equipmentRepository.getFilterOptions();
            } catch (error) {
                logger.warn('获取设备筛选选项失败，使用默认数据', { error: error.message });
            }

            const options = {
                types: [
                    { value: 'maintenance', label: '保养' },
                    { value: 'repair', label: '维修' }
                ],
                statuses: [
                    { value: 'pending', label: '待处理' },
                    { value: 'in_progress', label: '进行中' },
                    { value: 'completed', label: '已完成' },
                    { value: 'cancelled', label: '已取消' }
                ],
                severityLevels: [
                    { value: 'low', label: '轻微' },
                    { value: 'medium', label: '中等' },
                    { value: 'high', label: '严重' },
                    { value: 'critical', label: '紧急' }
                ],
                areas: Array.isArray(factories) ? factories.map(factory => ({
                    value: factory.id,
                    label: factory.name
                })) : [],
                locations: (filterOptions.locations && Array.isArray(filterOptions.locations)) ?
                    filterOptions.locations.map(location => ({
                        value: location.location,
                        label: location.location
                    })) : []
            };

            return {
                success: true,
                data: options,
                message: '获取选项数据成功'
            };
        } catch (error) {
            logger.error('获取维修保养记录选项数据失败', { error: error.message });
            return {
                success: false,
                message: '获取选项数据失败: ' + error.message
            };
        }
    }
}

module.exports = MaintenanceService;
