/* 样品单表单专用样式 - Sample Form CSS */

/* 邮件通知设置区域优化 */
.email-notification-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

/* 收件人和抄送人区域布局 */
.recipient-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .recipient-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* 收件人/抄送人卡片 */
.recipient-card {
    background: white;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.2s ease;
}

.recipient-card.to-recipients {
    border-color: #dbeafe;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.recipient-card.cc-recipients {
    border-color: #dcfce7;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.recipient-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 选择按钮优化 */
.select-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.select-button.primary {
    background: #3b82f6;
    color: white;
}

.select-button.primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.select-button.success {
    background: #10b981;
    color: white;
}

.select-button.success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.select-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

/* 用户标签优化 */
.user-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.user-tag.to-user {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.user-tag.cc-user {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #86efac;
}

.user-tag:hover {
    transform: scale(1.02);
}

.user-tag .remove-btn {
    margin-left: 0.25rem;
    font-weight: 700;
    cursor: pointer;
    transition: color 0.2s ease;
}

.user-tag.to-user .remove-btn:hover {
    color: #1d4ed8;
}

.user-tag.cc-user .remove-btn:hover {
    color: #15803d;
}

/* 用户选择列表优化 */
.user-selection-panel {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
}

.user-selection-panel.to-panel {
    border: 2px solid #3b82f6;
    background: #eff6ff;
}

.user-selection-panel.cc-panel {
    border: 2px solid #10b981;
    background: #f0fdf4;
}

.user-selection-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.user-selection-header.to-header {
    background: #dbeafe;
    color: #1e40af;
    border-bottom: 1px solid #93c5fd;
}

.user-selection-header.cc-header {
    background: #dcfce7;
    color: #166534;
    border-bottom: 1px solid #86efac;
}

.close-btn {
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* 搜索框优化 */
.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-input.to-search {
    border-color: #93c5fd;
    background: white;
}

.search-input.to-search:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input.cc-search {
    border-color: #86efac;
    background: white;
}

.search-input.cc-search:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 用户列表项优化 */
.user-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.user-item:last-child {
    border-bottom: none;
}

.user-item:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
}

.user-item.selected.to-selected {
    background: #dbeafe;
    border-color: #93c5fd;
}

.user-item.selected.cc-selected {
    background: #dcfce7;
    border-color: #86efac;
}

/* 复选框优化 */
.custom-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.custom-checkbox.to-checkbox.checked {
    background: #3b82f6;
    border-color: #3b82f6;
}

.custom-checkbox.cc-checkbox.checked {
    background: #10b981;
    border-color: #10b981;
}

.custom-checkbox svg {
    width: 0.75rem;
    height: 0.75rem;
    color: white;
}

/* 用户信息显示 */
.user-info {
    margin-left: 1rem;
    flex: 1;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.125rem;
}

.user-email {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.125rem;
}

.user-department {
    font-size: 0.75rem;
    color: #9ca3af;
}

/* 统计标签 */
.count-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.count-badge.to-count {
    background: #dbeafe;
    color: #1e40af;
}

.count-badge.cc-count {
    background: #dcfce7;
    color: #166534;
}

/* 空状态提示 */
.empty-state {
    padding: 2rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

/* 响应式优化 */
@media (max-width: 640px) {
    .user-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .select-button {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .user-item {
        padding: 0.75rem;
    }
    
    .user-selection-header {
        padding: 0.75rem;
    }
}
