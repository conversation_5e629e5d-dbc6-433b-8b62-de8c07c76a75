/**
 * PostgreSQL申请数据访问层
 * 替换SQLite的applicationRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresApplicationRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL申请数据访问层初始化完成');
        }
    }

    /**
     * 获取所有申请
     */
    async findAll() {
        try {
            const applications = await this.findMany(`
                SELECT * FROM applications 
                ORDER BY created_at DESC
            `);
            return applications.map(app => this.transformApplication(app));
        } catch (error) {
            logger.error('获取所有申请失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找申请
     */
    async findById(id) {
        try {
            const application = await this.findOne('SELECT * FROM applications WHERE id = $1', [id]);
            if (!application) return null;

            // 获取审批历史
            const approvalHistory = await this.findMany(`
                SELECT * FROM approval_history
                WHERE application_id = $1
                ORDER BY timestamp ASC
            `, [id]);

            const transformedApplication = this.transformApplication(application);
            transformedApplication.approvalHistory = approvalHistory.map(approval => ({
                id: approval.id,
                applicationId: approval.application_id,
                approverId: approval.approver_id,
                approverName: approval.approver_name,
                stage: approval.stage,
                action: approval.action,
                comment: approval.comment,
                timestamp: approval.timestamp, // approval_history表使用timestamp字段
                signaturePath: approval.signature_path
            }));

            return transformedApplication;
        } catch (error) {
            logger.error(`根据ID查找申请失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据用户ID查找申请
     */
    async findByUserId(userId) {
        try {
            const applications = await this.findMany(`
                SELECT * FROM applications WHERE user_id = $1 
                ORDER BY created_at DESC
            `, [userId]);
            return applications.map(app => this.transformApplication(app));
        } catch (error) {
            logger.error(`根据用户ID查找申请失败 (${userId}):`, error);
            throw error;
        }
    }

    /**
     * 根据状态查找申请
     */
    async findByStatus(status) {
        try {
            const applications = await this.findMany(`
                SELECT * FROM applications WHERE status = $1 
                ORDER BY created_at DESC
            `, [status]);
            return applications.map(app => this.transformApplication(app));
        } catch (error) {
            logger.error(`根据状态查找申请失败 (${status}):`, error);
            throw error;
        }
    }

    /**
     * 根据当前阶段查找申请
     */
    async findByStage(stage) {
        try {
            const applications = await this.findMany(`
                SELECT * FROM applications 
                WHERE current_stage = $1 AND status = 'pending'
                ORDER BY created_at DESC
            `, [stage]);
            return applications.map(app => this.transformApplication(app));
        } catch (error) {
            logger.error(`根据阶段查找申请失败 (${stage}):`, error);
            throw error;
        }
    }

    /**
     * 创建新申请
     */
    async create(applicationData) {
        try {
            const now = new Date().toISOString();
            
            const result = await this.query(`
                INSERT INTO applications (
                    id, application_number, user_id, applicant, department, date,
                    content, amount, priority, type, status, current_stage,
                    need_manager_approval, need_ceo_approval, selected_factory_managers,
                    selected_managers, pdf_path, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
                RETURNING *
            `, [
                applicationData.id,
                applicationData.applicationNumber,
                applicationData.userId,
                applicationData.applicant,
                applicationData.department,
                applicationData.date,
                applicationData.content,
                applicationData.amount,
                applicationData.priority || 'normal',
                applicationData.type || 'standard',
                applicationData.status || 'pending',
                applicationData.currentStage,
                applicationData.needManagerApproval || false,
                applicationData.needCeoApproval !== false,
                JSON.stringify(applicationData.selectedFactoryManagers || []),
                JSON.stringify(applicationData.selectedManagers || []),
                applicationData.pdfPath,
                now,
                now
            ]);

            return this.transformApplication(result.rows[0]);
        } catch (error) {
            logger.error('创建申请失败:', error);
            throw error;
        }
    }

    /**
     * 更新申请
     */
    async update(id, applicationData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE applications SET 
                    applicant = $2, department = $3, date = $4, content = $5,
                    amount = $6, priority = $7, type = $8, status = $9,
                    current_stage = $10, need_manager_approval = $11, need_ceo_approval = $12,
                    selected_factory_managers = $13, selected_managers = $14, pdf_path = $15,
                    updated_at = $16
                WHERE id = $1
                RETURNING *
            `, [
                id,
                applicationData.applicant,
                applicationData.department,
                applicationData.date,
                applicationData.content,
                applicationData.amount,
                applicationData.priority,
                applicationData.type,
                applicationData.status,
                applicationData.currentStage,
                applicationData.needManagerApproval,
                applicationData.needCeoApproval,
                JSON.stringify(applicationData.selectedFactoryManagers || []),
                JSON.stringify(applicationData.selectedManagers || []),
                applicationData.pdfPath,
                now
            ]);

            return result.rows.length > 0 ? this.transformApplication(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新申请失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 更新申请状态
     */
    async updateStatus(id, status, currentStage = null) {
        try {
            const now = new Date().toISOString();
            let query = 'UPDATE applications SET status = $2, updated_at = $3';
            let params = [id, status, now];
            
            if (currentStage !== null) {
                query += ', current_stage = $4';
                params.push(currentStage);
            }
            
            query += ' WHERE id = $1';
            
            const rowCount = await super.update(query, params);
            return rowCount > 0;
        } catch (error) {
            logger.error(`更新申请状态失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除申请
     */
    async delete(id) {
        try {
            const rowCount = await super.delete('DELETE FROM applications WHERE id = $1', [id]);
            return rowCount > 0;
        } catch (error) {
            logger.error(`删除申请失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据申请编号查找申请
     */
    async findByApplicationNumber(applicationNumber) {
        try {
            const application = await this.findOne(
                'SELECT * FROM applications WHERE application_number = $1',
                [applicationNumber]
            );
            return application ? this.transformApplication(application) : null;
        } catch (error) {
            logger.error(`根据申请编号查找申请失败 (${applicationNumber}):`, error);
            throw error;
        }
    }

    /**
     * 检查申请编号是否存在
     */
    async isApplicationNumberExists(applicationNumber) {
        try {
            const result = await this.query(
                'SELECT COUNT(*) as count FROM applications WHERE application_number = $1',
                [applicationNumber]
            );
            return parseInt(result.rows[0].count) > 0;
        } catch (error) {
            logger.error(`检查申请编号是否存在失败 (${applicationNumber}):`, error);
            throw error;
        }
    }

    /**
     * 分页查询申请
     */
    async findWithPagination(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                status,
                userId,
                department,
                startDate,
                endDate
            } = options;

            let whereClause = '';
            const params = [];
            const conditions = [];

            if (status) {
                conditions.push(`status = $${params.length + 1}`);
                params.push(status);
            }

            if (userId) {
                conditions.push(`user_id = $${params.length + 1}`);
                params.push(userId);
            }

            if (department) {
                conditions.push(`department = $${params.length + 1}`);
                params.push(department);
            }

            if (startDate) {
                conditions.push(`date >= $${params.length + 1}`);
                params.push(startDate);
            }

            if (endDate) {
                conditions.push(`date <= $${params.length + 1}`);
                params.push(endDate);
            }

            if (conditions.length > 0) {
                whereClause = 'WHERE ' + conditions.join(' AND ');
            }

            const baseQuery = `SELECT * FROM applications ${whereClause}`;
            const result = await this.paginate(
                baseQuery,
                params,
                page,
                limit,
                'ORDER BY created_at DESC'
            );

            return {
                ...result,
                data: result.data.map(app => this.transformApplication(app))
            };
        } catch (error) {
            logger.error('分页查询申请失败:', error);
            throw error;
        }
    }

    /**
     * 转换申请数据
     */
    transformApplication(application) {
        if (!application) return null;
        
        return {
            id: application.id,
            applicationNumber: application.application_number,
            userId: application.user_id,
            applicant: application.applicant,
            department: application.department,
            date: application.date,
            content: application.content,
            amount: application.amount,
            priority: application.priority,
            type: application.type,
            status: application.status,
            currentStage: application.current_stage,
            needManagerApproval: application.need_manager_approval,
            needCeoApproval: application.need_ceo_approval,
            selectedFactoryManagers: this.parseJSON(application.selected_factory_managers, []),
            selectedManagers: this.parseJSON(application.selected_managers, []),
            pdfPath: application.pdf_path,
            createdAt: application.created_at,
            updatedAt: application.updated_at
        };
    }

    /**
     * 生成申请ID
     */
    generateId() {
        return super.generateId();
    }
}

module.exports = PostgresApplicationRepository;
