<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维修/保养记录管理 - Makrite管理系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon.svg">
    <link rel="shortcut icon" href="/favicon.ico">

    <!-- Tailwind CSS -->
    <script src="/js/libs/tailwindcss.min.js"></script>

    <!-- Vue.js 3 -->
    <script src="/js/libs/vue.global.js"></script>

    <!-- Axios -->
    <script src="/js/libs/axios.min.js"></script>

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/equipment/maintenance.css">


</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="toggleSidebar" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="closeSidebar" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-lg md:text-xl font-semibold text-gray-800 mb-4 md:mb-6">维修/保养记录管理</h2>
                <!-- 操作按钮区域 -->
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-end mb-4 md:mb-6 space-y-3 sm:space-y-0">
                    <div class="flex flex-wrap items-center gap-2 sm:gap-3 w-full sm:w-auto">
                        <button
                            @click="showCreateForm"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="hidden sm:inline">新增记录</span>
                            <span class="sm:hidden">新增</span>
                        </button>

                        <button
                            @click="batchDeleteRecords"
                            :disabled="selectedRecords.length === 0"
                            :class="[
                                'px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center',
                                selectedRecords.length === 0
                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    : 'bg-red-500 hover:bg-red-600 text-white'
                            ]"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span class="hidden sm:inline">批量删除 {{ selectedRecords.length > 0 ? `(${selectedRecords.length})` : '' }}</span>
                            <span class="sm:hidden">删除{{ selectedRecords.length > 0 ? `(${selectedRecords.length})` : '' }}</span>
                        </button>

                        <button
                            @click="showImportDialog"
                            class="bg-green-500 hover:bg-green-600 text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            <span class="hidden sm:inline">导入记录</span>
                            <span class="sm:hidden">导入</span>
                        </button>

                        <button
                            @click="exportRecords"
                            :disabled="isExporting"
                            :class="isExporting ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'"
                            class="text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center"
                        >
                            <svg v-if="!isExporting" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <svg v-if="isExporting" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="hidden sm:inline">{{ isExporting ? '导出中...' : '导出记录' }}</span>
                            <span class="sm:hidden">{{ isExporting ? '导出中' : '导出' }}</span>
                        </button>

                        <button
                            @click="refreshData"
                            :disabled="refreshing"
                            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-3 md:px-4 py-2 rounded-lg flex items-center space-x-2 text-sm flex-1 sm:flex-none justify-center transition-colors"
                        >
                            <svg v-if="refreshing" class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span class="hidden sm:inline">{{ refreshing ? '刷新中...' : '刷新' }}</span>
                        </button>
                    </div>
                </div>



                <!-- 维修记录列表 -->
                <maintenance-list
                    ref="maintenanceListRef"
                    :refresh-trigger="refreshTrigger"
                    :selected-records="selectedRecords"
                    @edit="handleEdit"
                    @view="handleView"
                    @selection-change="handleSelectionChange"
                />
            </div>
        </div>

        <!-- 创建/编辑表单模态框 -->
        <div v-if="showForm" class="fixed inset-0 z-50">
            <div class="modal-overlay fixed inset-0"></div>
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <div class="slide-in inline-block align-bottom bg-white rounded-lg text-left shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full max-h-[85vh] overflow-y-auto">
                    <maintenance-form
                        :record="selectedRecord"
                        :is-edit="isEdit"
                        @success="handleFormSuccess"
                        @cancel="closeForm"
                    />
                </div>
            </div>
        </div>

        <!-- 记录详情模态框 -->
        <div v-if="showDetail" class="fixed inset-0 z-50 flex items-center justify-center p-4">
            <div class="modal-overlay fixed inset-0" @click="closeDetail"></div>
            <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <!-- 模态框头部 -->
                <div class="bg-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">维修保养记录详情</h3>
                        <button @click="closeDetail" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 模态框内容 -->
                <div class="bg-white px-6 py-6">
                    <div v-if="selectedRecord" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">记录编号</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ selectedRecord.id || '-' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">关联设备</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                    {{ selectedRecord.equipment?.code || '-' }}
                                    <span v-if="selectedRecord.equipment?.name">- {{ selectedRecord.equipment.name }}</span>
                                </p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">类型</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="selectedRecord.type === 'maintenance' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                                        {{ selectedRecord.type === 'maintenance' ? '保养' : '维修' }}
                                    </span>
                                </p>
                            </div>
                            <div v-if="selectedRecord.type === 'repair' && selectedRecord.severityLevel">
                                <label class="block text-sm font-medium text-gray-700 mb-1">故障程度</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getSeverityClass(selectedRecord.severityLevel)">
                                        {{ getSeverityLabel(selectedRecord.severityLevel) }}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- 时间信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ formatDate(selectedRecord.maintenanceDate) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ selectedRecord.startTime || '-' }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ selectedRecord.endTime || '-' }}</p>
                            </div>
                        </div>

                        <!-- 操作人信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">操作人</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ selectedRecord.technician || '-' }}</p>
                            </div>
                        </div>

                        <!-- 描述信息 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">问题描述/保养内容</label>
                            <div class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[60px] modal-text-field">{{ selectedRecord.description || '-' }}</div>
                        </div>

                        <!-- 处理结果 -->
                        <div v-if="selectedRecord.result">
                            <label class="block text-sm font-medium text-gray-700 mb-1">处理结果</label>
                            <div class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[60px] modal-text-field">{{ selectedRecord.result }}</div>
                        </div>

                        <!-- 备注 -->
                        <div v-if="selectedRecord.notes">
                            <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                            <div class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[60px] modal-text-field">{{ selectedRecord.notes }}</div>
                        </div>

                        <!-- 审核信息 -->
                        <div v-if="selectedRecord.reviewer">
                            <label class="block text-sm font-medium text-gray-700 mb-1">审核人</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ selectedRecord.reviewer }}</p>
                        </div>
                    </div>
                </div>
                <!-- 模态框底部 -->
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 rounded-b-lg border-t border-gray-200">
                    <button
                        @click="closeDetail"
                        class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                    >
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知组件 -->
    <div id="toast-container"></div>

    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/equipment/maintenance.js"></script>
</body>
</html>
