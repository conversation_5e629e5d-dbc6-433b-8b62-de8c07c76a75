/**
 * 文件管理列表页面
 * 显示和管理客户二认文件列表
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import FileList from '../../../components/file-management/FileList.js';
import eventManager, { EVENTS } from '../../utils/eventManager.js';

createStandardApp({
    components: {
        Sidebar,
        FileList
    },
    setup() {
        const { ref, onMounted } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        onMounted(() => {
            // 检查URL参数，如果有刷新标志则强制刷新文件列表
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('refresh') === 'true') {
                const action = urlParams.get('action');
                console.log('检测到页面刷新标志，强制刷新文件列表', { action });

                // 延迟触发刷新事件，确保FileList组件已完全加载
                setTimeout(() => {
                    eventManager.emit(EVENTS.FILE_LIST_REFRESH, {
                        reason: action || 'page_refresh',
                        timestamp: new Date()
                    });
                }, 100);

                // 清除URL参数，避免重复刷新
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        });

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_view'],
    onUserLoaded: async (user) => {
        console.log('文件管理列表页面加载完成，当前用户:', user.username);
    }
}).mount('#app');
