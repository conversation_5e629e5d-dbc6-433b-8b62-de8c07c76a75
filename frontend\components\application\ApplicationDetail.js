/**
 * 申请详情组件
 * 显示申请详情和附件下载
 */

import { downloadAttachment, approveApplication, rejectApplication } from '../../scripts/api/application.js';
import { getManagers } from '../../scripts/api/user.js';
import { PRIORITIES } from '../../scripts/config.js';
import ApplicationTemplate from './ApplicationTemplate.js';

export default {
    components: {
        ApplicationTemplate
    },
    props: {
        application: Object,
        visible: Boolean,
        showApprovalActions: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close', 'approval-success'],
    setup(props, { emit }) {
        const { computed, ref } = Vue;

        // 申请书模板状态
        const showApplicationTemplate = ref(false);

        // 当前活动标签页
        const activeTab = ref('basic');

        // 审批相关状态
        const showApprovalModal = ref(false);
        const showRejectionModal = ref(false);
        const approvalComment = ref('');
        const rejectionComment = ref('');
        const needManagerApproval = ref(false);
        const needCeoApproval = ref(true);
        const selectedManagers = ref([]);
        const availableManagers = ref([]);
        const isLoadingManagers = ref(false);

        // 关闭详情
        function close() {
            emit('close');
        }

        // 切换标签页
        function switchTab(tab) {
            activeTab.value = tab;
        }

        // 下载附件
        async function handleDownload(file) {
            try {
                await downloadAttachment(file);
            } catch (error) {
                console.error('下载失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('下载失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            return PRIORITIES[priority]?.class || 'bg-gray-100';
        }

        // 获取状态样式
        function getStatusClass(status) {
            switch (status) {
                case 'pending':
                    return 'bg-yellow-100 text-yellow-800';
                case 'approved':
                    return 'bg-green-100 text-green-800';
                case 'rejected':
                    return 'bg-red-100 text-red-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return '审批中';
                case 'approved':
                    return '已通过';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        // 获取当前阶段文本
        function getStageText(stage) {
            switch (stage) {
                case 'factory_manager':
                    return '厂长审批';
                case 'director':
                    return '总监审批';
                case 'manager':
                    return '经理审批';
                case 'ceo':
                    return 'CEO审批';
                case 'completed':
                    return '已完成';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        // 获取申请类型文本
        function getTypeText(type) {
            return type === 'standard' ? '标准申请' : '其他申请';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 是否有审批历史
        const hasApprovalHistory = computed(() => {
            return props.application &&
                   props.application.approvalHistory &&
                   props.application.approvalHistory.length > 0;
        });

        // 显示申请书模板
        function viewApplicationTemplate() {
            showApplicationTemplate.value = true;
        }

        // 关闭申请书模板
        function closeApplicationTemplate() {
            showApplicationTemplate.value = false;
        }

        // 检查是否可以审批
        const canApprove = computed(() => {
            return props.showApprovalActions &&
                   props.application &&
                   props.application.status === 'pending';
        });

        // 加载经理列表
        async function loadManagers() {
            try {
                isLoadingManagers.value = true;
                const response = await getManagers();
                availableManagers.value = response.data || [];
            } catch (error) {
                console.error('加载经理列表失败:', error);
                alert('加载经理列表失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoadingManagers.value = false;
            }
        }

        // 打开审批通过模态框
        function openApprovalModal() {
            approvalComment.value = '同意';
            needManagerApproval.value = false;
            selectedManagers.value = [];
            showApprovalModal.value = true;

            // 如果是总监审批阶段，加载经理列表
            if (props.application && props.application.currentStage === 'director') {
                loadManagers();
            }
        }

        // 打开审批拒绝模态框
        function openRejectionModal() {
            rejectionComment.value = '';
            showRejectionModal.value = true;
        }

        // 关闭审批模态框
        function closeApprovalModal() {
            showApprovalModal.value = false;
            approvalComment.value = '';
            needManagerApproval.value = false;
            selectedManagers.value = [];
        }

        // 关闭拒绝模态框
        function closeRejectionModal() {
            showRejectionModal.value = false;
            rejectionComment.value = '';
        }

        // 审批通过
        async function handleApprove() {
            if (!props.application?.id) return;

            try {
                const approvalData = {
                    comment: approvalComment.value,
                    needManagerApproval: needManagerApproval.value,
                    selectedManagers: selectedManagers.value,
                    needCeoApproval: needCeoApproval.value
                };

                const result = await approveApplication(props.application.id, approvalData);
                if (result.success) {
                    alert('审批通过成功！');
                    closeApprovalModal();
                    // 更新侧边栏待审核数量
                    if (window.updateSidebarPendingCount) {
                        window.updateSidebarPendingCount();
                    }
                    emit('approval-success');
                } else {
                    alert('审批通过失败: ' + result.message);
                }
            } catch (error) {
                console.error('审批通过失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('审批通过失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 审批拒绝
        async function handleReject() {
            if (!props.application?.id) return;

            if (!rejectionComment.value.trim()) {
                alert('请输入拒绝原因');
                return;
            }

            try {
                const rejectionData = {
                    comment: rejectionComment.value
                };

                const result = await rejectApplication(props.application.id, rejectionData);
                if (result.success) {
                    alert('审批拒绝成功！');
                    closeRejectionModal();
                    // 更新侧边栏待审核数量
                    if (window.updateSidebarPendingCount) {
                        window.updateSidebarPendingCount();
                    }
                    emit('approval-success');
                } else {
                    alert('审批拒绝失败: ' + result.message);
                }
            } catch (error) {
                console.error('审批拒绝失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('审批拒绝失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        return {
            close,
            handleDownload,
            getPriorityClass,
            getStatusClass,
            getStatusText,
            getStageText,
            getTypeText,
            formatDateTime,
            hasApprovalHistory,
            showApplicationTemplate,
            viewApplicationTemplate,
            closeApplicationTemplate,
            activeTab,
            switchTab,
            // 审批相关
            canApprove,
            showApprovalModal,
            showRejectionModal,
            approvalComment,
            rejectionComment,
            needManagerApproval,
            needCeoApproval,
            selectedManagers,
            availableManagers,
            isLoadingManagers,
            openApprovalModal,
            openRejectionModal,
            closeApprovalModal,
            closeRejectionModal,
            handleApprove,
            handleReject
        };
    },
    template: `
        <div v-if="visible" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl w-full max-w-5xl max-h-[90vh] flex flex-col shadow-2xl border border-gray-100 overflow-hidden">
                <!-- 标题栏 -->
                <div class="relative bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                    <div class="px-8 py-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900">申请详情</h2>
                                    <p class="text-sm text-gray-600 mt-0.5">查看申请的详细信息和处理状态</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3">
                                <!-- 审批操作按钮 -->
                                <div v-if="canApprove" class="flex items-center space-x-2">
                                    <button @click="openApprovalModal"
                                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        审批通过
                                    </button>
                                    <button @click="openRejectionModal"
                                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        审批拒绝
                                    </button>
                                </div>

                                <!-- 关闭按钮 -->
                                <button @click="close" class="w-8 h-8 rounded-lg bg-white/80 hover:bg-white flex items-center justify-center text-gray-400 hover:text-gray-600 transition-all duration-200 shadow-sm">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="bg-white px-8 pt-6">
                    <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl">
                        <button @click="switchTab('basic')"
                                :class="['flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                        activeTab === 'basic' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50']">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>基本信息</span>
                        </button>
                        <button @click="switchTab('record')"
                                :class="['flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                        activeTab === 'record' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50']">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <span>申请记录</span>
                        </button>
                        <button @click="switchTab('attachments')"
                                :class="['flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                        activeTab === 'attachments' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50']">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            <span>附件管理</span>
                        </button>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div v-if="application" class="flex-1 overflow-y-auto">
                    <div class="px-8 py-6">
                    <!-- 基本信息标签页 -->
                    <div v-if="activeTab === 'basic'" class="space-y-6">
                        <!-- 申请概览卡片 -->
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                            <div class="flex items-start justify-between mb-6">
                                <div>
                                    <h3 class="text-lg font-bold text-gray-900">申请概览</h3>
                                    <p class="text-sm text-gray-600">申请的基本信息和当前状态</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span :class="['px-3 py-1 rounded-full text-xs font-medium', getStatusClass(application.status)]">
                                        {{ getStatusText(application.status) }}
                                    </span>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">申请编号</p>
                                            <p class="text-sm font-semibold text-gray-900">{{ application.applicationNumber || (application.id ? application.id.substring(0, 8) : '未知编号') }}</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">申请人</p>
                                            <p class="text-sm font-semibold text-gray-900">{{ application.applicant }}</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">申请部门</p>
                                            <p class="text-sm font-semibold text-gray-900">{{ application.department || '未指定' }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V8a1 1 0 011-1h3z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">申请日期</p>
                                            <p class="text-sm font-semibold text-gray-900">{{ application.date }}</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">当前阶段</p>
                                            <p class="text-sm font-semibold text-gray-900">{{ getStageText(application.currentStage) }}</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-xs text-gray-500 uppercase tracking-wide">紧急程度</p>
                                            <span :class="['inline-flex px-2 py-1 rounded-full text-xs font-medium', getPriorityClass(application.priority)]">
                                                {{ application.priority === 'normal' ? '普通' : application.priority === 'medium' ? '中等' : '紧急' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 申请内容卡片 -->
                        <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                            <div class="mb-4">
                                <h3 class="text-lg font-bold text-gray-900">申请内容</h3>
                                <p class="text-sm text-gray-600">详细的申请说明和要求</p>
                            </div>
                            <div class="bg-gray-50 rounded-xl p-4">
                                <p class="text-gray-800 leading-relaxed whitespace-pre-wrap">{{ application.content }}</p>
                            </div>
                        </div>

                        <!-- 申请金额卡片 -->
                        <div class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                            <div class="mb-6">
                                <h3 class="text-lg font-bold text-gray-900">申请金额</h3>
                                <p class="text-sm text-gray-600">本次申请涉及的资金数额</p>
                            </div>
                            <div class="text-center py-8 bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl border border-amber-100">
                                <div class="text-4xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                                    {{ application.amount ? '¥' + parseFloat(application.amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '无' }}
                                </div>
                                <p v-if="application.amount" class="text-sm text-gray-600 mt-2">人民币</p>
                            </div>
                        </div>
                    </div>

                    <!-- 申请记录标签页 -->
                    <div v-if="activeTab === 'record'" class="space-y-6">
                        <div v-if="hasApprovalHistory">
                            <!-- 审批历史标题 -->
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-bold text-gray-900">审批历史</h3>
                                    <p class="text-sm text-gray-600">申请的审批流程和处理记录</p>
                                </div>
                            </div>

                            <!-- 审批记录列表 -->
                            <div class="space-y-4">
                                <div v-for="(history, index) in application.approvalHistory" :key="index"
                                     class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex items-start space-x-4">
                                            <!-- 审批状态图标 -->
                                            <div :class="[
                                                'w-12 h-12 rounded-xl flex items-center justify-center',
                                                history.action === 'approve'
                                                    ? 'bg-gradient-to-br from-green-500 to-emerald-600'
                                                    : 'bg-gradient-to-br from-red-500 to-rose-600'
                                            ]">
                                                <svg v-if="history.action === 'approve'" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <svg v-else class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </div>

                                            <!-- 审批信息 -->
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3 mb-2">
                                                    <h4 class="text-lg font-semibold text-gray-900">{{ getStageText(history.stage) }}</h4>
                                                    <span :class="[
                                                        'px-3 py-1 rounded-full text-xs font-medium',
                                                        history.action === 'approve'
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-red-100 text-red-800'
                                                    ]">
                                                        {{ history.action === 'approve' ? '审批通过' : '审批拒绝' }}
                                                    </span>
                                                </div>

                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                        <span class="text-sm text-gray-600">审批人：</span>
                                                        <span class="text-sm font-medium text-gray-900">{{ history.approverName }}</span>
                                                    </div>

                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z"></path>
                                                        </svg>
                                                        <span class="text-sm text-gray-600">角色：</span>
                                                        <span class="text-sm font-medium text-gray-900">{{ history.approverRole }}</span>
                                                    </div>
                                                </div>

                                                <!-- 审批意见 -->
                                                <div v-if="history.comment" class="bg-gray-50 rounded-xl p-4 mb-3">
                                                    <div class="flex items-start space-x-2">
                                                        <svg class="w-4 h-4 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                        </svg>
                                                        <div>
                                                            <p class="text-xs text-gray-500 uppercase tracking-wide mb-1">审批意见</p>
                                                            <p class="text-sm text-gray-800">{{ history.comment }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 审批时间 -->
                                        <div class="text-right">
                                            <div class="flex items-center space-x-2 text-sm text-gray-500">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span>{{ formatDateTime(history.timestamp) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-else class="text-center py-16">
                            <div class="w-20 h-20 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无审批记录</h3>
                            <p class="text-gray-600">该申请还未进入审批流程或暂无审批历史</p>
                        </div>
                    </div>

                    <!-- 附件管理标签页 -->
                    <div v-if="activeTab === 'attachments'" class="space-y-6">
                        <div v-if="application.attachments && application.attachments.length > 0">
                            <!-- 附件列表标题 -->
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-bold text-gray-900">附件列表</h3>
                                    <p class="text-sm text-gray-600">申请相关的文档和资料</p>
                                </div>
                            </div>

                            <!-- 附件网格 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div v-for="file in application.attachments" :key="file.id"
                                     class="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 group">
                                    <div class="flex items-start justify-between">
                                        <div class="flex items-start space-x-4 flex-1">
                                            <!-- 文件类型图标 -->
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </div>

                                            <!-- 文件信息 -->
                                            <div class="flex-1 min-w-0">
                                                <h4 class="text-sm font-semibold text-gray-900 truncate mb-1">
                                                    {{ file.originalName || file.name }}
                                                </h4>
                                                <div class="space-y-1">
                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <span class="text-xs text-gray-500">
                                                            {{ file.size ? (file.size / 1024).toFixed(1) + ' KB' : '未知大小' }}
                                                        </span>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        <span class="text-xs text-gray-500">
                                                            {{ formatDateTime(file.uploadedAt) || '上传时间未知' }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 下载按钮 -->
                                        <button @click="handleDownload(file)"
                                                class="ml-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
                                            <div class="flex items-center space-x-2">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span>下载</span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-else class="text-center py-16">
                            <div class="w-20 h-20 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无附件</h3>
                            <p class="text-gray-600">该申请暂未上传任何附件文档</p>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- 底部按钮区域 -->
                <div class="border-t border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
                    <div class="px-8 py-6">
                        <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span>申请编号：{{ application.applicationNumber || (application.id ? application.id.substring(0, 8) : '未知编号') }}</span>
                        </div>
                        <button @click="viewApplicationTemplate"
                                class="px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-medium rounded-xl hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl">
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>查看申请书</span>
                            </div>
                        </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请书模板组件 -->
        <application-template
            :application="application"
            :visible="showApplicationTemplate"
            @close="closeApplicationTemplate">
        </application-template>

        <!-- 审批通过模态框 -->
        <div v-if="showApprovalModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">审批通过</h3>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="approvalComment">
                        审批意见
                    </label>
                    <textarea
                        id="approvalComment"
                        v-model="approvalComment"
                        class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                        rows="4"
                        placeholder="请输入审批意见...">
                    </textarea>
                </div>

                <!-- 总监审批阶段的额外选项 -->
                <div v-if="application && application.currentStage === 'director'" class="mb-4">
                    <div class="mb-3">
                        <label class="flex items-center">
                            <input type="checkbox" v-model="needManagerApproval" class="mr-2">
                            <span class="text-sm text-gray-700">需要经理审批</span>
                        </label>
                    </div>

                    <!-- 经理选择 -->
                    <div v-if="needManagerApproval" class="mb-3">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            选择经理 <span class="text-red-500">*</span>
                        </label>
                        <div v-if="isLoadingManagers" class="text-gray-500 text-sm">加载中...</div>
                        <div v-else class="space-y-2 max-h-32 overflow-y-auto">
                            <label v-for="manager in availableManagers" :key="manager.id" class="flex items-center">
                                <input type="checkbox" :value="manager" v-model="selectedManagers" class="mr-2">
                                <span class="text-sm">{{ manager.username }} ({{ manager.department }})</span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="flex items-center">
                            <input type="checkbox" v-model="needCeoApproval" class="mr-2">
                            <span class="text-sm text-gray-700">需要CEO审批</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button @click="closeApprovalModal"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="handleApprove"
                            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                        确认通过
                    </button>
                </div>
            </div>
        </div>

        <!-- 审批拒绝模态框 -->
        <div v-if="showRejectionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">审批拒绝</h3>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="rejectionComment">
                        拒绝原因 <span class="text-red-500">*</span>
                    </label>
                    <textarea
                        id="rejectionComment"
                        v-model="rejectionComment"
                        class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                        rows="4"
                        placeholder="请输入拒绝原因...">
                    </textarea>
                </div>

                <div class="flex justify-end space-x-2">
                    <button @click="closeRejectionModal"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="handleReject"
                            class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                        确认拒绝
                    </button>
                </div>
            </div>
        </div>
    `
};
