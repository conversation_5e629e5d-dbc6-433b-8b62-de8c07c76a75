/**
 * PostgreSQL产品数据访问层
 * 替换SQLite的productRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const { ProductModel, ProductionProcessModel } = require('../models/productModel');
const logger = require('../utils/logger');

class PostgresProductRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL产品数据访问层初始化完成');
        }
    }

    /**
     * 创建产品
     */
    async create(product) {
        try {
            const dbData = product.toDatabase();

            const result = await this.query(`
                INSERT INTO products (
                    id, code, name, category, specifications, unit, standard_time, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            `, [
                dbData.id, dbData.code, dbData.name, dbData.category,
                dbData.specifications, dbData.unit, dbData.standard_time,
                dbData.created_at, dbData.updated_at
            ]);

            logger.info('产品创建成功', { productId: product.id });
            return ProductModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('产品创建失败', { error: error.message, productId: product.id });
            throw error;
        }
    }

    /**
     * 根据ID获取产品
     */
    async findById(id) {
        try {
            const row = await this.findOne('SELECT * FROM products WHERE id = $1', [id]);
            return row ? ProductModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据ID获取产品失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 根据产品编码获取产品
     */
    async findByCode(code) {
        try {
            const row = await this.findOne('SELECT * FROM products WHERE code = $1', [code]);
            return row ? ProductModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据编码获取产品失败', { error: error.message, code });
            throw error;
        }
    }

    /**
     * 获取所有产品
     */
    async findAll(options = {}) {
        try {
            let query = `
                SELECT * FROM products
                ORDER BY created_at DESC
            `;

            // 如果指定了限制数量
            if (options.limit) {
                query += ` LIMIT ${parseInt(options.limit)}`;
            }

            const rows = await this.findMany(query);
            return rows.map(row => ProductModel.fromDatabase(row));
        } catch (error) {
            logger.error('获取所有产品失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 根据分类获取产品
     */
    async findByCategory(category) {
        try {
            const rows = await this.findMany('SELECT * FROM products WHERE category = $1', [category]);
            return rows.map(row => ProductModel.fromDatabase(row));
        } catch (error) {
            logger.error('根据分类获取产品失败', { error: error.message, category });
            throw error;
        }
    }

    /**
     * 更新产品
     */
    async update(id, product) {
        try {
            const dbData = product.toDatabase();

            const result = await this.query(`
                UPDATE products SET
                    code = $1, name = $2, category = $3, specifications = $4,
                    unit = $5, standard_time = $6, updated_at = $7
                WHERE id = $8
                RETURNING *
            `, [
                dbData.code, dbData.name, dbData.category, dbData.specifications,
                dbData.unit, dbData.standard_time, dbData.updated_at, id
            ]);

            if (result.rows.length > 0) {
                logger.info('产品更新成功', { productId: id });
                return ProductModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('产品更新失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 删除产品
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM products WHERE id = $1', [id]);
            const success = result.rowCount > 0;
            
            if (success) {
                logger.info('产品删除成功', { productId: id });
            }
            return success;
        } catch (error) {
            logger.error('产品删除失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 检查产品编码是否存在
     */
    async checkCodeExists(code, excludeId = null) {
        try {
            let query = 'SELECT COUNT(*) as count FROM products WHERE code = $1';
            let params = [code];
            
            if (excludeId) {
                query += ' AND id != $2';
                params.push(excludeId);
            }

            const result = await this.findOne(query, params);
            return result ? parseInt(result.count) > 0 : false;
        } catch (error) {
            logger.error('检查产品编码是否存在失败', { error: error.message, code });
            throw error;
        }
    }

    // 生产工艺相关方法

    /**
     * 创建生产工艺
     */
    async createProcess(process) {
        try {
            const dbData = process.toDatabase();

            const result = await this.query(`
                INSERT INTO production_processes (
                    id, product_id, step_number, step_name, description,
                    equipment_required, estimated_time, quality_requirements,
                    created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING *
            `, [
                dbData.id, dbData.product_id, dbData.step_number, dbData.step_name,
                dbData.description, dbData.equipment_required, dbData.estimated_time,
                dbData.quality_requirements, dbData.created_at, dbData.updated_at
            ]);

            logger.info('生产工艺创建成功', { processId: process.id });
            return ProductionProcessModel.fromDatabase(result.rows[0]);
        } catch (error) {
            logger.error('生产工艺创建失败', { error: error.message, processId: process.id });
            throw error;
        }
    }

    /**
     * 根据产品ID获取生产工艺
     */
    async findProcessesByProductId(productId) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM production_processes 
                WHERE product_id = $1 
                ORDER BY step_number ASC
            `, [productId]);
            return rows.map(row => ProductionProcessModel.fromDatabase(row));
        } catch (error) {
            logger.error('根据产品ID获取生产工艺失败', { error: error.message, productId });
            throw error;
        }
    }

    /**
     * 更新生产工艺
     */
    async updateProcess(id, process) {
        try {
            const dbData = process.toDatabase();

            const result = await this.query(`
                UPDATE production_processes SET
                    step_number = $1, step_name = $2, description = $3,
                    equipment_required = $4, estimated_time = $5,
                    quality_requirements = $6, updated_at = $7
                WHERE id = $8
                RETURNING *
            `, [
                dbData.step_number, dbData.step_name, dbData.description,
                dbData.equipment_required, dbData.estimated_time,
                dbData.quality_requirements, dbData.updated_at, id
            ]);

            if (result.rows.length > 0) {
                logger.info('生产工艺更新成功', { processId: id });
                return ProductionProcessModel.fromDatabase(result.rows[0]);
            }
            return null;
        } catch (error) {
            logger.error('生产工艺更新失败', { error: error.message, processId: id });
            throw error;
        }
    }

    /**
     * 删除产品的所有生产工艺
     */
    async deleteProcessesByProductId(productId) {
        try {
            const result = await this.query('DELETE FROM production_processes WHERE product_id = $1', [productId]);
            logger.info('产品生产工艺删除成功', { productId, deletedCount: result.rowCount });
            return result.rowCount;
        } catch (error) {
            logger.error('删除产品生产工艺失败', { error: error.message, productId });
            throw error;
        }
    }

    /**
     * 获取产品统计信息
     */
    async getProductStats() {
        try {
            const stats = await this.findOne(`
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT category) as category_count
                FROM products
            `);
            
            return {
                totalCount: parseInt(stats.total_count) || 0,
                categoryCount: parseInt(stats.category_count) || 0
            };
        } catch (error) {
            logger.error('获取产品统计信息失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 搜索产品
     */
    async searchProducts(keyword) {
        try {
            const rows = await this.findMany(`
                SELECT * FROM products 
                WHERE name ILIKE $1 OR code ILIKE $1 OR category ILIKE $1
                ORDER BY created_at DESC
            `, [`%${keyword}%`]);
            return rows.map(row => ProductModel.fromDatabase(row));
        } catch (error) {
            logger.error('搜索产品失败', { error: error.message, keyword });
            throw error;
        }
    }
}

module.exports = PostgresProductRepository;
