/**
 * 待审核申请列表组件
 * 显示待审核的申请
 */

import { PRIORITIES } from '../../scripts/config.js';
import { getPendingApplications, approveApplication, rejectApplication } from '../../scripts/api/application.js';
import { getManagers } from '../../scripts/api/user.js';
import UnifiedPagination from '../common/UnifiedPagination.js';
import ContentViewModal from '../common/ContentViewModal.js';

export default {
    components: {
        UnifiedPagination,
        ContentViewModal
    },
    emits: ['view-detail'],
    setup(props, { emit }) {
        const { ref, computed, onMounted } = Vue;

        // 申请列表
        const applications = ref([]);
        const isLoading = ref(true);

        // 内容查看弹出框
        const showContentModal = ref(false);
        const contentModalData = ref({
            title: '',
            content: ''
        });

        // 分页相关
        const currentPage = ref(1);
        const itemsPerPage = ref(10);

        // 审批相关
        const showApprovalModal = ref(false);
        const showRejectionModal = ref(false);
        const currentAppId = ref(null);
        const approvalComment = ref('');
        const needManagerApproval = ref(false);
        const rejectionComment = ref('');

        // 经理选择相关
        const selectedManagers = ref([]);
        const availableManagers = ref([]);
        const isLoadingManagers = ref(false);

        // 总监审批相关
        const needCeoApproval = ref(true);

        // 分页后的申请列表（最终显示的数据）
        const filteredApplications = computed(() => {
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return applications.value.slice(start, end);
        });

        // 总记录数（用于分页组件）
        const totalItems = computed(() => {
            return applications.value.length;
        });



        // 初始化
        onMounted(() => {
            loadApplications();
        });

        // 加载待审核申请列表
        async function loadApplications() {
            try {
                isLoading.value = true;
                applications.value = await getPendingApplications();
            } catch (error) {
                console.error('加载待审核申请列表失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('加载待审核申请列表失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isLoading.value = false;
            }
        }

        // 查看详情
        function viewDetail(id) {
            emit('view-detail', id);
        }

        // 分页处理
        function handlePageChange(page) {
            currentPage.value = page;
        }

        // 加载经理列表
        async function loadManagers() {
            try {
                isLoadingManagers.value = true;
                const response = await getManagers();
                availableManagers.value = response.managers || [];
            } catch (error) {
                console.error('加载经理列表失败:', error);
                alert('加载经理列表失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoadingManagers.value = false;
            }
        }

        // 查看申请内容
        function viewContent(application) {
            contentModalData.value = {
                title: `申请内容 - ${application.applicant}`,
                content: application.content || '暂无申请内容'
            };
            showContentModal.value = true;
        }

        // 关闭内容查看弹出框
        function closeContentModal() {
            showContentModal.value = false;
        }

        // 打开审批通过模态框
        function openApproveModal(id) {
            currentAppId.value = id;
            approvalComment.value = '同意';
            needManagerApproval.value = false;
            selectedManagers.value = [];
            showApprovalModal.value = true;

            // 如果是总监审批阶段，加载经理列表
            const application = filteredApplications.value.find(app => app.id === id);
            if (application && application.currentStage === 'director') {
                loadManagers();
            }
        }

        // 打开审批拒绝模态框
        function openRejectModal(id) {
            currentAppId.value = id;
            rejectionComment.value = '';
            showRejectionModal.value = true;
        }

        // 经理选择相关函数
        function toggleManagerSelection(manager) {
            const index = selectedManagers.value.findIndex(m => m.id === manager.id);
            if (index > -1) {
                selectedManagers.value.splice(index, 1);
            } else {
                selectedManagers.value.push({
                    id: manager.id,
                    name: manager.name,
                    role: manager.role
                });
            }
        }

        function isManagerSelected(managerId) {
            return selectedManagers.value.some(m => m.id === managerId);
        }

        // 关闭审批模态框
        function closeApprovalModal() {
            showApprovalModal.value = false;
            currentAppId.value = null;
            approvalComment.value = '';
            needManagerApproval.value = false;
            needCeoApproval.value = true;
            selectedManagers.value = [];
        }

        // 关闭拒绝模态框
        function closeRejectionModal() {
            showRejectionModal.value = false;
            currentAppId.value = null;
        }

        // 审批通过
        async function handleApprove() {
            if (!currentAppId.value) return;

            try {
                const approvalData = {
                    comment: approvalComment.value,
                    needManagerApproval: needManagerApproval.value,
                    selectedManagers: selectedManagers.value,
                    needCeoApproval: needCeoApproval.value
                };

                const result = await approveApplication(currentAppId.value, approvalData);
                if (result.success) {
                    alert('审批通过成功！');
                    closeApprovalModal();
                    // 更新侧边栏待审核数量
                    if (window.updateSidebarPendingCount) {
                        window.updateSidebarPendingCount();
                    }
                    await loadApplications();
                } else {
                    alert('审批通过失败: ' + result.message);
                }
            } catch (error) {
                console.error('审批通过失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('审批通过失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 审批拒绝
        async function handleReject() {
            if (!currentAppId.value) return;

            try {
                const rejectionData = {
                    comment: rejectionComment.value
                };

                const result = await rejectApplication(currentAppId.value, rejectionData);
                if (result.success) {
                    alert('审批拒绝成功！');
                    closeRejectionModal();
                    // 更新侧边栏待审核数量
                    if (window.updateSidebarPendingCount) {
                        window.updateSidebarPendingCount();
                    }
                    await loadApplications();
                } else {
                    alert('审批拒绝失败: ' + result.message);
                }
            } catch (error) {
                console.error('审批拒绝失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    localStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('审批拒绝失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            return PRIORITIES[priority]?.class || 'bg-gray-100';
        }

        // 获取当前阶段文本
        function getStageText(stage) {
            switch (stage) {
                case 'factory_manager':
                    return '厂长审批';
                case 'director':
                    return '总监审批';
                case 'manager':
                    return '经理审批';
                case 'ceo':
                    return 'CEO审批';
                case 'completed':
                    return '已完成';
                case 'rejected':
                    return '已拒绝';
                default:
                    return '未知';
            }
        }

        // 获取申请类型文本
        function getTypeText(type) {
            return type === 'standard' ? '标准申请' : '其他申请';
        }

        return {
            applications,
            filteredApplications,
            isLoading,
            // 分页相关
            currentPage,
            itemsPerPage,
            totalItems,
            handlePageChange,
            viewDetail,
            viewContent,
            closeContentModal,
            showContentModal,
            contentModalData,
            openApproveModal,
            openRejectModal,
            closeApprovalModal,
            closeRejectionModal,
            handleApprove,
            handleReject,
            getPriorityClass,
            getStageText,
            getTypeText,
            showApprovalModal,
            showRejectionModal,
            currentAppId,
            approvalComment,
            needManagerApproval,
            rejectionComment,
            // 经理选择相关
            selectedManagers,
            availableManagers,
            isLoadingManagers,
            toggleManagerSelection,
            isManagerSelected,
            // 总监审批相关
            needCeoApproval
        };
    },
    template: `
        <div>

            <div v-if="isLoading" class="text-center py-8">
                <div class="inline-block w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                <p class="mt-2 text-gray-500">加载中...</p>
            </div>

            <div v-else-if="filteredApplications.length === 0" class="text-center py-8">
                <p class="text-gray-500">暂无待审核申请</p>
            </div>

            <div v-else class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-50 text-gray-600 uppercase text-xs">
                            <th class="py-3 px-4 text-left">申请编号</th>
                            <th class="py-3 px-4 text-left">申请人</th>
                            <th class="py-3 px-4 text-left">部门</th>
                            <th class="py-3 px-4 text-left">日期</th>
                            <th class="py-3 px-4 text-left">紧急程度</th>
                            <th class="py-3 px-4 text-left">申请内容</th>
                            <th class="py-3 px-4 text-left">申请类型</th>
                            <th class="py-3 px-4 text-left">当前阶段</th>
                            <th class="py-3 px-4 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-600">
                        <tr v-for="app in filteredApplications" :key="app.id" class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">{{ app.applicationNumber || (app.id ? app.id.substring(0, 8) : '未知编号') }}</td>
                            <td class="py-3 px-4">{{ app.applicant }}</td>
                            <td class="py-3 px-4">{{ app.department }}</td>
                            <td class="py-3 px-4">{{ app.date }}</td>
                            <td class="py-3 px-4">
                                <span :class="['px-2 py-1 rounded-full text-xs', getPriorityClass(app.priority)]">
                                    {{ app.priority === 'normal' ? '普通' : app.priority === 'medium' ? '中等' : '紧急' }}
                                </span>
                            </td>
                            <!-- 申请内容 -->
                            <td class="py-3 px-4">
                                <div class="max-w-xs">
                                    <button @click="viewContent(app)"
                                            class="text-left w-full truncate text-gray-900 hover:text-gray-700 focus:outline-none cursor-pointer transition-colors duration-200"
                                            :title="app.content ? '点击查看完整内容' : '暂无内容'">
                                        {{ app.content ? (app.content.length > 30 ? app.content.substring(0, 30) + '...' : app.content) : '-' }}
                                    </button>
                                </div>
                            </td>
                            <td class="py-3 px-4">{{ getTypeText(app.type) }}</td>
                            <td class="py-3 px-4">
                                <div>
                                    <div>{{ getStageText(app.currentStage) }}</div>
                                    <div v-if="app.pendingApprovers && app.pendingApprovers.length > 0" class="text-xs mt-1">
                                        <span class="text-gray-500">待审批: </span>
                                        <span class="text-blue-600 font-medium">{{ app.pendingApprovers.map(approver => approver.name).join(', ') }}</span>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <!-- 审批申请按钮 -->
                                    <button @click="viewDetail(app.id)"
                                            class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300 transition-all duration-200"
                                            title="审批申请">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        审批申请
                                    </button>

                                    <!-- 审批通过按钮 -->
                                    <button @click="openApproveModal(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-emerald-50 text-emerald-600 hover:bg-emerald-100 hover:text-emerald-700 transition-all duration-200 group"
                                            title="审批通过">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </button>

                                    <!-- 审批拒绝按钮 -->
                                    <button @click="openRejectModal(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 transition-all duration-200 group"
                                            title="审批拒绝">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页组件 -->
                <unified-pagination
                    :current-page="currentPage"
                    :total-items="totalItems"
                    :items-per-page="itemsPerPage"
                    @page-change="handlePageChange">
                </unified-pagination>
            </div>

            <!-- 审批通过模态框 -->
            <div v-if="showApprovalModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">审批通过</h3>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="approvalComment">
                            审批意见
                        </label>
                        <textarea
                            id="approvalComment"
                            v-model="approvalComment"
                            class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                            rows="4"
                            placeholder="请输入审批意见...">
                        </textarea>
                    </div>

                    <!-- 总监审批时显示经理选择选项 -->
                    <div v-if="filteredApplications.find(app => app.id === currentAppId)?.currentStage === 'director'" class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            选择需要审批的经理（可选）
                        </label>

                        <div v-if="isLoadingManagers" class="text-center py-4">
                            <span class="text-gray-500">加载经理列表中...</span>
                        </div>

                        <div v-else-if="availableManagers.length === 0" class="text-center py-4">
                            <span class="text-gray-500">暂无可选经理</span>
                        </div>

                        <div v-else class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
                            <label v-for="manager in availableManagers" :key="manager.id"
                                   class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                <input
                                    type="checkbox"
                                    :checked="isManagerSelected(manager.id)"
                                    @change="toggleManagerSelection(manager)"
                                    class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">{{ manager.name }}</div>
                                    <div class="text-xs text-gray-500">{{ manager.role }}</div>
                                </div>
                            </label>
                        </div>

                        <div v-if="selectedManagers.length > 0" class="mt-2 text-sm text-gray-600">
                            已选择 {{ selectedManagers.length }} 位经理
                        </div>
                    </div>

                    <!-- 总监审批时显示是否需要CEO审批的选项（仅其他申请） -->
                    <div v-if="filteredApplications.find(app => app.id === currentAppId)?.currentStage === 'director' &&
                              filteredApplications.find(app => app.id === currentAppId)?.type !== 'standard'" class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            后续审批选择
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" v-model="needCeoApproval" :value="false" name="ceoApproval" class="mr-2">
                                <span class="text-gray-700">直接完成申请（无需CEO审批）</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="needCeoApproval" :value="true" name="ceoApproval" class="mr-2">
                                <span class="text-gray-700">提交给CEO审批</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button
                            @click="closeApprovalModal"
                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none">
                            取消
                        </button>
                        <button
                            @click="handleApprove"
                            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none">
                            确认通过
                        </button>
                    </div>
                </div>
            </div>

            <!-- 审批拒绝模态框 -->
            <div v-if="showRejectionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">审批拒绝</h3>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="rejectionComment">
                            拒绝原因 <span class="text-red-500">*</span>
                        </label>
                        <textarea
                            id="rejectionComment"
                            v-model="rejectionComment"
                            class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                            rows="4"
                            placeholder="请输入拒绝原因...">
                        </textarea>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button
                            @click="closeRejectionModal"
                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none">
                            取消
                        </button>
                        <button
                            @click="handleReject"
                            :disabled="!rejectionComment"
                            :class="[
                                'px-4 py-2 rounded-md focus:outline-none',
                                rejectionComment ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-red-300 text-white cursor-not-allowed'
                            ]">
                            确认拒绝
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容查看弹出框 -->
            <content-view-modal
                :visible="showContentModal"
                :title="contentModalData.title"
                :content="contentModalData.content"
                @close="closeContentModal"
            />
        </div>
    `
};
