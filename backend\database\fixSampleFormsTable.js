/**
 * 修复sample_forms表结构
 * 添加缺失的additional_notes字段
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');

class SampleFormsTableFixer {
    constructor() {
        // 创建PostgreSQL连接池
        this.pool = new Pool({
            host: process.env.POSTGRES_HOST || 'localhost',
            port: process.env.POSTGRES_PORT || 5432,
            user: process.env.POSTGRES_USER || 'postgres',
            password: process.env.POSTGRES_PASSWORD,
            database: process.env.POSTGRES_DATABASE || 'makrite_system',
            max: 5,
            min: 1,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
    }

    /**
     * 修复sample_forms表结构
     */
    async fixSampleFormsTable() {
        try {
            console.log('🔧 开始修复sample_forms表结构...');

            // 检查additional_notes字段是否存在
            const checkResult = await this.pool.query(`
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'sample_forms' 
                AND column_name = 'additional_notes'
            `);

            if (checkResult.rows.length === 0) {
                // 字段不存在，添加它
                await this.pool.query(`
                    ALTER TABLE sample_forms
                    ADD COLUMN additional_notes TEXT
                `);
                console.log('✅ 已添加sample_forms表的additional_notes字段');
            } else {
                console.log('✅ sample_forms表additional_notes字段已存在');
            }

            // 验证表结构
            const columnsResult = await this.pool.query(`
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'sample_forms'
                ORDER BY ordinal_position
            `);

            console.log('📋 sample_forms表当前结构:');
            columnsResult.rows.forEach(row => {
                console.log(`  - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
            });

            console.log('✅ sample_forms表结构修复完成');

        } catch (error) {
            console.error('❌ 修复sample_forms表结构失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        await this.pool.end();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const fixer = new SampleFormsTableFixer();
    
    fixer.fixSampleFormsTable()
        .then(() => {
            console.log('🎉 数据库修复完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 数据库修复失败:', error);
            process.exit(1);
        })
        .finally(() => {
            fixer.close();
        });
}

module.exports = SampleFormsTableFixer;
