/**
 * 文件管理路由
 * 定义文件管理相关的API路由
 */

const express = require('express');
const router = express.Router();
const fileManagementController = require('../controllers/fileManagementController');
const userController = require('../controllers/userController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const { fileManagementUpload, handleUploadError } = require('../middlewares/fileManagementUpload');
const { sampleUpload, handleSampleUploadError } = require('../middlewares/sampleUpload');

// 获取用户列表（用于邮件通知选择）
router.get('/users',
    authenticateJWT,
    checkPermission('file_upload'), // 需要上传权限才能选择用户
    userController.getUsersForNotification
);

// 客户管理路由
router.get('/customers',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getAllCustomers
);

router.get('/customers/active',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getActiveCustomers
);

router.post('/customers',
    authenticateJWT,
    checkPermission('file_upload'),
    fileManagementController.createCustomer
);

router.put('/customers/:id',
    authenticateJWT,
    checkPermission('file_upload'), // 使用file_upload权限，与创建客户保持一致
    fileManagementController.updateCustomer
);

router.patch('/customers/:id/toggle-status',
    authenticateJWT,
    checkPermission('file_upload'), // 使用file_upload权限，与创建客户保持一致
    fileManagementController.toggleCustomerStatus
);

router.delete('/customers/:id',
    authenticateJWT,
    checkPermission('file_upload'), // 使用file_upload权限，与创建客户保持一致
    fileManagementController.deleteCustomer
);

router.get('/historical-batches',
    authenticateJWT,
    checkPermission('file_upload'), // 使用file_upload权限
    fileManagementController.getHistoricalBatches
);

router.get('/customers/:customerId/products',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getCustomerProducts
);

// 文件记录管理路由
router.get('/files',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getAllFiles
);

router.get('/files/search',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.searchFiles
);

router.get('/files/statistics',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getFileStatistics
);

router.get('/files/:id',
    authenticateJWT,
    checkPermission('file_view'),
    fileManagementController.getFileById
);

// 创建文件记录（支持文件上传）
router.post('/files',
    authenticateJWT,
    checkPermission('file_upload'),
    fileManagementController.preprocessCustomerInfo, // 预处理客户信息
    fileManagementUpload.array('files', 10), // 最多上传10个文件
    handleUploadError,
    fileManagementController.createFileRecord
);

// 删除文件记录
router.delete('/files/:id',
    authenticateJWT,
    checkPermission('file_manage'), // 需要管理权限才能删除
    fileManagementController.deleteFileRecord
);

// 下载文件
router.get('/files/:fileId/attachments/:attachmentId/download',
    authenticateJWT,
    checkPermission('file_download'),
    fileManagementController.downloadFile
);

// 通知管理路由
router.get('/notifications',
    authenticateJWT,
    checkPermission('file_confirm'),
    fileManagementController.getUserNotifications
);

router.post('/notifications/:id/confirm',
    authenticateJWT,
    checkPermission('file_confirm'),
    fileManagementController.confirmNotification
);

// 样品单管理路由
// 获取下一个样品单编号
router.get('/sample/next-number',
    authenticateJWT,
    checkPermission('file_upload'),
    fileManagementController.getNextSampleNumber
);

// 调试中间件
const debugMiddleware = (req, res, next) => {
    console.log('样品单请求到达路由:', {
        method: req.method,
        url: req.url,
        contentType: req.headers['content-type'],
        contentLength: req.headers['content-length'],
        hasFiles: !!req.files,
        bodyKeys: Object.keys(req.body || {}),
        filesKeys: Object.keys(req.files || {})
    });
    next();
};

router.post('/sample',
    authenticateJWT,
    checkPermission('file_upload'),
    debugMiddleware,
    sampleUpload.fields([
        { name: 'maskPrintFiles', maxCount: 10 },
        { name: 'valvePrintFiles', maxCount: 10 },
        { name: 'colorBoxFiles', maxCount: 10 },
        { name: 'blisterPackFiles', maxCount: 10 }
    ]),
    handleSampleUploadError,
    fileManagementController.submitSampleForm
);

// 样品单附件下载路由
router.get('/sample/:sampleId/attachments/:attachmentId/download',
    authenticateJWT,
    checkPermission('file_download'),
    fileManagementController.downloadSampleAttachment
);

module.exports = router;
